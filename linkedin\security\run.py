import sys
import os

# Add the 'src' directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from manager import main as manager_main, force_decrypt_files, load_config, generate_and_save_keys, show_setup_status, prepare_recipient_package
from tools.generate_salt import generate_salt
import utils

def main():
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    # Automatically prepare recipient package before any menu or command
    logger = utils.setup_logger()
    try:
        prepare_recipient_package(logger, project_root)
    except Exception as e:
        logger.warning(f"[RECIPIENT] Could not prepare recipient package: {e}")
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == 'generate-salt':
            generate_salt()
        elif command == 'generate-keys':
            logger = utils.setup_logger()
            config = load_config(logger, project_root)
            if not config:
                logger.critical("Could not load config.ini. Aborting.")
                return
            generate_and_save_keys(logger, project_root, config)
        elif command == 'status':
            logger = utils.setup_logger()
            config = load_config(logger, project_root)
            if config:
                show_setup_status(logger, project_root, config)
        elif command == 'force-decrypt':
            logger = utils.setup_logger()
            logger.warning("--- RECOVERY MODE: FORCE DECRYPT ---")
            
            config = load_config(logger, project_root)
            if not config:
                logger.critical("Could not load config.ini. Aborting.")
                return

            salt_path = os.path.join(project_root, 'security', 'salt.bin')
            if not os.path.exists(salt_path):
                logger.critical(f"Salt file not found at {salt_path}. Cannot proceed.")
                return
            
            with open(salt_path, 'rb') as f:
                salt = f.read()

            password = utils.get_password(prompt="Enter the password used for the failed encryption: ", confirm=True)
            if not password:
                logger.error("No password provided. Aborting.")
                return
            
            force_decrypt_files(password, salt, logger, project_root, config)
        elif command == 'cleanup':
            logger = utils.setup_logger()
            logger.info("--- CLEANUP AND START NEW ---")
            # It's better to ask for confirmation before deleting files
            confirm = input("This will delete security files and logs. Are you sure? (y/n): ")
            if confirm.lower() == 'y':
                from manager import cleanup_and_start_new
                cleanup_and_start_new(logger, project_root)
            else:
                logger.info("Cleanup aborted by user.")
        else:
            print(f"Unknown command: {command}")
            print("Usage: python run.py [status | generate-salt | generate-keys | force-decrypt | cleanup]")
    else:
        manager_main(project_root)

if __name__ == "__main__":
    main()
