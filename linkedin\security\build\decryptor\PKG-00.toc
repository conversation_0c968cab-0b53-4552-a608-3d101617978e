('C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\decryptor.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('decryptor',
   'C:\\Users\\<USER>\\linkedin\\security\\decryptor.py',
   'PYSOURCE'),
  ('python39.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python39.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\intelpython3\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\intelpython3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\linkedin\\security\\build\\decryptor\\base_library.zip',
   'DATA')],
 'python39.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
