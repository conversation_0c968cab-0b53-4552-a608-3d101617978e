@echo off
setlocal

REM Build script for the Standalone Decryptor
REM This script uses PyArmor to obfuscate the code and Py<PERSON>nstalle<PERSON> to create a single executable.

REM Ensure the script is run from the 'security' directory
pushd "%~dp0"

echo.
echo [INFO] Preparing to build the standalone decryptor...

REM Define paths
set SCRIPT_TO_PACK=decryptor.py
set DIST_DIR=dist
set BUILD_DIR=build
set OBFUSCATED_DIR=pyarmor_obfuscated

REM Check for required tools
where pyarmor >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] PyArmor not found. Please install it: pip install pyarmor
    goto :eof
)

where pyinstaller >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller not found. Please install it: pip install pyinstaller
    goto :eof
)

echo.
echo [STEP 1/4] Cleaning up previous builds...
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
if exist "%OBFUSCATED_DIR%" rmdir /s /q "%OBFUSCATED_DIR%"
del "*.spec" >nul 2>nul

echo.
echo [STEP 2/4] Obfuscating the script with PyArmor...
REM Using --platform windows to ensure it builds for windows.
REM The --gen-key option creates a new wrapping key for this obfuscation session.
pyarmor obfuscate --platform windows --output "%OBFUSCATED_DIR%" "%SCRIPT_TO_PACK%"
if %errorlevel% neq 0 (
    echo [ERROR] PyArmor obfuscation failed.
    goto :eof
)

echo.
echo [STEP 3/4] Building the executable with PyInstaller...
REM We point PyInstaller to the obfuscated script.
REM --onefile: Create a single executable.
REM --noconsole: The application will not have a command window in the background.
REM --name: The name of the final executable.
cd %OBFUSCATED_DIR%
pyinstaller --onefile --noconsole --name decryptor "%SCRIPT_TO_PACK%"
if %errorlevel% neq 0 (
    echo [ERROR] PyInstaller build failed.
    cd ..
    goto :eof
)
cd ..

echo.
echo [STEP 4/4] Finalizing and cleaning up...

REM Move the final executable to the local dist folder
if not exist "%DIST_DIR%" mkdir "%DIST_DIR%"
move "%OBFUSCATED_DIR%\dist\decryptor.exe" "%DIST_DIR%\decryptor.exe"

REM Clean up build artifacts
rmdir /s /q "%BUILD_DIR%"
rmdir /s /q "%OBFUSCATED_DIR%"
del "*.spec" >nul 2>nul

echo.
echo ===================================================================
echo. 
echo [SUCCESS] Standalone decryptor has been built!
echo.
echo   - Location: %~dp0%DIST_DIR%\decryptor.exe
echo.
echo   Instructions for Sharing:
echo   1. Provide the recipient with the encrypted project folder.
echo   2. Give them the 'decryptor.exe' file.
echo   3. They run the executable and point it to the project folder.
echo.
echo ===================================================================

popd
endlocal
