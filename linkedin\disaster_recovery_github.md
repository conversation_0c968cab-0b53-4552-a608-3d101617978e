# Disaster Recovery GitHub Commands

If you accidentally delete or lose your project data in a Git repository, follow these steps to recover your data:

1. **Check the Git reflog for lost commits:**
   ```powershell
   git reflog
   ```
   - Find the commit hash before the deletion or data loss.

2. **Check for dangling commits (lost data):**
   ```powershell
   git fsck --lost-found
   ```
   - Look for lines like `dangling commit <commit_hash>`.

3. **Reset your working directory (if needed):**
   ```powershell
   git reset --hard
   ```

4. **Remove untracked files that block recovery:**
   ```powershell
   git clean -fd
   ```
   - WARNING: This deletes all untracked files and directories.

5. **Checkout the lost commit:**
   ```powershell
   git checkout <commit_hash>
   ```
   - Replace `<commit_hash>` with the hash from step 1 or 2.

6. **Create a new branch from the recovered state:**
   ```powershell
   git switch -c recovered-data
   ```

7. **Add any untracked folders (like 'Linkedin') if needed:**
   ```powershell
   git add Linkedin
   git commit -m "Restore full project data including Linkedin folder"
   ```

8. **Push the recovered branch to remote (optional):**
   ```powershell
   git push -u origin recovered-data
   ```

---

**Tips:**
- Always check `git status` before running destructive commands.
- If you have important untracked files, back them up before running `git clean -fd`.
- You can use these steps for most accidental deletions or branch mistakes in Git.
