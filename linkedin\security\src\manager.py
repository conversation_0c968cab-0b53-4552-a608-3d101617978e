import os
import subprocess
import sys
import base64
import hashlib
import configparser
import shutil
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import core
import utils
import backup

# --- Configuration is now handled by config.ini ---
SALT_FILE = 'salt.bin'
# --- End Configuration ---

def generate_and_save_keys(logger, project_root, config):
    """Generates and saves a new RSA key pair."""
    logger.info("\n--- Generating New Digital Signature Keys ---")
    private_key_file = config.get('Integrity', 'private_key_file')
    public_key_file = config.get('Integrity', 'public_key_file')
    private_key_path = os.path.join(project_root, 'security', private_key_file)
    public_key_path = os.path.join(project_root, 'security', public_key_file)

    if os.path.exists(private_key_path) or os.path.exists(public_key_path):
        logger.warning("Key files already exist. Overwriting them will invalidate all existing signatures.")
        confirm = input("Are you sure you want to proceed? (yes/no): ")
        if confirm.lower() != 'yes':
            logger.info("Key generation aborted by user.")
            return

    password = utils.get_password(prompt="Enter a strong password to encrypt your new private key: ")
    if not password:
        logger.error("Password entry failed. Aborting key generation.")
        return

    try:
        private_key, public_key = core.generate_key_pair()
        core.save_keys(private_key, public_key, private_key_path, public_key_path, password)
        logger.info(f"Successfully generated and saved keys:")
        logger.info(f"  - Encrypted Private Key: {private_key_path}")
        logger.info(f"  - Public Key: {public_key_path}")
        logger.warning("IMPORTANT: You must now re-encrypt your project to use these new keys.")
    except Exception as e:
        logger.error(f"Failed to generate keys: {e}", exc_info=True)

def show_setup_status(logger, project_root, config):
    """Checks for the existence of critical files and prints the setup status."""
    logger.info("\n--- Checking Security Toolkit Setup Status ---")
    
    salt_path = os.path.join(project_root, 'security', SALT_FILE)
    private_key_file = config.get('Integrity', 'private_key_file')
    public_key_file = config.get('Integrity', 'public_key_file')
    private_key_path = os.path.join(project_root, 'security', private_key_file)
    public_key_path = os.path.join(project_root, 'security', public_key_file)

    # Check for salt
    if os.path.exists(salt_path):
        logger.info(f"[FOUND] Salt File: {salt_path}")
    else:
        logger.warning(f"[MISSING] Salt File: {salt_path}")
        logger.warning("  -> Action: Run 'python security/run.py generate-salt'")

    # Check for private key
    if os.path.exists(private_key_path):
        logger.info(f"[FOUND] Private Key: {private_key_path}")
    else:
        logger.warning(f"[MISSING] Private Key: {private_key_path}")
        logger.warning("  -> Action: Run 'python security/run.py generate-keys'")

    # Check for public key
    if os.path.exists(public_key_path):
        logger.info(f"[FOUND] Public Key: {public_key_path}")
    else:
        logger.warning(f"[MISSING] Public Key: {public_key_path}")
        logger.warning("  -> Action: Run 'python security/run.py generate-keys'")

    logger.info("\n--- Status Check Complete ---")


def load_config(logger, project_root):
    """Loads the configuration from config.ini."""
    config_path = os.path.join(project_root, 'security', 'config.ini')
    config = configparser.ConfigParser()
    if not os.path.exists(config_path):
        logger.error(f"CRITICAL: config.ini not found at {config_path}. Cannot proceed.")
        return None
    config.read(config_path)
    return config

def process_files(action, level, password, salt, logger, project_root, config):
    """
    Atomically walks through the project directory and performs the given action on files.
    Operations are performed in a temporary directory and then committed.
    """
    action_str = "Encrypting" if action == 'encrypt' else "Decrypting"
    logger.info(f"--- Starting {action_str} (Level {level}) ---")

    # --- Setup paths and configuration ---
    excluded_dirs = set(e.strip() for e in config.get('Exclusions', 'excluded_dirs', fallback='').split(','))
    excluded_files = set(e.strip() for e in config.get('Exclusions', 'excluded_files', fallback='').split(','))
    file_extensions = tuple(e.strip() for e in config.get('Encryption', 'file_extensions', fallback='').split(','))
    
    security_dir = os.path.join(project_root, 'security')
    manifest_file = config.get('Integrity', 'manifest_file', fallback='manifest.sha256')
    private_key_file = config.get('Integrity', 'private_key_file')
    public_key_file = config.get('Integrity', 'public_key_file')

    manifest_path = os.path.join(security_dir, manifest_file)
    private_key_path = os.path.join(security_dir, private_key_file)
    public_key_path = os.path.join(security_dir, public_key_file)
    signature_file = f"{manifest_path}.sig"

    # --- Setup temporary directory for atomic operations ---
    temp_dir = os.path.join(security_dir, 'temp_processing')
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)

    try:
        # --- Select encryption/decryption implementation ---
        if level == '1':
            key = core.derive_key(password, salt, 32)
            fernet_key = base64.urlsafe_b64encode(key)
            encrypt_func = lambda data: core.encrypt_fernet(data, fernet_key)
            decrypt_func = lambda data: core.decrypt_fernet(data, fernet_key)
        elif level == '2':
            key = core.derive_key(password, salt, 32)
            encrypt_func = lambda data: core.encrypt_aes_gcm(data, key)
            decrypt_func = lambda data: core.decrypt_aes_gcm(data, key)
        else:
            raise ValueError("Invalid security level selected.")

        # --- Pre-action checks for Decryption ---
        if action == 'decrypt':
            logger.info("--- Running Pre-Decryption Integrity Check ---")
            encrypted_sig_path = f"{signature_file}.enc"
            encrypted_pubkey_path = f"{public_key_path}.enc"
            
            if not all(os.path.exists(p) for p in [encrypted_sig_path, encrypted_pubkey_path, manifest_path]):
                raise FileNotFoundError("Essential integrity files (.sig.enc, .pub.enc, or manifest) are missing. Cannot decrypt.")

            try:
                # Decrypt integrity files first
                with open(encrypted_sig_path, 'rb') as f: sig_data = decrypt_func(f.read())
                with open(encrypted_pubkey_path, 'rb') as f: pubkey_data = decrypt_func(f.read())
                
                # **BUG FIX**: Write decrypted keys back to disk to be available for next operation
                with open(public_key_path, 'wb') as f: f.write(pubkey_data)
                with open(signature_file, 'wb') as f: f.write(sig_data)
                
                public_key = serialization.load_pem_public_key(pubkey_data, backend=default_backend())
            except Exception as e:
                raise Exception(f"Failed to decrypt integrity files. Wrong password or files are corrupt. Error: {e}")

            with open(manifest_path, 'rb') as f: manifest_data = f.read()
            if not core.verify_signature(public_key, sig_data, manifest_data):
                raise Exception("Manifest signature is INVALID! The manifest file has been tampered with.")
            
            logger.info("Manifest signature verified.")
            if not verify_integrity(logger, project_root, config, pre_decryption_check=True):
                raise Exception("File hash verification failed against the manifest.")
            
            logger.info("--- Pre-Decryption Integrity Check Passed ---")

        # --- Main file processing loop (operates on temp_dir) ---
        files_to_process = []
        for root, dirs, files in os.walk(project_root, topdown=True):
            # Exclude the security directory itself from the walk
            if os.path.samefile(root, security_dir):
                dirs[:] = []
                files[:] = []
                continue
            
            dirs[:] = [d for d in dirs if d not in excluded_dirs]
            
            for filename in files:
                if filename.endswith(file_extensions):
                    files_to_process.append(os.path.join(root, filename))
        
        logger.info(f"Found {len(files_to_process)} files to {action}.")
        hashes = {}

        for full_path in files_to_process:
            try:
                with open(full_path, 'rb') as f: original_data = f.read()
                
                processed_data = encrypt_func(original_data) if action == 'encrypt' else decrypt_func(original_data)
                
                rel_path = os.path.relpath(full_path, project_root)
                temp_path = os.path.join(temp_dir, rel_path)
                os.makedirs(os.path.dirname(temp_path), exist_ok=True)
                
                with open(temp_path, 'wb') as f: f.write(processed_data)
                
                if action == 'encrypt':
                    hashes[rel_path.replace('\\', '/')] = hashlib.sha256(processed_data).hexdigest()
                
                logger.info(f"Processed: {rel_path}")
            except Exception as e:
                raise Exception(f"Failed to {action} {os.path.relpath(full_path, project_root)}. Reason: {e}")

        # --- Post-action / Commit Phase ---
        if action == 'encrypt':
            logger.info("Finalizing encryption: creating and signing manifest...")
            manifest_data = "".join(f"{h} *{p}\n" for p, h in sorted(hashes.items())).encode('utf-8')
            
            temp_manifest_path = os.path.join(temp_dir, os.path.basename(manifest_path))
            with open(temp_manifest_path, 'wb') as f: f.write(manifest_data)

            try:
                private_key = core.load_private_key(private_key_path, password)
                signature = core.sign_data(private_key, manifest_data)
                
                temp_signature_file = os.path.join(temp_dir, os.path.basename(signature_file))
                with open(temp_signature_file, 'wb') as f: f.write(signature)
                
                # Encrypt signature and public key into the temp dir
                with open(public_key_path, 'rb') as f: pubkey_data = f.read()
                
                encrypted_sig = encrypt_func(signature)
                encrypted_pubkey = encrypt_func(pubkey_data)
                
                with open(f"{temp_signature_file}.enc", 'wb') as f: f.write(encrypted_sig)
                with open(os.path.join(temp_dir, f"{os.path.basename(public_key_path)}.enc"), 'wb') as f: f.write(encrypted_pubkey)
            except Exception as e:
                raise Exception(f"Failed to sign or encrypt integrity files: {e}")

            logger.info("Commiting changes...")
            # Replace original files with their processed versions from temp_dir
            for rel_path_str in hashes.keys():
                rel_path = os.path.normpath(rel_path_str)
                shutil.move(os.path.join(temp_dir, rel_path), os.path.join(project_root, rel_path))
            
            # Move new integrity files into place
            shutil.move(temp_manifest_path, manifest_path)
            shutil.move(f"{temp_signature_file}.enc", f"{signature_file}.enc")
            shutil.move(os.path.join(temp_dir, f"{os.path.basename(public_key_path)}.enc"), f"{public_key_path}.enc")
            
            # Clean up plaintext signature
            if os.path.exists(signature_file): os.remove(signature_file)

        if action == 'decrypt':
            logger.info("Commiting changes...")
            # Replace encrypted files with decrypted versions
            for path in files_to_process:
                rel_path = os.path.relpath(path, project_root)
                shutil.move(os.path.join(temp_dir, rel_path), path)
            
            # Clean up encrypted integrity files and restore plaintext versions
            if os.path.exists(f"{signature_file}.enc"): os.remove(f"{signature_file}.enc")
            if os.path.exists(f"{public_key_path}.enc"): os.remove(f"{public_key_path}.enc")

        logger.info(f"--- {action_str} Complete ---")

    except Exception as e:
        logger.critical(f"A critical error occurred during the {action_str.lower()} process: {e}", exc_info=False)
        logger.critical("Operation aborted. The project has NOT been modified.")
    finally:
        # Clean up the temporary directory
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def verify_integrity(logger, project_root, config, pre_decryption_check=False):
    """Verifies the integrity of encrypted files against the manifest."""
    if not pre_decryption_check:
        logger.info("\n--- Starting File Integrity Verification ---")
    manifest_file = config.get('Integrity', 'manifest_file', fallback='manifest.sha256')
    manifest_path = os.path.join(project_root, 'security', manifest_file)

    if not os.path.exists(manifest_path):
        logger.error(f"Manifest file not found: {manifest_path}")
        logger.error("Cannot verify integrity. Please encrypt the project first to generate it.")
        return

    has_errors = False
    with open(manifest_path, 'r') as f:
        for line in f:
            try:
                hash_val, path = line.strip().split(' *')
                full_path = os.path.join(project_root, path.replace('/', '\\'))
                
                if not os.path.exists(full_path):
                    logger.error(f"[CORRUPT] File not found: {path}")
                    has_errors = True
                    continue

                with open(full_path, 'rb') as file_to_check:
                    current_hash = hashlib.sha256(file_to_check.read()).hexdigest()
                
                if current_hash != hash_val:
                    logger.error(f"[TAMPERED] Hash mismatch for: {path}")
                    has_errors = True
                elif not pre_decryption_check:
                    logger.info(f"[OK] Verified: {path}")

            except ValueError:
                logger.warning(f"Skipping malformed line in manifest: {line.strip()}")
                continue

    if not pre_decryption_check:
        logger.info("--- Verification Complete ---")
        if has_errors:
            logger.warning("Integrity check failed. Some files may be corrupt or tampered with.")
        else:
            logger.info("All files passed the integrity check.")
    
    return not has_errors

def obfuscate_project(logger, project_root):
    """Obfuscates the project using pyarmor with trial version limitations handling."""
    logger.info("\n--- Starting Obfuscation with PyArmor ---")
    
    output_dir = os.path.join(project_root, 'dist')
    entry_script = os.path.join(project_root, 'run_job_search.py') # Example, change if needed

    logger.info(f"Obfuscating project root: {project_root}")
    logger.info(f"Output will be in: {output_dir}")
    logger.info(f"Main script to obfuscate: {entry_script}")
    
    # Check if we're using trial version and warn about limitations
    logger.warning("Note: PyArmor trial version has a 32KB limit per code object.")
    logger.warning("Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.")

    try:
        # First attempt: Try with --exclude to skip problematic directories/files
        command = [
            'pyarmor-7', 'obfuscate',
            '--recursive',
            '--exclude', 'Linkedin',  # Exclude the Linkedin directory that has large files
            '--exclude', '*.pyc',     # Exclude compiled Python files
            '--exclude', '__pycache__',  # Exclude cache directories
            '--output', output_dir,
            entry_script
        ]
        
        logger.info("Attempting obfuscation with exclusions to avoid trial limitations...")
        result = subprocess.run(command, check=True, cwd=project_root, capture_output=True, text=True)
        
        logger.info(f"\nObfuscation complete. Check the '{output_dir}' directory.")
        logger.info(f"The obfuscated application and required runtime files have been placed in '{output_dir}'.")
        logger.info("You can run the application from there and distribute the entire directory.")
        
        # Copy excluded files manually (unobfuscated)
        linkedin_src = os.path.join(project_root, 'Linkedin')
        linkedin_dst = os.path.join(output_dir, 'Linkedin')
        if os.path.exists(linkedin_src):
            logger.info("Copying excluded Linkedin directory (unobfuscated) to output...")
            shutil.copytree(linkedin_src, linkedin_dst, dirs_exist_ok=True)
            logger.warning("WARNING: Files in 'Linkedin' directory are NOT obfuscated due to size limitations.")

    except subprocess.CalledProcessError as e:
        logger.error(f"Obfuscation failed: {e}")
        if "Too big code object" in str(e.stderr) or "32768 bytes" in str(e.stderr):
            logger.error("\n--- TRIAL VERSION LIMITATION DETECTED ---")
            logger.error("The trial version of PyArmor cannot obfuscate files larger than 32KB.")
            logger.info("\nAlternative solutions:")
            logger.info("1. Purchase PyArmor full version to remove size limitations")
            logger.info("2. Split large Python files into smaller modules")
            logger.info("3. Use alternative obfuscation tools")
            logger.info("4. Continue without obfuscation (encryption still provides protection)")
            
            # Offer to try obfuscating only small files
            try_selective = input("\nWould you like to try obfuscating only smaller files? (y/n): ")
            if try_selective.lower() == 'y':
                try_selective_obfuscation(logger, project_root, output_dir, entry_script)
        else:
            logger.error(f"Obfuscation error details: {e.stderr if hasattr(e, 'stderr') else 'No details available'}")
            
    except FileNotFoundError:
        logger.error("Error: 'pyarmor-7' is not installed or not in your PATH.")
        logger.error("Please install with: pip install pyarmor<8.0")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

def try_selective_obfuscation(logger, project_root, output_dir, entry_script):
    """Attempts to obfuscate only files that are small enough for the trial version."""
    logger.info("\n--- Attempting Selective Obfuscation ---")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find Python files and check their sizes
    small_files = []
    large_files = []
    max_size = 32000  # Leave some buffer below 32KB limit
    
    for root, dirs, files in os.walk(project_root):
        # Skip certain directories
        if any(skip_dir in root for skip_dir in ['__pycache__', '.git', 'security', 'dist']):
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size <= max_size:
                        small_files.append(file_path)
                    else:
                        large_files.append((file_path, file_size))
                except OSError:
                    continue
    
    logger.info(f"Found {len(small_files)} files suitable for obfuscation")
    logger.info(f"Found {len(large_files)} files too large for trial version:")
    for large_file, size in large_files:
        rel_path = os.path.relpath(large_file, project_root)
        logger.info(f"  - {rel_path} ({size:,} bytes)")
    
    if small_files:
        # Try to obfuscate individual small files
        success_count = 0
        for file_path in small_files:
            try:
                rel_path = os.path.relpath(file_path, project_root)
                output_file_dir = os.path.join(output_dir, os.path.dirname(rel_path))
                os.makedirs(output_file_dir, exist_ok=True)
                
                command = [
                    'pyarmor-7', 'obfuscate',
                    '--output', output_file_dir,
                    file_path
                ]
                
                subprocess.run(command, check=True, cwd=project_root, capture_output=True)
                success_count += 1
                logger.info(f"Obfuscated: {rel_path}")
                
            except subprocess.CalledProcessError:
                # Copy file unobfuscated if obfuscation fails
                rel_path = os.path.relpath(file_path, project_root)
                output_file_path = os.path.join(output_dir, rel_path)
                os.makedirs(os.path.dirname(output_file_path), exist_ok=True)
                shutil.copy2(file_path, output_file_path)
                logger.warning(f"Copied unobfuscated: {rel_path}")
        
        logger.info(f"Successfully obfuscated {success_count}/{len(small_files)} small files")
    
    # Copy large files unobfuscated
    for large_file, _ in large_files:
        rel_path = os.path.relpath(large_file, project_root)
        output_file_path = os.path.join(output_dir, rel_path)
        os.makedirs(os.path.dirname(output_file_path), exist_ok=True)
        shutil.copy2(large_file, output_file_path)
    
    # Copy other necessary files (non-Python)
    for root, dirs, files in os.walk(project_root):
        if any(skip_dir in root for skip_dir in ['__pycache__', '.git', 'security', 'dist']):
            continue
            
        for file in files:
            if not file.endswith('.py') and not file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, project_root)
                output_file_path = os.path.join(output_dir, rel_path)
                os.makedirs(os.path.dirname(output_file_path), exist_ok=True)
                try:
                    shutil.copy2(file_path, output_file_path)
                except (OSError, shutil.Error):
                    pass  # Skip files that can't be copied
    
    logger.info(f"\nSelective obfuscation complete. Output in: {output_dir}")
    logger.warning("Large files have been copied unobfuscated due to trial version limitations.")


def alternative_protection_menu(logger, project_root):
    """Provides alternative code protection methods when PyArmor fails."""
    logger.info("\n--- Alternative Code Protection Methods ---")
    logger.info("1. Split Large Files (PyArmor Compatible)")
    logger.info("2. Python Minifier + Obfuscation")
    logger.info("3. Nuitka Compilation (Recommended)")
    logger.info("4. PyInstaller + UPX Packing")
    logger.info("5. Custom Source Encryption")
    logger.info("6. Cython Compilation")
    logger.info("7. View Detailed Guide")
    logger.info("8. Back to Main Menu")
    
    choice = input("Select protection method (1-8): ")
    logger.info(f"User selected alternative protection: '{choice}'")
    
    if choice == '1':
        split_large_files(logger, project_root)
    elif choice == '2':
        python_minifier_protection(logger, project_root)
    elif choice == '3':
        nuitka_compilation(logger, project_root)
    elif choice == '4':
        pyinstaller_protection(logger, project_root)
    elif choice == '5':
        custom_encryption_protection(logger, project_root)
    elif choice == '6':
        cython_compilation(logger, project_root)
    elif choice == '7':
        show_protection_guide(logger, project_root)
    elif choice == '8':
        return
    else:
        logger.warning(f"Invalid choice: '{choice}'. Please try again.")

def split_large_files(logger, project_root):
    """Analyzes and offers to split large Python files for PyArmor compatibility."""
    logger.info("\n--- File Size Analysis for PyArmor Compatibility ---")
    
    large_files = []
    max_size = 32000  # 32KB limit for PyArmor trial
    
    for root, dirs, files in os.walk(project_root):
        if 'security' in root or '__pycache__' in root:
            continue
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size > max_size:
                        large_files.append((file_path, file_size))
                except OSError:
                    continue
    
    if not large_files:
        logger.info("No files exceed the 32KB PyArmor trial limit.")
        return
    
    logger.info(f"Found {len(large_files)} files exceeding 32KB limit:")
    for file_path, size in large_files:
        rel_path = os.path.relpath(file_path, project_root)
        logger.info(f"  - {rel_path}: {size:,} bytes ({size/1024:.1f} KB)")
    
    proceed = input("\nWould you like to create a modular structure for the largest file? (y/n): ")
    if proceed.lower() == 'y' and large_files:
        largest_file = max(large_files, key=lambda x: x[1])
        create_modular_structure(logger, project_root, largest_file[0])

def create_modular_structure(logger, project_root, large_file_path):
    """Creates a modular structure for a large Python file."""
    logger.info(f"\n--- Creating Modular Structure for {os.path.basename(large_file_path)} ---")
    
    # Create modules directory
    modules_dir = os.path.join(project_root, 'modules')
    os.makedirs(modules_dir, exist_ok=True)
    
    # Read the large file
    try:
        with open(large_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"Failed to read file: {e}")
        return
    
    # Basic class/function extraction (simplified)
    import re
    
    # Extract classes
    class_pattern = r'class\s+(\w+).*?(?=\nclass|\ndef|\n\n|\Z)'
    classes = re.findall(class_pattern, content, re.DOTALL)
    
    # Extract functions
    function_pattern = r'def\s+(\w+).*?(?=\ndef|\nclass|\n\n|\Z)'
    functions = re.findall(function_pattern, content, re.DOTALL)
    
    logger.info(f"Found {len(classes)} classes and {len(functions)} functions")
    
    # Create __init__.py
    init_content = """# Modular structure for PyArmor compatibility
# Import all modules here
"""
    
    with open(os.path.join(modules_dir, '__init__.py'), 'w') as f:
        f.write(init_content)
    
    # Create a simple split (header + imports in one file, classes in another)
    lines = content.split('\n')
    import_lines = []
    other_lines = []
    
    for line in lines:
        if (line.strip().startswith('import ') or 
            line.strip().startswith('from ') or 
            line.strip().startswith('#') or
            line.strip() == '' or
            line.strip().startswith('"""') or
            line.strip().startswith("'''")):
            import_lines.append(line)
        else:
            other_lines.append(line)
    
    # Split into roughly equal parts
    mid_point = len(other_lines) // 2
    part1 = other_lines[:mid_point]
    part2 = other_lines[mid_point:]
    
    # Write parts
    base_name = os.path.splitext(os.path.basename(large_file_path))[0]
    
    # Part 1
    with open(os.path.join(modules_dir, f'{base_name}_core.py'), 'w') as f:
        f.write('\n'.join(import_lines + part1))
    
    # Part 2
    with open(os.path.join(modules_dir, f'{base_name}_handlers.py'), 'w') as f:
        f.write('\n'.join(import_lines + part2))
    
    # Create new main file
    main_content = f'''#!/usr/bin/env python
"""
Modular version of {os.path.basename(large_file_path)}
Split for PyArmor trial compatibility
"""

# Import all modules
from modules.{base_name}_core import *
from modules.{base_name}_handlers import *

# Main execution
if __name__ == "__main__":
    # Add your main execution code here
    pass
'''
    
    main_file = os.path.join(project_root, f'{base_name}_modular.py')
    with open(main_file, 'w') as f:
        f.write(main_content)
    
    logger.info(f"Created modular structure:")
    logger.info(f"  - {os.path.relpath(os.path.join(modules_dir, f'{base_name}_core.py'), project_root)}")
    logger.info(f"  - {os.path.relpath(os.path.join(modules_dir, f'{base_name}_handlers.py'), project_root)}")
    logger.info(f"  - {os.path.relpath(main_file, project_root)}")
    logger.warning("Note: You may need to adjust imports and fix any circular dependencies manually.")

def nuitka_compilation(logger, project_root):
    """Compiles Python files using Nuitka."""
    logger.info("\n--- Nuitka Compilation Protection ---")
    
    try:
        # Check if Nuitka is installed
        result = subprocess.run(['python', '-c', 'import nuitka'], capture_output=True)
        if result.returncode != 0:
            logger.warning("Nuitka not installed. Installing...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
    except Exception as e:
        logger.error(f"Failed to install Nuitka: {e}")
        return
      # Find Python entry point
    entry_scripts = []
    for file in os.listdir(project_root):
        if file.endswith('.py') and not file.startswith('_'):
            entry_scripts.append(file)
    
    if not entry_scripts:
        logger.error("No Python entry scripts found in project root.")
        return
    
    logger.info("Available entry scripts:")
    for i, script in enumerate(entry_scripts, 1):
        logger.info(f"  {i}. {script}")
    
    try:
        choice = int(input("Select entry script number: ")) - 1
        if 0 <= choice < len(entry_scripts):
            entry_script = entry_scripts[choice]
        else:
            logger.error("Invalid selection.")
            return
    except ValueError:
        logger.error("Invalid input. Please enter a number.")
        return
    
    output_dir = os.path.join(project_root, 'nuitka_dist')
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info(f"Compiling {entry_script} with Nuitka...")
    
    try:
        command = [
            sys.executable, '-m', 'nuitka',
            '--standalone',
            f'--output-dir={output_dir}',
            '--assume-yes-for-downloads',
            '--follow-imports',
            '--enable-plugin=tk-inter',
            '--disable-console',
            os.path.join(project_root, entry_script)
        ]
        
        logger.info("Starting Nuitka compilation (this may take several minutes)...")
        result = subprocess.run(command, check=True, cwd=project_root, capture_output=True, text=True)
        logger.info(f"Nuitka compilation complete. Output in: {output_dir}")
        logger.info("The compiled executable provides strong protection against reverse engineering.")
        
        # Look for the generated executable
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith('.exe'):
                    exe_path = os.path.join(root, file)
                    logger.info(f"Executable created: {exe_path}")
                    break
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Nuitka compilation failed: {e}")
        if hasattr(e, 'stderr') and e.stderr:
            logger.error(f"Error details: {e.stderr}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

def python_minifier_protection(logger, project_root):
    """Applies Python minification and basic obfuscation."""
    logger.info("\n--- Python Minifier Protection ---")
    
    try:
        # Check if python-minifier is installed
        result = subprocess.run(['python', '-c', 'import python_minifier'], capture_output=True)
        if result.returncode != 0:
            logger.warning("python-minifier not installed. Installing...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'python-minifier'], check=True)
    except Exception as e:
        logger.error(f"Failed to install python-minifier: {e}")
        return
    
    output_dir = os.path.join(project_root, 'minified')
    os.makedirs(output_dir, exist_ok=True)
    
    # Find Python files to minify
    python_files = []
    for root, dirs, files in os.walk(project_root):
        if 'security' in root or '__pycache__' in root or 'minified' in root:
            continue
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    logger.info(f"Found {len(python_files)} Python files to minify")
    
    try:
        import python_minifier
        
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, project_root)
            output_path = os.path.join(output_dir, rel_path)
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                source = f.read()
            
            # Only use supported arguments for python_minifier.minify
            minified = python_minifier.minify(
                source,
                remove_literal_statements=True,
                remove_annotations=True,
                combine_imports=True,
                hoist_literals=True,
                rename_locals=True,
                rename_globals=True,
                remove_object_base=True,
                convert_posargs_to_args=True
            )
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(minified)
            
            logger.info(f"Minified: {rel_path}")
    
        logger.info(f"Minification complete. Output in: {output_dir}")
        logger.info("Files are now minified and partially obfuscated.")
        
    except Exception as e:
        logger.error(f"Minification failed: {e}")

def pyinstaller_protection(logger, project_root):
    """Creates executable using PyInstaller with UPX compression."""
    logger.info("\n--- PyInstaller + UPX Protection ---")
    
    try:
        # Check if PyInstaller is installed
        result = subprocess.run(['python', '-c', 'import PyInstaller'], capture_output=True)
        if result.returncode != 0:
            logger.warning("PyInstaller not installed. Installing...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
    except Exception as e:
        logger.error(f"Failed to install PyInstaller: {e}")
        return
    
    # Find entry point
    entry_scripts = [f for f in os.listdir(project_root) if f.endswith('.py') and not f.startswith('_')]
    
    if not entry_scripts:
        logger.error("No Python entry scripts found.")
        return
    
    logger.info("Available entry scripts:")
    for i, script in enumerate(entry_scripts, 1):
        logger.info(f"  {i}. {script}")
    
    try:
        choice = int(input("Select entry script number: ")) - 1
        if 0 <= choice < len(entry_scripts):
            entry_script = entry_scripts[choice]
        else:
            logger.error("Invalid selection.")
            return
    except ValueError:
        logger.error("Invalid input.")
        return
    
    logger.info(f"Creating executable for {entry_script}...")
    
    try:
        command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--distpath', os.path.join(project_root, 'dist'),
            '--workpath', os.path.join(project_root, 'build'),
            '--specpath', os.path.join(project_root, 'spec'),
            os.path.join(project_root, entry_script)
        ]
        
        subprocess.run(command, check=True, cwd=project_root)
        
        exe_name = os.path.splitext(entry_script)[0] + '.exe'
        exe_path = os.path.join(project_root, 'dist', exe_name)
        
        if os.path.exists(exe_path):
            logger.info(f"PyInstaller executable created: {exe_path}")
            
            # Try UPX compression if available
            try:
                subprocess.run(['upx', '--best', exe_path], check=True)
                logger.info("UPX compression applied successfully.")
            except (subprocess.CalledProcessError, FileNotFoundError):
                logger.warning("UPX not available or compression failed. Executable created without compression.")
        
    except subprocess.CalledProcessError as e:
        logger.error(f"PyInstaller failed: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")

def custom_encryption_protection(logger, project_root):
    """Creates encrypted Python files with a runner."""
    logger.info("\n--- Custom Source Encryption Protection ---")
    
    try:
        from cryptography.fernet import Fernet
    except ImportError:
        logger.error("cryptography library required. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'cryptography'], check=True)
        from cryptography.fernet import Fernet
    
    # Generate encryption key
    key = Fernet.generate_key()
    f = Fernet(key)
    
    output_dir = os.path.join(project_root, 'encrypted')
    os.makedirs(output_dir, exist_ok=True)
    
    # Find Python files to encrypt
    python_files = []
    for root, dirs, files in os.walk(project_root):
        if any(skip in root for skip in ['security', '__pycache__', 'encrypted']):
            continue
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    logger.info(f"Encrypting {len(python_files)} Python files...")
    
    encrypted_files = {}
    for file_path in python_files:
        rel_path = os.path.relpath(file_path, project_root)
        
        with open(file_path, 'rb') as f:
            source_code = f.read()
        
        encrypted_code = f.encrypt(source_code)
        encrypted_files[rel_path] = encrypted_code
        logger.info(f"Encrypted: {rel_path}")
    
    # Create runner script
    runner_code = f'''#!/usr/bin/env python
"""
Encrypted Python Runner
This file contains encrypted source code that is decrypted and executed at runtime.
"""

import base64
from cryptography.fernet import Fernet

# Encryption key (in production, this should be better protected)
KEY = {key!r}

# Encrypted files
ENCRYPTED_FILES = {encrypted_files!r}

def decrypt_and_execute(encrypted_data):
    """Decrypt and execute Python code."""
    f = Fernet(KEY)
    decrypted_code = f.decrypt(encrypted_data)
    exec(decrypted_code, globals())

def main():
    """Main execution function."""
    # Execute main module (adjust as needed)
    main_module = next((k for k in ENCRYPTED_FILES.keys() if 'main' in k.lower() or k.endswith('.py')), None)
    if main_module:
        print(f"Executing encrypted module: {{main_module}}")
        decrypt_and_execute(ENCRYPTED_FILES[main_module])
    else:
        print("No main module found. Available modules:")
        for module in ENCRYPTED_FILES.keys():
            print(f"  - {{module}}")

if __name__ == "__main__":
    main()
'''
    
    runner_path = os.path.join(output_dir, 'encrypted_runner.py')
    with open(runner_path, 'w') as f:
        f.write(runner_code)
    
    logger.info(f"Custom encryption complete. Files encrypted in: {output_dir}")
    logger.info(f"Run with: python {runner_path}")
    logger.warning("Note: The encryption key is embedded in the runner. For production use, implement better key protection.")

def cython_compilation(logger, project_root):
    """Compiles Python files to Cython for protection."""
    logger.info("\n--- Cython Compilation Protection ---")
    
    try:
        result = subprocess.run(['python', '-c', 'import Cython'], capture_output=True)
        if result.returncode != 0:
            logger.warning("Cython not installed. Installing...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'Cython'], check=True)
    except Exception as e:
        logger.error(f"Failed to install Cython: {e}")
        return
    
    logger.info("Cython compilation is a complex process that requires:")
    logger.info("1. Converting .py files to .pyx files")
    logger.info("2. Creating setup.py for compilation")
    logger.info("3. Compiling to C extensions")
    logger.info("4. Building binary modules")
    logger.info("\nThis process requires manual setup and C compiler.")
    logger.info("Consider using Nuitka for easier compilation instead.")

def show_protection_guide(logger, project_root):
    """Shows the detailed protection guide."""
    guide_path = os.path.join(project_root, 'security', 'code_protection_alternatives.md')
    if os.path.exists(guide_path):
        logger.info(f"\nDetailed guide available at: {guide_path}")
        try:
            with open(guide_path, 'r') as f:
                content = f.read()
            print("\n" + "="*60)
            print(content[:2000] + "..." if len(content) > 2000 else content)
            print("="*60)
        except Exception as e:
            logger.error(f"Failed to read guide: {e}")
    else:
        logger.error("Protection guide not found.")


def self_destruct(logger, project_root):
    """Triggers the self-destruct batch script."""
    logger.warning("\n" + "="*50)
    logger.warning("WARNING: IRREVERSIBLE ACTION")
    logger.warning("="*50)
    logger.info("You are about to trigger the self-destruct mechanism.")
    logger.info(f"This will PERMANENTLY DELETE the entire project directory located at: {project_root}")
    logger.warning("This action cannot be undone. All backups will also be deleted.")
    
    # Forcing the user to type the full path is a strong safeguard
    confirm = input(f"To confirm, please type the full path of the directory to be deleted: ")
    
    if confirm.strip() == project_root:
        logger.critical("Self-destruct sequence confirmed by user. Deleting project...")
        try:
            # Use shutil.rmtree for a powerful, cross-platform delete
            shutil.rmtree(project_root)
            # The script itself will be deleted, so this is the last action.
            # A print statement is used here as the logger might be gone.
            print("Project deleted.")
        except Exception as e:
            logger.critical(f"Failed to delete project: {e}")
    else:
        logger.info("Self-destruct aborted. Path did not match.")

def main(project_root):
    logger = utils.setup_logger()
    # Add a polished ASCII art banner and colorized menu for Alpha Secure
    banner = r'''
\033[1;36m
    █████╗ ██╗      ███████╗ █████╗     ███████╗███████╗██████╗ ██╗   ██╗
   ██╔══██╗██║      ██╔════╝██╔══██╗    ██╔════╝██╔════╝██╔══██╗╚██╗ ██╔╝
   ███████║██║█████╗███████╗███████║    ███████╗█████╗  ██████╔╝ ╚████╔╝ 
   ██╔══██║██║╚════╝╚════██║██╔══██║    ╚════██║██╔══╝  ██╔══██╗  ╚██╔╝  
   ██║  ██║███████╗███████║██║  ██║    ███████║███████╗██║  ██║   ██║   
   ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═╝   ╚═╝   
\033[0m'''
    print(banner)
    print("\033[1;32m--- ALPHA SECURE MANAGER (v3.1 - Enhanced Protection) ---\033[0m")
    config = load_config(logger, project_root)
    if not config:
        return
    try:
        salt_path = os.path.join(project_root, 'security', SALT_FILE)
        if not os.path.exists(salt_path):
            logger.error(f"Error: '{SALT_FILE}' not found in the 'security' directory.")
            logger.error("Please run 'python run.py generate-salt' first to create it.")
            return
        with open(salt_path, 'rb') as f:
            salt = f.read()
        while True:
            print("\033[1;34m" + "="*70 + "\033[0m")
            print("\033[1;33mSelect an option:\033[0m")
            print("\033[1;36m 1.\033[0m Encrypt Project")
            print("\033[1;36m 2.\033[0m Decrypt Project")
            print("\033[1;36m 3.\033[0m Verify Project Integrity (Standalone)")
            print("\033[1;36m 4.\033[0m Create Project Backup")
            print("\033[1;36m 5.\033[0m Manage Backups (Prune Old)")
            print("\033[1;36m 6.\033[0m Obfuscate Project (PyArmor)")
            print("\033[1;36m 7.\033[0m Alternative Protection Methods")
            print("\033[1;31m 8.\033[0m Self-Destruct Project (DANGEROUS)")
            print("\033[1;35m 9.\033[0m Generate New Signature Keys (DANGEROUS)")
            print("\033[1;36m10.\033[0m Check Setup Status")
            print("\033[1;36m11.\033[0m Cleanup and Start New")
            print("\033[1;36m12.\033[0m Exit")
            print("\033[1;34m" + "="*70 + "\033[0m")
            choice = input("\033[1;33mAlpha Secure > \033[0m").strip()
            logger.info(f"User selected option: '{choice}'")

            if choice in ['1', '2']:
                default_level = config.get('Encryption', 'default_level', fallback='1')
                level = input(f"Select security level (1: Standard, 2: Paranoid) [Default: {default_level}]: ") or default_level
                logger.info(f"User selected security level: '{level}'")
                if level not in ['1', '2']:
                    logger.warning("Invalid level. Please try again.")
                    continue
                
                password = utils.get_password()
                if password:
                    action = 'encrypt' if choice == '1' else 'decrypt'
                    logger.info(f"Initiating {action} process.")
                    process_files(action, level, password, salt, logger, project_root, config)

            elif choice == '3':
                logger.warning("This is a standalone check. For decryption, integrity is checked automatically.")
                verify_integrity(logger, project_root, config)

            elif choice == '4':
                logger.info("Initiating backup process.")
                backup.run_backup_process(project_root, logger, config)

            elif choice == '5':
                logger.info("Opening backup management.")
                backup.prune_backups(os.path.join(project_root, 'backups'), logger, config)

            elif choice == '6':
                logger.info("Initiating obfuscation process.")
                obfuscate_project(logger, project_root)
                
            elif choice == '7':
                logger.info("Opening alternative protection methods.")
                alternative_protection_menu(logger, project_root)
                
            elif choice == '8':
                logger.warning("Initiating self-destruct sequence.")
                self_destruct(logger, project_root)
                break

            elif choice == '9':
                generate_and_save_keys(logger, project_root, config)

            elif choice == '10':
                show_setup_status(logger, project_root, config)

            elif choice == '11':
                confirm = input("This will delete security files and logs. Are you sure? (y/n): ")
                if confirm.lower() == 'y':
                    cleanup_and_start_new(logger, project_root)
                    logger.info("Please restart the manager to continue.")
                    break # Exit after cleanup
                else:
                    logger.info("Cleanup aborted by user.")

            elif choice == '12':
                break
            else:
                logger.warning(f"Invalid choice: '{choice}'. Please enter a number between 1 and 12.")
    
    except Exception as e:
        logger.critical(f"An unhandled exception occurred in the main loop: {e}", exc_info=True)
    finally:
        logger.info("--- Exiting Security Manager ---")

def cleanup_and_start_new(logger, project_root):
    """Deletes generated security files to prepare for a fresh start."""
    logger.info("\n--- CLEANUP AND START NEW ---")
    security_dir = os.path.join(project_root, 'security')
    
    # List of files and directories to remove
    files_to_delete = [
        'salt.bin',
        'private_key.pem',
        'public_key.pem',
        'public_key.pem.enc',
        'manifest.sha256',
        'manifest.sha256.sig',
        'manifest.sha256.sig.enc'
    ]
    dirs_to_delete = [
        os.path.join(security_dir, 'logs'),
        os.path.join(security_dir, 'temp_processing'),
        os.path.join(security_dir, '__pycache__'),
        os.path.join(security_dir, 'src', '__pycache__')
    ]

    for filename in files_to_delete:
        file_path = os.path.join(security_dir, filename)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info(f"[DELETED] {filename}")
            except Exception as e:
                logger.error(f"[FAILED] Could not delete {filename}: {e}")
        else:
            logger.info(f"[SKIPPED] {filename} not found.")

    for dir_path in dirs_to_delete:
        if os.path.exists(dir_path):
            try:
                shutil.rmtree(dir_path)
                logger.info(f"[DELETED] Directory {os.path.relpath(dir_path, security_dir)}")
            except Exception as e:
                logger.error(f"[FAILED] Could not delete directory {dir_path}: {e}")
        else:
            logger.info(f"[SKIPPED] Directory {os.path.relpath(dir_path, security_dir)} not found.")

    logger.info("\n--- Cleanup Complete ---")
    logger.info("Project has been reset. To secure it again, please run:")
    logger.info("1. python run.py generate-salt")
    logger.info("2. python run.py generate-keys")
    logger.info("3. python run.py (and select 'Encrypt Project')")


def force_decrypt_files(password, salt, logger, project_root, config):
    """A special recovery function to decrypt files when integrity checks cannot be passed."""
    logger.warning("\n--- INITIATING FORCE DECRYPTION (RECOVERY MODE) ---")
    logger.warning("This mode bypasses all integrity and signature checks.")
    logger.warning("Use only for recovering files after a failed encryption.")

    # Simplified key derivation (assumes level 1, which was used)
    key = core.derive_key(password, salt, 32)
    fernet_key = base64.urlsafe_b64encode(key)
    
    # We can't use the normal decrypt_func because it requires the private key.
    # We must use the raw underlying Fernet decryption for this recovery.
    f = core.Fernet(fernet_key)
    try:
        decrypt_func = f.decrypt
    except Exception as e:
        logger.critical(f"Failed to create decryption cipher. Incorrect password? Error: {e}")
        return

    excluded_dirs = set(e.strip() for e in config.get('Exclusions', 'excluded_dirs', fallback='').split(','))
    excluded_files = set(e.strip() for e in config.get('Exclusions', 'excluded_files', fallback='').split(','))
    file_extensions = tuple(e.strip() for e in config.get('Encryption', 'file_extensions', fallback='').split(','))

    files_to_process = []
    for root, dirs, files in os.walk(project_root, topdown=True):
        dirs[:] = [d for d in dirs if d not in excluded_dirs]
        for filename in files:
            if os.path.commonpath([os.path.join(project_root, 'security'), os.path.join(root, filename)]) == os.path.join(project_root, 'security'):
                continue
            if filename.endswith(file_extensions):
                files_to_process.append(os.path.join(root, filename))

    logger.info(f"Found {len(files_to_process)} files to attempt to decrypt.")

    for full_path in files_to_process:
        try:
            with open(full_path, 'rb') as f_enc:
                encrypted_data = f_enc.read()
            
            # Attempt to decrypt
            decrypted_data = decrypt_func(encrypted_data)

            with open(full_path, 'wb') as f_dec:
                f_dec.write(decrypted_data)
            logger.info(f"[SUCCESS] Decrypted: {os.path.relpath(full_path, project_root)}")
        except core.InvalidToken:
            logger.error(f"[FAILED] Could not decrypt {os.path.relpath(full_path, project_root)}. The file may not have been encrypted or the key is wrong.")
        except Exception as e:
            logger.error(f"[FAILED] An unexpected error occurred for {os.path.relpath(full_path, project_root)}: {e}")

    logger.info("--- Force Decryption Attempt Complete ---")
    logger.warning("RECOMMENDATION: Generate keys and perform a full, proper encryption now.")

def prepare_recipient_package(logger, project_root):
    """
    Prepares a 'recipient' folder containing only the files needed to share with a receiver.
    Copies the encrypted project and the decryptor executable into 'security/recipient/'.
    """
    import shutil
    import os

    recipient_dir = os.path.join(project_root, 'security', 'recipient')
    if os.path.exists(recipient_dir):
        shutil.rmtree(recipient_dir)
    os.makedirs(recipient_dir, exist_ok=True)

    # List of files/folders to include for the recipient
    include = [
        'backups',
        'manifest.sha256',
        'manifest.sha256.sig.enc',
        'public_key.pem.enc',
        'decryptor.exe',
    ]
    # Copy each file/folder if it exists
    for name in include:
        src_path = os.path.join(project_root, 'security', name)
        dst_path = os.path.join(recipient_dir, name)
        if os.path.exists(src_path):
            if os.path.isdir(src_path):
                shutil.copytree(src_path, dst_path)
            else:
                shutil.copy2(src_path, dst_path)
            logger.info(f"[RECIPIENT] Copied: {name}")
        else:
            logger.warning(f"[RECIPIENT] Not found: {name}")
    logger.info(f"Recipient package prepared at: {recipient_dir}")

