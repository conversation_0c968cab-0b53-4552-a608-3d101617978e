#!/usr/bin/env python
"""
LinkedIn Easy Apply Pro - Main Execution Script
Professional-grade automation with 99% success ratio

Usage:
    python linkedin_easy_apply_pro_main.py

Features:
- AI-powered question answering
- Smart job filtering and matching
- Advanced error recovery
- Human-like behavior simulation
- Real-time performance analytics
- Comprehensive logging and monitoring
"""

import os
import sys
import json
import argparse
from datetime import datetime
from linkedin_easy_apply import LinkedInEasyApplyPro

def load_user_config():
    """Load user configuration from file or create default"""
    config_file = "linkedin_config.json"
    
    default_config = {
        "credentials": {
            "email": "",
            "password": ""
        },
        "search_preferences": {
            "default_search_term": "data analyst",
            "default_location": "Remote",
            "max_applications_per_session": 50
        },
        "user_profile": {
            "skills": ["Python", "Data Analysis", "Machine Learning", "SQL", "Excel"],
            "experience_years": 3,
            "preferred_locations": ["Remote", "New York", "San Francisco", "Austin"],
            "salary_range": [80000, 150000],
            "job_types": ["Full-time", "Contract"],
            "industries": ["Technology", "Finance", "Healthcare", "E-commerce"]
        },
        "automation_settings": {
            "headless": False,
            "stealth_mode": True,
            "intelligent_filtering": True,
            "auto_optimization": True,
            "success_rate_target": 0.99,
            "max_retries": 5,
            "application_threshold": 0.7
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            print(f"✅ Loaded configuration from {config_file}")
            return config
        except Exception as e:
            print(f"⚠️ Error loading config file: {e}")
            print("Using default configuration...")
    
    # Save default config for future use
    try:
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        print(f"📝 Created default configuration file: {config_file}")
        print("Please edit the file to customize your settings.")
    except Exception as e:
        print(f"⚠️ Could not save config file: {e}")
    
    return default_config

def get_user_inputs(config):
    """Get user inputs for the session"""
    print("\n" + "="*60)
    print("🚀 LINKEDIN EASY APPLY PRO - PROFESSIONAL AUTOMATION")
    print("="*60)
    
    # Get credentials if not in config
    email = config["credentials"].get("email")
    password = config["credentials"].get("password")
    
    if not email:
        email = input("📧 Enter your LinkedIn email: ").strip()
    
    if not password:
        import getpass
        password = getpass.getpass("🔐 Enter your LinkedIn password: ")
    
    # Get search parameters
    print(f"\n🔍 Search Configuration:")
    search_term = input(f"Job search term (default: '{config['search_preferences']['default_search_term']}'): ").strip()
    if not search_term:
        search_term = config['search_preferences']['default_search_term']
    
    location = input(f"Location (default: '{config['search_preferences']['default_location']}'): ").strip()
    if not location:
        location = config['search_preferences']['default_location']
    
    max_apps = input(f"Max applications (default: {config['search_preferences']['max_applications_per_session']}): ").strip()
    if not max_apps:
        max_apps = config['search_preferences']['max_applications_per_session']
    else:
        try:
            max_apps = int(max_apps)
        except ValueError:
            max_apps = config['search_preferences']['max_applications_per_session']
    
    # Update credentials in config
    config["credentials"]["email"] = email
    config["credentials"]["password"] = password
    
    return search_term, location, max_apps

def display_session_summary(results):
    """Display comprehensive session summary"""
    print("\n" + "="*60)
    print("📊 SESSION SUMMARY")
    print("="*60)
    
    print(f"🎯 Applications Submitted: {results['successful_applications']}")
    print(f"❌ Applications Failed: {results['failed_applications']}")
    print(f"📈 Success Rate: {results['success_rate']:.1%}")
    print(f"🔍 Jobs Analyzed: {results['jobs_analyzed']}")
    print(f"⏱️ Session Duration: {results['session_duration']/60:.1f} minutes")
    print(f"🚀 Applications/Hour: {results.get('applications_per_hour', 0):.1f}")
    
    if results.get('recommendations'):
        print(f"\n💡 Recommendations:")
        for rec in results['recommendations']:
            print(f"   • {rec}")
    
    if results.get('errors'):
        print(f"\n⚠️ Errors Encountered ({len(results['errors'])}):")
        for error in results['errors'][:5]:  # Show first 5 errors
            print(f"   • {error}")
        if len(results['errors']) > 5:
            print(f"   ... and {len(results['errors']) - 5} more")

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="LinkedIn Easy Apply Pro - Professional Automation")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    parser.add_argument("--config", type=str, help="Path to custom config file")
    parser.add_argument("--search", type=str, help="Job search term")
    parser.add_argument("--location", type=str, help="Job location")
    parser.add_argument("--max-apps", type=int, help="Maximum applications")
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = load_user_config()
        
        # Override with command line arguments
        if args.headless:
            config["automation_settings"]["headless"] = True
        
        # Get user inputs
        if args.search and args.location and args.max_apps:
            search_term = args.search
            location = args.location
            max_applications = args.max_apps
            # Still need credentials
            email = config["credentials"].get("email") or input("📧 LinkedIn email: ")
            password = config["credentials"].get("password") or input("🔐 LinkedIn password: ")
            config["credentials"]["email"] = email
            config["credentials"]["password"] = password
        else:
            search_term, location, max_applications = get_user_inputs(config)
        
        print(f"\n🎯 Starting automation:")
        print(f"   Search: '{search_term}' in '{location}'")
        print(f"   Target: {max_applications} applications")
        print(f"   Mode: {'Headless' if config['automation_settings']['headless'] else 'Visible'}")
        
        # Initialize the professional automation
        automation = LinkedInEasyApplyPro(
            headless=config["automation_settings"]["headless"],
            credentials=config["credentials"],
            config=config
        )
        
        # Run the automation
        print(f"\n🚀 Launching Professional LinkedIn Automation...")
        results = automation.run_professional_automation(
            search_term=search_term,
            location=location,
            max_applications=max_applications
        )
        
        # Display results
        display_session_summary(results)
        
        # Save session results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"session_results_{timestamp}.json"
        
        try:
            with open(results_file, 'w') as f:
                # Convert datetime objects to strings for JSON serialization
                serializable_results = results.copy()
                json.dump(serializable_results, f, indent=2, default=str)
            print(f"\n💾 Session results saved to: {results_file}")
        except Exception as e:
            print(f"⚠️ Could not save results: {e}")
        
        # Exit with appropriate code
        if results['success_rate'] >= 0.8:
            print(f"\n🎉 Session completed successfully!")
            sys.exit(0)
        else:
            print(f"\n⚠️ Session completed with low success rate")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️ Automation stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Critical error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
