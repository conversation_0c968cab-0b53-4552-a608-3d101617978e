# Technical Architecture & Design: Project Security Toolkit v3.0

**Document Version:** 1.0
**Date:** 2025-06-21
**Author:** GitHub Copilot

## 1.0 Executive Summary

This document provides a comprehensive technical breakdown of the Project Security & Backup Toolkit v3.0. The toolkit is a self-contained, multi-layered security suite designed to protect a software project's source code and intellectual property through a combination of robust encryption, digital signatures, code obfuscation, and resilient backup mechanisms.

The system's core philosophy is built upon four principles:

1.  **Defense in Depth:** Security is not achieved by a single control, but by a series of overlapping, independent layers. An attacker who bypasses one layer is still faced with others.
2.  **Chain of Trust:** The system's integrity relies on a cryptographic chain. The validity of the source code is guaranteed by a manifest file, and the validity of the manifest is guaranteed by a digital signature. This chain is verified before any sensitive operations are permitted.
3.  **Tamper-Evident Design:** The system is designed to make unauthorized modification immediately obvious. It prioritizes failing safely and loudly over operating in a potentially compromised state. Any break in the chain of trust halts the decryption process.
4.  **Zero-Trust for Self:** The toolkit does not inherently trust its own state. It performs mandatory verification checks before critical operations, assuming that files could have been altered between executions.

---

## 2.0 System Architecture

### 2.1 Component Diagram

```
[ User ]
   |
   v
[ run.py (Entry Point) ]
   |   ^         |
   |   |         v
   |   +-----> [ manager.py (Main Logic & CLI) ]
   |                 |         ^           |
   |                 |         |           v
   |                 v         |
   |   [ generate-salt, generate-keys ]
   |
   +-----> [ src/core.py (Cryptography Engine) ]
   |         - Symmetric Ciphers (AES, Fernet)
   |         - Asymmetric Ciphers (RSA)
   |         - Key Derivation (PBKDF2)
   |         - Digital Signatures (PSS)
   |
   +-----> [ src/backup.py (Backup & Pruning Engine) ]
   |         - Archive Creation (zip, tar.gz)
   |         - Filesystem Mirroring
   |         - Automated Pruning Logic
   |
   +-----> [ src/utils.py (Helpers) ]
   |         - Secure Password Input
   |         - Logger Configuration
   |
   +-----> [ config.ini (Configuration File) ]
   |
   +-----> [ logs/security.log (Audit Trail) ]
```

### 2.2 File & Folder Structure

The toolkit is organized into a modular structure within the `security/` directory:

-   `/run.py`: The single, user-facing entry point. Its sole purpose is to locate the project root, set up the Python path, and invoke the correct function within `manager.py`.
-   `/config.ini`: A plain-text configuration file that controls the toolkit's behavior without requiring code modification.
-   `/requirements.txt`: A list of all Python dependencies (`cryptography`, `pyarmor`).
-   `/src/`: A directory containing all the core application logic.
    -   `manager.py`: The heart of the application. Contains the main menu loop, orchestrates all high-level workflows (encryption, decryption, etc.), and calls other modules to perform specific tasks.
    -   `core.py`: The cryptography engine. Contains all low-level functions for encryption, decryption, key derivation, and digital signatures. It has no knowledge of files or user interaction.
    -   `backup.py`: The backup engine. Manages the creation of zip, tar.gz, and mirror backups, and contains the logic for pruning old backups.
    -   `utils.py`: A collection of helper functions, primarily for setting up the rotating file logger and handling secure password input.
-   `/logs/`: Contains the `security.log` file, which provides a detailed, timestamped audit trail of all operations.
-   **Cryptographic Artifacts:**
    -   `salt.bin`: A public, non-secret value used to prevent rainbow table attacks on the encryption password.
    -   `private_key.pem`: The user's RSA private key. **This is the most critical file for integrity.** It is itself encrypted with a user-provided password.
    -   `public_key.pem`: The corresponding RSA public key, used to verify signatures. This file is deleted after being encrypted into the project payload.

---

## 3.0 Cryptographic Implementation Deep Dive

### 3.1 Password-Based Key Derivation (PBKDF2)

-   **Technology:** `PBKDF2HMAC` with `SHA256`.
-   **Purpose:** A user's password is not suitable to be used directly as an encryption key. PBKDF2 is a standard algorithm that takes a password, a `salt`, and an iteration count to produce a cryptographically strong key of a desired length.
-   **Implementation (`core.py` -> `derive_key`):
    1.  The user's password (e.g., "`my-password`") is taken as input.
    2.  The public `salt.bin` is read from disk.
    3.  These are fed into the PBKDF2 function, which performs hundreds of thousands of hashing rounds.
    4.  **Result:** A secure, 32-byte key that is used for the symmetric encryption of files.
-   **Security Benefit:** The salt ensures that two users with the same password will have different encryption keys. The high iteration count makes brute-force attacks (trying millions of passwords) extremely slow and computationally expensive.

### 3.2 Symmetric Encryption (Confidentiality)

This layer protects the file contents ("what"). It is used to encrypt and decrypt the actual source code.

-   **Level 1 (Standard): `Fernet`**
    -   **Technology:** AES-128 in CBC mode with a SHA-256 HMAC signature.
    -   **How it Works:** The 32-byte derived key is used by the Fernet algorithm to encrypt data. Fernet automatically handles initialization vectors (IVs) and signs the ciphertext to prevent tampering.
    -   **Pros:** Fast, secure, and reliable. An industry standard for symmetric encryption.
-   **Level 2 (Paranoid): `AES-256-GCM`**
    -   **Technology:** AES with a 256-bit key in Galois/Counter Mode (GCM).
    -   **How it Works:** This is an Authenticated Encryption with Associated Data (AEAD) mode. It encrypts the data and simultaneously computes an authentication tag. This tag can only be generated with the key and ensures both confidentiality and authenticity.
    -   **Pros:** Considered a top-tier standard in modern cryptography. Offers a higher key strength (256-bit vs. 128-bit) and integrated authentication.

### 3.3 Asymmetric Cryptography & Digital Signatures (Integrity & Authenticity)

This layer protects the manifest file, proving who created it ("who") and that it hasn't changed.

-   **Technology:** RSA-4096 with PSS padding and SHA-256 hashing.
-   **Key Pair (`private_key.pem`, `public_key.pem`):
    -   The **private key** is the secret signing key. It is stored in PEM format and is encrypted using the `BestAvailableEncryption` standard from the `cryptography` library, protected by a user-provided password. It is mathematically linked to the public key.
    -   The **public key** can verify signatures made by the private key, but cannot create them. It is not secret.
-   **Signing Process (`core.py` -> `sign_data`):
    1.  The content of the `manifest.sha256` file is read.
    2.  The user provides their password to decrypt and load the `private_key.pem` into memory.
    3.  The private key is used to generate a unique digital signature of the manifest data.
    4.  This signature is saved to `manifest.sha256.sig`.
-   **Verification Process (`core.py` -> `verify_signature`):
    1.  The `public_key.pem` is loaded.
    2.  The signature from `manifest.sha256.sig` is loaded.
    3.  The content of the `manifest.sha256` file is read.
    4.  The verification function checks if the signature corresponds to the manifest data and the public key. It returns `True` only if they match perfectly.

---

### 3.4 Core Security Concepts: The Lockbox Analogy

This section provides a high-level, conceptual overview of how the core cryptographic components work together. Think of it like securing a valuable package in a locked box that has a special tamper-proof seal.

#### 3.4.1 The Salt (`salt.bin`) - Making Your Lock Stronger
-   **What it is:** A random, unique value.
-   **Its Purpose:** The salt is mixed with your password *before* the actual encryption key is created.
-   **Analogy:** Imagine your password is a simple key. The salt is like a special adapter that changes the shape of your key before it goes into the lock. Even if someone has an identical key (the same password), they can't open your lock without your unique adapter (the salt).
-   **Why it's important:** It protects against "rainbow table" or "dictionary" attacks. If two people used the exact same password (`password123`), the salt ensures that their final encryption keys would be completely different. It's not a secret, but it's essential for turning a memorable password into a strong, unique encryption key.

#### 3.4.2 The PEM Files (`private_key.pem`, `public_key.pem`) - Your Unique Seal and Inspector
These two files work as a pair for **digital signatures**. They prove that the files haven't been tampered with.

-   **`private_key.pem` (Your Personal Seal):**
    -   **What it is:** This is your secret, digital signature stamp. It's protected by the password you created.
    -   **Its Purpose:** When you encrypt the project, the toolkit uses this private key to "sign" a list of all your files (the manifest). This creates a unique, verifiable seal.
    -   **Security:** This file is **CRITICAL**. You must **NEVER** share it. It's the "second factor" of your security—you need both the password AND this key to decrypt.

-   **`public_key.pem` (The Seal Inspector):**
    -   **What it is:** A key that is mathematically linked to your private key, but cannot be used to create your signature.
    -   **Its Purpose:** It's used to *verify* that a signature created by your private key is authentic.
    -   **Security:** This key is safe to be included with the encrypted project. It can only check a seal; it can't create one.

#### 3.4.3 The Signature (`manifest.sha256.sig`) - The Tamper-Proof Seal Itself
-   **What it is:** The output file created when your `private_key.pem` signs the project manifest.
-   **How it works:**
    1.  **Hashing:** When you choose to encrypt, the system first scans all your project files and creates a unique fingerprint (a SHA256 hash) for each one. This list of fingerprints is saved in `manifest.sha256`.
    2.  **Signing (Creating the Seal):** The system then takes your `private_key.pem` and your password to create the digital signature (`manifest.sha256.sig`) from the manifest file.
    3.  **Verification (Checking the Seal):** When you decrypt, the first thing the system does is use the `public_key.pem` to check if the signature is valid for the manifest. If it is, it then verifies the hashes of the individual files.

#### Summary: The Complete Workflow
-   **Encrypt:** Your **Password** + **Salt** creates the key to lock the files. Your **Private Key** signs the file list to create a seal.
-   **Decrypt:** Your **Password** + **Salt** re-creates the key to unlock the files. The **Public Key** checks the seal to make sure nothing has changed before opening the box.

## 4.0 The Chain of Trust: Workflow Analysis

### 4.1 Encryption Workflow

This is the sequence of events when the user selects "Encrypt Project".

1.  **Collect Hashes:** The manager traverses the project directory (respecting exclusions in `config.ini`) and calculates a SHA-256 hash for each file that will be encrypted. These are stored in memory.
2.  **Create Manifest:** The collected hashes and their relative file paths are written into the `manifest.sha256` file.
3.  **Sign Manifest:** The user is prompted for their **private key password**. The private key is loaded, and it creates a digital signature of the manifest file, saved as `manifest.sha256.sig`.
4.  **Derive Encryption Key:** The user is prompted for their **encryption password**. This password and the `salt.bin` are used to derive the symmetric encryption key via PBKDF2.
5.  **Encrypt Project Files:** The manager iterates through the files again, reading the plaintext, encrypting it in memory using the derived key, and overwriting the original file with the ciphertext.
6.  **Encrypt Integrity Artifacts:** The `manifest.sha256.sig` and `public_key.pem` files are now themselves encrypted using the same derived key. Their original, plaintext versions are securely deleted.

### 4.2 Decryption Workflow (The Mandatory Check)

This sequence is the core of the tamper-evident design.

1.  **Check for Artifacts:** The manager first checks for the existence of the encrypted signature and public key files. If they are missing, the process aborts with a critical error.
2.  **Derive Encryption Key:** The user provides the **encryption password** to derive the symmetric key.
3.  **Decrypt Artifacts:** The system decrypts *only* the signature and public key files into memory.
4.  **Verify Signature:** The public key is used to verify the signature of the on-disk `manifest.sha256` file. If verification fails (meaning the manifest was altered), the process aborts.
5.  **Verify Hashes:** If the signature is valid, the system proceeds to read the trusted manifest. It then traverses the project, calculating the SHA-256 hash of each encrypted file and comparing it to the value in the manifest. If any hash does not match, the process aborts.
6.  **Decrypt Project Files:** Only if all previous checks pass, the system finally proceeds to decrypt all the project source files.

---

## 5.0 Backup & Recovery System

### 5.1 Backup Formats

-   **`.zip`:** A single, compressed archive. Chosen for its universal compatibility across all operating systems.
-   **`.tar.gz`:** A compressed tarball. Standard in Linux/macOS environments, preserving file permissions more effectively.
-   **`mirror`:** A direct, uncompressed copy of the entire project tree. Useful for quick access to a single file without needing to decompress an entire archive.

### 5.2 Hidden vs. Visible Backups

-   **Mechanism:** On Windows (`os.name == 'nt'`), the `backup.py` script uses the `ctypes` library to interact directly with the Windows Kernel32 API.
-   **Implementation:** After the `.zip` and `.tar.gz` files are created, the script calls `ctypes.windll.kernel32.SetFileAttributesW()` with the `FILE_ATTRIBUTE_HIDDEN` flag (value `0x02`). This makes the files hidden in File Explorer by default, reducing clutter. The mirror backup is always left visible.

### 5.3 Automated Pruning

-   **Logic:** When backups are created or pruning is run manually, the `prune_backups` function is called.
-   **Process:**
    1.  It reads `retention_days` and `retention_count` from `config.ini`.
    2.  It lists all items in the `backups` directory and groups them by type (zip, tar, mirror).
    3.  For each group, it sorts the backups by date, newest first.
    4.  It automatically keeps the newest `retention_count` backups, regardless of their age.
    5.  For the remaining older backups, it deletes any that are older than `retention_days`.

---

## 6.0 Security Analysis

This architecture is designed to mitigate specific threats:

-   **Threat:** Attacker gains filesystem access and modifies an encrypted file.
    -   **Mitigation:** The hash of the modified file will not match the hash in the manifest. The mandatory pre-decryption check will fail. Access denied.
-   **Threat:** Attacker modifies an encrypted file AND modifies the manifest to match the new hash.
    -   **Mitigation:** The manifest's digital signature will now be invalid. The pre-decryption check will fail. Access denied.
-   **Threat:** Attacker modifies a file, the manifest, and tries to sign it with their own key.
    -   **Mitigation:** The signature will not be verifiable with the project's original public key. The check will fail. Furthermore, the original public key is encrypted as part of the payload, so it cannot be easily replaced. Access denied.
-   **Threat:** Accidental deletion of recent backups.
    -   **Mitigation:** The pruning system's `retention_count` ensures a minimum number of recent backups are always preserved.
-   **Threat:** Accidental self-destruction.
    -   **Mitigation:** The self-destruct mechanism is now cross-platform (`shutil.rmtree`) and requires the user to type the full, absolute path of the project directory as confirmation, making it extremely difficult to trigger by accident.

---

## 7.0 Future Enhancements & System Hardening

The v3.0 architecture is designed to be robust and resilient. However, security is an ongoing process of improvement. This section outlines potential future enhancements to address the theoretical limits of the current software-only implementation.

### 7.1 Tier 1: Software-Level Hardening

These enhancements can be implemented directly within the existing Python codebase.

1.  **Secure File Wipe Utility:**
    *   **Weakness:** Standard `os.remove` deletes a file's index pointer, but the data blocks on disk remain recoverable by forensic tools until overwritten by the OS. This poses a risk for the ephemeral plaintext files created during the signing process (e.g., `manifest.sha256.sig`).
    *   **Enhancement:** Implement a `secure_wipe` function that overwrites a file with multiple passes of random data and zeros before deletion. This "shreds" the file, making forensic recovery practically impossible.
    *   **Impact:** Closes a physical data remanence vulnerability.

2.  **Password Strength Meter & Policy Enforcement:**
    *   **Weakness:** The system's security is anchored by user-provided passwords. Weak passwords present a significant risk.
    *   **Enhancement:** Integrate a library like `zxcvbn-python` to provide real-time feedback on password strength during creation. Enforce a minimum strength score, configurable in `config.ini`, preventing the use of weak or easily guessable passwords.
    *   **Impact:** Directly mitigates the most significant risk vector: the human factor.

### 7.2 Tier 2: Environment-Level Hardening (Hardware Security)

This involves leveraging specialized hardware to move beyond the limitations of a software-only environment.

1.  **Hardware Security Module (HSM) / Trusted Platform Module (TPM) Integration:**
    *   **Weakness:** The encrypted private key, while secure, still exists as a file on disk. A fully compromised host OS with a keylogger could potentially capture the password and the key file.
    *   **Enhancement:** Abstract the signing process to use a hardware module. The private key would be generated and stored *inside* the HSM or TPM chip and would be physically incapable of leaving it. Data to be signed would be sent to the chip, and only the resulting signature would be returned to the OS.
    *   **Impact:** Provides the ultimate protection for the signing key, making it immune to theft even on a compromised system. This is the gold standard for high-security environments.

### 7.3 Tier 3: Process-Level Hardening

This focuses on the development and operational lifecycle of the toolkit itself.

1.  **Dependency Vulnerability Auditing:**
    *   **Weakness:** The toolkit relies on third-party libraries (e.g., `cryptography`). A vulnerability discovered in a dependency could compromise the entire system (a supply chain attack).
    *   **Enhancement:** Add a new menu option to run a dependency audit using a tool like `pip-audit`. This tool checks all installed packages against a database of known vulnerabilities.
    *   **Impact:** Provides proactive defense against supply chain attacks, allowing the user to be informed of risks within their software environment.

---

## 8.0 Disaster Recovery & Resilience Scenarios

This section outlines how to recover from data loss or tampering. The ability to "win" depends entirely on having a disciplined backup strategy.

**The Golden Rule:** The integrated backup system is your primary recovery tool. The **3-2-1 Backup Rule** (3 copies, 2 different media, 1 off-site) described in the `README.md` is your ultimate safeguard against catastrophic failure.

--- 

**Scenario 1: A single encrypted source file is deleted or corrupted.**

*   **Detection:** The `Decrypt Project` command will fail with a `[CORRUPT]` or `[TAMPERED]` error during the mandatory integrity check.
*   **How to Win:** 
    1.  Do not proceed. Identify the missing/corrupted file from the error message.
    2.  Navigate to your `backups` folder.
    3.  Open the most recent `mirror` backup folder.
    4.  Copy the pristine version of the affected file from the mirror backup and paste it into the correct location in your live project, overwriting the corrupted one.
    5.  Run the `Decrypt Project` command again. The integrity check should now pass.

--- 

**Scenario 2: Critical security artifacts are deleted (e.g., `salt.bin`, `private_key.pem`).**

*   **Detection:** The application will fail immediately on startup or during a specific operation (e.g., encryption will fail without the private key).
*   **Problem:** The system is in a broken state. Without the salt, new keys cannot be derived. Without the private key, new signatures cannot be created.
*   **How to Win:** This is a critical failure that must be resolved with a full backup.
    1.  Delete the entire corrupted `security` folder from your project.
    2.  Restore the `security` folder from your most recent `mirror` or archive (`.zip`, `.tar.gz`) backup.
    3.  The restored folder will contain the correct, missing artifacts, and the system will be functional again.

--- 

**Scenario 3: The user forgets the *Encryption/Decryption* password.**

*   **Detection:** The decryption process will fail with a cryptographic error (specifically, an `InvalidToken` or similar error from the `cryptography` library).
*   **Problem:** This is a catastrophic data loss scenario. By design, there is no backdoor. The password is required to derive the key.
*   **How to Win (Partial Victory):**
    *   **If you have a plaintext backup:** Your only hope is if you have an old backup of the project from *before* it was encrypted. This is not the recommended workflow, but it is a recovery option.
    *   **If all backups are encrypted:** The data is permanently and irrecoverably lost. This underscores that password management is the most critical user responsibility.

--- 

**Scenario 4: The user forgets the *Private Key* password.**

*   **Detection:** You will be able to decrypt the project (as this uses the other password), but the `Encrypt Project` command will fail because it cannot load the private key to sign the new manifest.
*   **Problem:** You can access your code, but you cannot secure it again using the existing integrity chain.
*   **How to Win (Full Code Recovery):**
    1.  **Decrypt the project** one last time to get all your source code in plaintext. Copy the decrypted source code to a safe, separate location.
    2.  Run the command `python run.py generate-keys`.
    3.  Provide a **new** private key password you will remember. This creates a new `private_key.pem` and `public_key.pem`, breaking the old integrity chain.
    4.  You can now run `Encrypt Project` again. It will use the new private key to sign the manifest and secure your project.
    5.  **Result:** You have successfully recovered 100% of your code and re-established security. You have lost the historical integrity chain, but that is a small price to pay for full recovery.

--- 

**Scenario 5: Catastrophic hardware failure (entire project and local `backups` folder are destroyed).**

*   **Detection:** The hardware is gone.
*   **Problem:** Total local data loss.
*   **How to Win:** This is the scenario that validates the **3-2-1 Backup Rule.**
    1.  Acquire new hardware.
    2.  Retrieve your latest **off-site backup** (from your external hard drive, cloud storage, etc.).
    3.  Restore the backup to your new machine.
    4.  The entire project, including the `security` toolkit and all necessary key files, will be restored. You can resume work exactly where your last off-site backup left off.
