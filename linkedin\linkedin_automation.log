2025-06-25 22:37:20,807 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApply<PERSON><PERSON> initialized with advanced features
2025-06-25 22:37:20,809 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApply<PERSON>ro initialized with advanced features
2025-06-25 22:37:20,809 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:37:20,810 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApply<PERSON><PERSON> initialized with advanced features
2025-06-25 22:37:20,810 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:37:20,810 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApply<PERSON><PERSON> initialized with advanced features
2025-06-25 22:38:09,377 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:38:09,379 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:38:09,379 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:40:07,673 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:40:07,675 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-25 22:40:07,675 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:25:00,029 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:25:00,075 - LinkedInEasyApplyPro - INFO - run_professional_automation:894 - Target: 40 applications for 'data scientist' in 'Remote'
2025-06-26 06:25:00,075 - LinkedInEasyApplyPro - INFO - setup:840 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:25:02,867 - LinkedInEasyApplyPro - INFO - setup:882 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:25:08,047 - LinkedInEasyApplyPro - INFO - login_with_stealth:1072 - Entering credentials with human-like behavior...
2025-06-26 06:25:18,501 - LinkedInEasyApplyPro - ERROR - login_with_stealth:1103 - Login failed - redirected to: https://www.linkedin.com/checkpoint/challenge/AgHmOwhDvpC5XwAAAZepu0uqwthQXf3QOTxXwN2RehAlAoK_xg7vLVL9HR0AdcJ7BogQs3IGlBC_Vt4Izb6TaQvWjJzwfw?ut=0asPduOGXnhHQ1
2025-06-26 06:25:18,502 - LinkedInEasyApplyPro - ERROR - run_professional_automation:917 - Failed to login
2025-06-26 06:25:40,536 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:25:40,541 - LinkedInEasyApplyPro - INFO - run_professional_automation:894 - Target: 30 applications for 'data scientist' in 'Remote'
2025-06-26 06:25:40,541 - LinkedInEasyApplyPro - INFO - setup:840 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:25:41,754 - LinkedInEasyApplyPro - INFO - setup:882 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:25:47,417 - LinkedInEasyApplyPro - INFO - login_with_stealth:1072 - Entering credentials with human-like behavior...
2025-06-26 06:25:59,982 - LinkedInEasyApplyPro - ERROR - login_with_stealth:1103 - Login failed - redirected to: https://www.linkedin.com/checkpoint/challenge/AgFLkweyYdJ_xgAAAZepu-ivFkfdrG55Cbvldsj9gutvUsRFwcaRnnqbA7l2hQdrjNzbI1DTYB-7D2-5e-o0DvZtXU_cXQ?ut=1VU8ooOufnhHQ1
2025-06-26 06:25:59,982 - LinkedInEasyApplyPro - ERROR - run_professional_automation:917 - Failed to login
2025-06-26 06:27:25,450 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:27:25,482 - LinkedInEasyApplyPro - INFO - run_professional_automation:894 - Target: 50 applications for 'ai engineer' in 'india'
2025-06-26 06:27:25,482 - LinkedInEasyApplyPro - INFO - setup:840 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:27:27,168 - LinkedInEasyApplyPro - INFO - setup:882 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:27:32,733 - LinkedInEasyApplyPro - INFO - login_with_stealth:1072 - Entering credentials with human-like behavior...
2025-06-26 06:27:45,413 - LinkedInEasyApplyPro - ERROR - login_with_stealth:1103 - Login failed - redirected to: https://www.linkedin.com/checkpoint/challenge/AgFFqj4XPx-wvgAAAZepvYU7GBcpvIOJzlhHjTQHy23jA4i7gX3yHagb0wM-iz6RsjJFb3kR5TFv0ODxN5FykDvZN_VJIg?ut=2SdnQYTp3phHQ1
2025-06-26 06:27:45,415 - LinkedInEasyApplyPro - ERROR - run_professional_automation:917 - Failed to login
2025-06-26 06:31:49,340 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:31:49,342 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 06:31:49,343 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'ai engineer' in 'inida'
2025-06-26 06:31:49,343 - LinkedInEasyApplyPro - INFO - setup:875 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:32:02,170 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:32:02,172 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 06:32:02,172 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'ai engineer' in 'india'
2025-06-26 06:32:02,172 - LinkedInEasyApplyPro - INFO - setup:875 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:32:04,192 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:32:04,193 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 06:32:10,380 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 06:32:30,021 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 06:32:30,021 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 06:32:30,022 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 06:32:47,002 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 06:32:47,010 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1179 - 🔍 Searching for 'ai engineer' jobs with intelligent filtering...
2025-06-26 06:33:06,329 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1204 - ✅ Search results loaded successfully
2025-06-26 06:33:06,333 - LinkedInEasyApplyPro - INFO - run_professional_automation:972 - 📄 Processing page 1
2025-06-26 06:33:06,336 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1216 - 🔍 Finding and analyzing job cards...
2025-06-26 06:33:17,086 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:33:27,167 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:33:37,232 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:33:47,344 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:33:56,355 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:33:56,356 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 06:33:56,357 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data science' in 'india'
2025-06-26 06:33:56,357 - LinkedInEasyApplyPro - INFO - setup:875 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:33:57,452 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:33:59,221 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:33:59,222 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 06:34:07,621 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:34:07,937 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 06:34:17,765 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:34:26,157 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 06:34:26,158 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 06:34:26,158 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 06:34:27,853 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:34:38,058 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:34:40,448 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 06:34:40,450 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1179 - 🔍 Searching for 'data science' jobs with intelligent filtering...
2025-06-26 06:34:48,377 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:34:53,455 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1204 - ✅ Search results loaded successfully
2025-06-26 06:34:53,455 - LinkedInEasyApplyPro - INFO - run_professional_automation:972 - 📄 Processing page 1
2025-06-26 06:34:53,456 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1216 - 🔍 Finding and analyzing job cards...
2025-06-26 06:34:58,492 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:03,582 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:08,563 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:13,613 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:18,654 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:18,655 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1237 - 📊 Analyzed 0 job cards
2025-06-26 06:35:18,655 - LinkedInEasyApplyPro - WARNING - run_professional_automation:978 - No job cards found on current page
2025-06-26 06:35:18,958 - LinkedInEasyApplyPro - INFO - navigate_to_next_page:1825 - 📄 No more pages available
2025-06-26 06:35:18,959 - LinkedInEasyApplyPro - INFO - run_professional_automation:1063 - 🎯 Professional Automation Session Complete!
2025-06-26 06:35:18,959 - LinkedInEasyApplyPro - INFO - run_professional_automation:1064 - 📊 Results: 0/0 applications successful
2025-06-26 06:35:18,959 - LinkedInEasyApplyPro - INFO - run_professional_automation:1065 - 📈 Success Rate: 0.00%
2025-06-26 06:35:18,959 - LinkedInEasyApplyPro - INFO - run_professional_automation:1066 - ⏱️ Session Duration: 3.3 minutes
2025-06-26 06:35:21,529 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:35:23,690 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:33,734 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:37,883 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:35:43,797 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:35:53,827 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:36:03,933 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:36:03,934 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1237 - 📊 Analyzed 0 job cards
2025-06-26 06:36:03,935 - LinkedInEasyApplyPro - WARNING - run_professional_automation:978 - No job cards found on current page
2025-06-26 06:36:24,121 - LinkedInEasyApplyPro - INFO - navigate_to_next_page:1825 - 📄 No more pages available
2025-06-26 06:36:24,121 - LinkedInEasyApplyPro - INFO - run_professional_automation:1063 - 🎯 Professional Automation Session Complete!
2025-06-26 06:36:24,122 - LinkedInEasyApplyPro - INFO - run_professional_automation:1064 - 📊 Results: 0/0 applications successful
2025-06-26 06:36:24,122 - LinkedInEasyApplyPro - INFO - run_professional_automation:1065 - 📈 Success Rate: 0.00%
2025-06-26 06:36:24,122 - LinkedInEasyApplyPro - INFO - run_professional_automation:1066 - ⏱️ Session Duration: 2.5 minutes
2025-06-26 06:36:26,520 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:36:42,870 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:41:37,418 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 06:41:37,419 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 06:41:37,419 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data science' in 'india'
2025-06-26 06:41:37,421 - LinkedInEasyApplyPro - INFO - setup:875 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 06:41:40,312 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 06:41:40,313 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 06:41:44,285 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 06:41:57,696 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 06:41:57,697 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 06:41:57,697 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 06:42:10,478 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 06:42:10,479 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1179 - 🔍 Searching for 'data science' jobs with intelligent filtering...
2025-06-26 06:42:23,215 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1204 - ✅ Search results loaded successfully
2025-06-26 06:42:23,216 - LinkedInEasyApplyPro - INFO - run_professional_automation:972 - 📄 Processing page 1
2025-06-26 06:42:23,216 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1216 - 🔍 Finding and analyzing job cards...
2025-06-26 06:42:33,355 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:42:43,527 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:42:53,846 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:43:05,176 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:43:16,228 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:43:26,486 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:43:36,581 - LinkedInEasyApplyPro - WARNING - extract_job_data_from_card:1272 - Error extracting job data: Message: no such element: Unable to locate element: {"method":"css selector","selector":".job-card-container__company-name, .job-card-list__subtitle"}
  (Session info: chrome=137.0.7151.122); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception
Stacktrace:
	GetHandleVerifier [0x0x7ff6b48ecda5+78885]
	GetHandleVerifier [0x0x7ff6b48ece00+78976]
	(No symbol) [0x0x7ff6b46a9bca]
	(No symbol) [0x0x7ff6b4700766]
	(No symbol) [0x0x7ff6b4700a1c]
	(No symbol) [0x0x7ff6b46f303c]
	(No symbol) [0x0x7ff6b4728bcf]
	(No symbol) [0x0x7ff6b46f2f06]
	(No symbol) [0x0x7ff6b4728da0]
	(No symbol) [0x0x7ff6b475122f]
	(No symbol) [0x0x7ff6b4728963]
	(No symbol) [0x0x7ff6b46f16b1]
	(No symbol) [0x0x7ff6b46f2443]
	GetHandleVerifier [0x0x7ff6b4bc4eed+3061101]
	GetHandleVerifier [0x0x7ff6b4bbf33d+3037629]
	GetHandleVerifier [0x0x7ff6b4bde592+3165202]
	GetHandleVerifier [0x0x7ff6b490730e+186766]
	GetHandleVerifier [0x0x7ff6b490eb3f+217535]
	GetHandleVerifier [0x0x7ff6b48f59b4+114740]
	GetHandleVerifier [0x0x7ff6b48f5b69+115177]
	GetHandleVerifier [0x0x7ff6b48dc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 06:43:36,582 - LinkedInEasyApplyPro - INFO - find_and_analyze_jobs:1237 - 📊 Analyzed 0 job cards
2025-06-26 06:43:36,583 - LinkedInEasyApplyPro - WARNING - run_professional_automation:978 - No job cards found on current page
2025-06-26 06:43:56,684 - LinkedInEasyApplyPro - INFO - navigate_to_next_page:1825 - 📄 No more pages available
2025-06-26 06:43:56,684 - LinkedInEasyApplyPro - INFO - run_professional_automation:1063 - 🎯 Professional Automation Session Complete!
2025-06-26 06:43:56,685 - LinkedInEasyApplyPro - INFO - run_professional_automation:1064 - 📊 Results: 0/0 applications successful
2025-06-26 06:43:56,685 - LinkedInEasyApplyPro - INFO - run_professional_automation:1065 - 📈 Success Rate: 0.00%
2025-06-26 06:43:56,685 - LinkedInEasyApplyPro - INFO - run_professional_automation:1066 - ⏱️ Session Duration: 2.3 minutes
2025-06-26 06:43:58,992 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:44:15,340 - LinkedInEasyApplyPro - INFO - cleanup:1837 - 🧹 Browser cleanup completed
2025-06-26 06:47:57,189 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 07:00:06,044 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 07:00:06,047 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 07:00:06,047 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data science' in 'india'
2025-06-26 07:00:06,048 - LinkedInEasyApplyPro - INFO - setup:875 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 07:00:08,009 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 07:00:08,009 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 07:00:13,565 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 07:00:27,277 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 07:00:27,278 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 07:00:27,278 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 07:00:41,409 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 07:00:41,409 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1179 - 🔍 Searching for 'data science' jobs with intelligent filtering...
2025-06-26 07:00:58,457 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1204 - ✅ Search results loaded successfully
2025-06-26 07:00:58,457 - LinkedInEasyApplyPro - INFO - run_professional_automation:972 - 📄 Processing page 1
2025-06-26 07:00:58,458 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 07:00:58,547 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 07:17:34,889 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [CHART] Successfully analyzed 19 job cards
2025-06-26 07:17:34,890 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,892 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for REF73478J_Python Fullstack Developer (2-3 yrs)- Senior Program Analyst - IT
REF73478J_Python Fullstack Developer (2-3 yrs)- Senior Program Analyst - IT with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,893 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: REF73478J_Python Fullstack Developer (2-3 yrs)- Senior Program Analyst - IT
REF73478J_Python Fullstack Developer (2-3 yrs)- Senior Program Analyst - IT with verification (Low match score)
2025-06-26 07:17:34,893 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,893 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Leadership Roles with High-Growth Founders (CEO, VP, Director)
Leadership Roles with High-Growth Founders (CEO, VP, Director): SKIP (Score: -0.20)
2025-06-26 07:17:34,894 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Leadership Roles with High-Growth Founders (CEO, VP, Director)
Leadership Roles with High-Growth Founders (CEO, VP, Director) (Low match score)
2025-06-26 07:17:34,894 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,894 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Business Intelligence Specialist
Business Intelligence Specialist with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,895 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Business Intelligence Specialist
Business Intelligence Specialist with verification (Low match score)
2025-06-26 07:17:34,895 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,895 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Physics Teacher
Physics Teacher: SKIP (Score: -0.20)
2025-06-26 07:17:34,896 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Physics Teacher
Physics Teacher (Low match score)
2025-06-26 07:17:34,897 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,897 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Senior Data Engineer
Senior Data Engineer: SKIP (Score: -0.20)
2025-06-26 07:17:34,897 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Senior Data Engineer
Senior Data Engineer (Low match score)
2025-06-26 07:17:34,897 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,898 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Anaplan Consultant
Anaplan Consultant: SKIP (Score: -0.20)
2025-06-26 07:17:34,898 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Anaplan Consultant
Anaplan Consultant (Low match score)
2025-06-26 07:17:34,898 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,899 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Senior Software Engineer - Machine Learning
Senior Software Engineer - Machine Learning with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,899 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Senior Software Engineer - Machine Learning
Senior Software Engineer - Machine Learning with verification (Low match score)
2025-06-26 07:17:34,899 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,900 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Freelance GenAI Developer - Prompt Engineering & Data Workflows
Freelance GenAI Developer - Prompt Engineering & Data Workflows: SKIP (Score: -0.20)
2025-06-26 07:17:34,900 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Freelance GenAI Developer - Prompt Engineering & Data Workflows
Freelance GenAI Developer - Prompt Engineering & Data Workflows (Low match score)
2025-06-26 07:17:34,900 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,900 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Business Analyst
Business Analyst: SKIP (Score: -0.20)
2025-06-26 07:17:34,900 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Business Analyst
Business Analyst (Low match score)
2025-06-26 07:17:34,902 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,902 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for System Administrator
System Administrator: SKIP (Score: -0.20)
2025-06-26 07:17:34,903 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: System Administrator
System Administrator (Low match score)
2025-06-26 07:17:34,903 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,904 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Field Representative
Field Representative: SKIP (Score: -0.20)
2025-06-26 07:17:34,904 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Field Representative
Field Representative (Low match score)
2025-06-26 07:17:34,904 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,904 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Senior Software Engineer
Senior Software Engineer: SKIP (Score: -0.20)
2025-06-26 07:17:34,905 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Senior Software Engineer
Senior Software Engineer (Low match score)
2025-06-26 07:17:34,905 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,905 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Sr. Executive- Lidar
Sr. Executive- Lidar with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,905 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Sr. Executive- Lidar
Sr. Executive- Lidar with verification (Low match score)
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Data Engineer
Data Engineer: SKIP (Score: -0.20)
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Data Engineer
Data Engineer (Low match score)
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Back End Developer
Back End Developer: SKIP (Score: -0.20)
2025-06-26 07:17:34,906 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Back End Developer
Back End Developer (Low match score)
2025-06-26 07:17:34,907 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,907 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Python Software Developer
Python Software Developer: SKIP (Score: -0.20)
2025-06-26 07:17:34,907 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Python Software Developer
Python Software Developer (Low match score)
2025-06-26 07:17:34,907 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,907 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Data Engineer
Data Engineer with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,908 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Data Engineer
Data Engineer with verification (Low match score)
2025-06-26 07:17:34,908 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,908 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Senior AWS Data Engineer
Senior AWS Data Engineer with verification: SKIP (Score: -0.20)
2025-06-26 07:17:34,909 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Senior AWS Data Engineer
Senior AWS Data Engineer with verification (Low match score)
2025-06-26 07:17:34,909 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1423 - Error in AI job analysis: 'LinkedInEasyApplyPro' object has no attribute 'click_element_robust'
2025-06-26 07:17:34,909 - LinkedInEasyApplyPro - INFO - should_apply_intelligently:1456 - 🎯 Decision for Data Engineer
Data Engineer: SKIP (Score: -0.20)
2025-06-26 07:17:34,910 - LinkedInEasyApplyPro - INFO - run_professional_automation:997 - ⏭️ Skipping job: Data Engineer
Data Engineer (Low match score)
2025-06-26 07:17:35,054 - LinkedInEasyApplyPro - INFO - navigate_to_next_page:1989 - 📄 No more pages available
2025-06-26 07:17:35,055 - LinkedInEasyApplyPro - INFO - run_professional_automation:1063 - 🎯 Professional Automation Session Complete!
2025-06-26 07:17:35,055 - LinkedInEasyApplyPro - INFO - run_professional_automation:1064 - 📊 Results: 0/0 applications successful
2025-06-26 07:17:35,055 - LinkedInEasyApplyPro - INFO - run_professional_automation:1065 - 📈 Success Rate: 0.00%
2025-06-26 07:17:35,055 - LinkedInEasyApplyPro - INFO - run_professional_automation:1066 - ⏱️ Session Duration: 17.5 minutes
2025-06-26 07:17:37,409 - LinkedInEasyApplyPro - INFO - cleanup:2001 - 🧹 Browser cleanup completed
2025-06-26 07:17:53,899 - LinkedInEasyApplyPro - INFO - cleanup:2001 - 🧹 Browser cleanup completed
2025-06-26 10:05:31,163 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:09:39,316 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:09:39,317 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:09:39,317 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data scientist' in 'india'
2025-06-26 10:09:39,318 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:09:43,725 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:09:43,725 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:09:53,254 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:10:10,734 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 10:10:10,735 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 10:10:10,735 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 10:10:25,690 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 10:10:25,690 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data scientist' jobs with intelligent filtering...
2025-06-26 10:10:35,690 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 10:10:35,690 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 10:10:35,691 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 10:10:35,746 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 10:13:14,553 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:14:13,324 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:14:13,325 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:14:13,325 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data scientist' in 'india'
2025-06-26 10:14:13,326 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:14:15,136 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:14:15,139 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:14:23,064 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:14:39,176 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 10:14:39,176 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 10:14:39,177 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 10:14:49,440 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 10:14:49,440 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data scientist' jobs with intelligent filtering...
2025-06-26 10:15:00,844 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 10:15:00,844 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 10:15:00,845 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 10:15:00,925 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 10:20:05,581 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:20:05,582 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:20:05,582 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data scientist' in 'india'
2025-06-26 10:20:05,582 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:20:07,647 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:20:07,647 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:20:23,011 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:20:41,535 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 10:20:41,536 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 10:20:41,536 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 10:21:37,504 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 10:21:37,508 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data scientist' jobs with intelligent filtering...
2025-06-26 10:22:05,251 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 10:22:05,252 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 10:22:05,253 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 10:22:05,336 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 10:27:57,754 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:27:57,759 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:27:57,764 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 3 applications for 'data analyst' in 'Remote'
2025-06-26 10:27:57,766 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:28:00,845 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:28:00,847 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:28:00,849 - LinkedInEasyApplyPro - INFO - _safe_log:845 - No credentials found, prompting for input...
2025-06-26 10:28:22,260 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:30:27,504 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:30:27,506 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:30:27,506 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'india'
2025-06-26 10:30:27,507 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:30:29,721 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:30:29,721 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:30:37,753 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:30:55,563 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 10:30:55,563 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 10:30:55,564 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 10:31:25,145 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 10:31:25,161 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 10:32:00,335 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 10:32:00,335 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 10:32:00,335 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 10:32:01,160 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 10:54:18,476 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 10:54:18,477 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 10:54:18,477 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'india'
2025-06-26 10:54:18,478 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 10:54:20,632 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 10:54:20,633 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 10:54:33,010 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 10:54:49,052 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 10:54:49,052 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 10:54:49,052 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 10:55:19,123 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Still waiting for challenge completion... (30s elapsed)
2025-06-26 10:56:14,140 - LinkedInEasyApplyPro - ERROR - _safe_log:847 - Authentication error: Message: timeout: Timed out receiving message from renderer: 45.000
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff71c00cda5+78885]
	GetHandleVerifier [0x0x7ff71c00ce00+78976]
	(No symbol) [0x0x7ff71bdc9bca]
	(No symbol) [0x0x7ff71bdb706c]
	(No symbol) [0x0x7ff71bdb6d5a]
	(No symbol) [0x0x7ff71bdb492f]
	(No symbol) [0x0x7ff71bdb538f]
	(No symbol) [0x0x7ff71bdc402e]
	(No symbol) [0x0x7ff71bdda3e1]
	(No symbol) [0x0x7ff71bde153a]
	(No symbol) [0x0x7ff71bdb5b2d]
	(No symbol) [0x0x7ff71bdda115]
	(No symbol) [0x0x7ff71be71109]
	(No symbol) [0x0x7ff71be48963]
	(No symbol) [0x0x7ff71be116b1]
	(No symbol) [0x0x7ff71be12443]
	GetHandleVerifier [0x0x7ff71c2e4eed+3061101]
	GetHandleVerifier [0x0x7ff71c2df33d+3037629]
	GetHandleVerifier [0x0x7ff71c2fe592+3165202]
	GetHandleVerifier [0x0x7ff71c02730e+186766]
	GetHandleVerifier [0x0x7ff71c02eb3f+217535]
	GetHandleVerifier [0x0x7ff71c0159b4+114740]
	GetHandleVerifier [0x0x7ff71c015b69+115177]
	GetHandleVerifier [0x0x7ff71bffc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 10:56:14,152 - LinkedInEasyApplyPro - ERROR - run_professional_automation:980 - Failed to login
2025-06-26 10:56:16,804 - LinkedInEasyApplyPro - INFO - cleanup:2312 - 🧹 Browser cleanup completed
2025-06-26 10:56:33,240 - LinkedInEasyApplyPro - INFO - cleanup:2312 - 🧹 Browser cleanup completed
2025-06-26 10:56:49,663 - LinkedInEasyApplyPro - INFO - cleanup:2312 - 🧹 Browser cleanup completed
2025-06-26 11:13:29,492 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 11:13:29,494 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 11:13:29,495 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'generative ai' in 'india'
2025-06-26 11:13:29,496 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 11:13:31,078 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 11:13:31,078 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 11:13:53,544 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 11:14:34,647 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 11:14:34,650 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 11:14:34,651 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 11:15:04,705 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Still waiting for challenge completion... (30s elapsed)
2025-06-26 11:15:35,089 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 11:15:35,089 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'generative ai' jobs with intelligent filtering...
2025-06-26 11:16:20,272 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 11:16:20,280 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 11:16:20,282 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 11:16:52,275 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 11:21:55,069 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 11:21:55,069 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 11:21:55,069 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'generative ai' in 'Remote'
2025-06-26 11:21:55,069 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 11:21:56,889 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 11:21:56,889 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 11:22:08,534 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 11:22:25,127 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 11:22:25,128 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 11:22:25,129 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 11:23:24,517 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 11:23:24,518 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'generative ai' jobs with intelligent filtering...
2025-06-26 11:24:05,007 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 11:24:05,008 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 11:24:05,009 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 11:24:39,378 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 11:33:30,120 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 11:33:30,122 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 11:33:30,122 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 50 applications for 'generative ai' in 'Remote'
2025-06-26 11:33:30,122 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 11:33:32,568 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 11:33:32,569 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 11:33:43,210 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 11:33:59,105 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 11:33:59,105 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 11:33:59,106 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 11:34:53,458 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 11:34:53,459 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'generative ai' jobs with intelligent filtering...
2025-06-26 11:35:38,495 - LinkedInEasyApplyPro - ERROR - search_jobs_intelligently:1239 - Error searching for jobs: Message: timeout: Timed out receiving message from renderer: 44.581
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff71c00cda5+78885]
	GetHandleVerifier [0x0x7ff71c00ce00+78976]
	(No symbol) [0x0x7ff71bdc9bca]
	(No symbol) [0x0x7ff71bdb706c]
	(No symbol) [0x0x7ff71bdb6d5a]
	(No symbol) [0x0x7ff71bdb492f]
	(No symbol) [0x0x7ff71bdb538f]
	(No symbol) [0x0x7ff71bdc402e]
	(No symbol) [0x0x7ff71bdda3e1]
	(No symbol) [0x0x7ff71bde153a]
	(No symbol) [0x0x7ff71bdb5b2d]
	(No symbol) [0x0x7ff71bdda115]
	(No symbol) [0x0x7ff71be71568]
	(No symbol) [0x0x7ff71be48963]
	(No symbol) [0x0x7ff71be116b1]
	(No symbol) [0x0x7ff71be12443]
	GetHandleVerifier [0x0x7ff71c2e4eed+3061101]
	GetHandleVerifier [0x0x7ff71c2df33d+3037629]
	GetHandleVerifier [0x0x7ff71c2fe592+3165202]
	GetHandleVerifier [0x0x7ff71c02730e+186766]
	GetHandleVerifier [0x0x7ff71c02eb3f+217535]
	GetHandleVerifier [0x0x7ff71c0159b4+114740]
	GetHandleVerifier [0x0x7ff71c015b69+115177]
	GetHandleVerifier [0x0x7ff71bffc368+10728]
	BaseThreadInitThunk [0x0x7ffa09c5e8d7+23]
	RtlUserThreadStart [0x0x7ffa0b1fc34c+44]

2025-06-26 11:35:38,498 - LinkedInEasyApplyPro - ERROR - run_professional_automation:986 - Failed to search for jobs
2025-06-26 11:35:42,571 - LinkedInEasyApplyPro - INFO - cleanup:2431 - 🧹 Browser cleanup completed
2025-06-26 11:35:59,028 - LinkedInEasyApplyPro - INFO - cleanup:2431 - 🧹 Browser cleanup completed
2025-06-26 11:49:14,566 - ERROR - Critical error: 'debug_mode'
2025-06-26 11:50:44,072 - ERROR - Critical error: 'debug_mode'
2025-06-26 11:54:29,253 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 12:32:28,081 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 12:32:28,084 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 12:32:28,085 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 12:32:28,085 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 12:32:32,851 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 12:32:32,851 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 12:32:38,531 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 12:32:57,165 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 12:32:57,166 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 12:32:57,167 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 12:33:27,370 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 12:33:27,370 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 12:33:41,252 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 12:33:41,252 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 12:33:41,253 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 12:34:12,524 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:07:15,194 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:07:15,195 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:07:15,196 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:07:15,196 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:07:17,345 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:07:17,346 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:07:24,944 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:07:43,487 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:07:43,487 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:07:43,488 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:08:17,897 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:08:17,898 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:08:32,363 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:08:32,363 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:08:32,364 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:09:08,192 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:14:25,070 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:14:25,076 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:14:25,077 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:14:25,078 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:14:26,984 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:14:26,984 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:14:34,130 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:14:50,008 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:14:50,008 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:14:50,009 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:15:02,950 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:15:02,952 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:15:20,178 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:15:20,179 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:15:20,179 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:15:51,718 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:16:56,075 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:16:56,078 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:16:56,079 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:16:56,080 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:16:58,117 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:16:58,117 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:17:05,020 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:17:21,397 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:17:21,398 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:17:21,399 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:17:34,557 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:17:34,558 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:17:47,425 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:17:47,427 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:17:47,428 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:18:19,853 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:23:44,070 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:23:44,070 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:23:44,071 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:23:44,071 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:23:46,212 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:23:46,213 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:23:52,345 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:24:07,640 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:24:07,641 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:24:07,641 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:24:37,740 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:24:37,740 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:24:47,126 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:24:47,127 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:24:47,127 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:24:47,325 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 22 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:31:52,540 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:31:52,541 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:31:52,541 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:31:52,542 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:31:54,392 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:31:54,392 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:32:00,152 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:32:14,358 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:32:14,358 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:32:14,359 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:32:26,354 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:32:26,355 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:32:38,801 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:32:38,802 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:32:38,803 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:32:38,843 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 13:38:18,475 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:38:52,524 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 13:38:52,525 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 13:38:52,526 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 13:38:52,526 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 13:38:54,918 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 13:38:54,918 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 13:39:02,090 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 13:39:17,547 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 13:39:17,547 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 13:39:17,547 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 13:39:41,581 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 13:39:41,582 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 13:39:54,334 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 13:39:54,335 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 13:39:54,336 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 13:39:54,441 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 21 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:09:57,602 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:09:57,603 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:09:57,604 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 15:09:57,604 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:10:00,146 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:10:00,146 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:10:06,536 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:10:19,294 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:10:19,294 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:10:19,294 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:10:29,790 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:10:29,791 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:10:40,321 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 15:10:40,321 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:10:40,322 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:10:40,367 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 22 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:13:43,725 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - Error analyzing job card 9: HTTPConnectionPool(host='localhost', port=62094): Max retries exceeded with url: /session/daeacea8966e21a3a209d081cc97eb56/execute/sync (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000021EC941CDC0>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))
2025-06-26 15:14:28,139 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:14:28,139 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:14:28,139 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 15:14:28,140 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:14:29,320 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:14:29,321 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:14:33,021 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:14:45,736 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:14:45,736 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:14:45,736 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:14:55,768 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:14:55,769 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:15:02,919 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 15:15:02,920 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:15:02,920 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:15:03,193 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 22 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:21:28,811 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:21:28,813 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:21:28,813 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 15:21:28,813 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:21:30,051 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:21:30,051 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:21:36,138 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:21:49,405 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:21:49,405 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:21:49,405 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:21:59,452 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:21:59,453 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:22:04,631 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 15:22:04,633 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:22:04,633 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:22:04,663 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 22 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:28:21,117 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:28:21,118 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:28:21,118 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 15:28:21,118 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:28:23,148 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:28:23,149 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:28:29,108 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:28:40,646 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:28:40,647 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:28:40,647 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:28:57,587 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:28:57,587 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1207 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:29:06,519 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1232 - ✅ Search results loaded successfully
2025-06-26 15:29:06,520 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:29:06,520 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:29:06,603 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 22 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:32:28,587 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:32:28,588 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:32:28,588 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 25 applications for 'data analyst' in 'Remote'
2025-06-26 15:32:28,588 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:32:29,803 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:32:29,803 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:32:33,045 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:32:46,057 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:32:46,057 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:32:46,057 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:32:56,096 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:32:56,097 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1216 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:33:02,004 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1241 - ✅ Search results loaded successfully
2025-06-26 15:33:02,004 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:33:02,004 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:33:02,025 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 18 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:33:02,025 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 1/18
2025-06-26 15:33:02,161 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 1: Marketing Data Analyst
Marketing Data Analyst at Magma Consultancy
2025-06-26 15:33:02,162 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 1 to analyzed jobs list
2025-06-26 15:33:02,162 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 2/18
2025-06-26 15:33:02,265 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 2: Data Entry & Analysis Executive
Data Entry & Analysis Executive at EvolveNow Media
2025-06-26 15:33:02,265 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 2 to analyzed jobs list
2025-06-26 15:33:02,265 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 3/18
2025-06-26 15:33:02,369 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 3: Senior Data Scientist
Senior Data Scientist with verification at Teradata
2025-06-26 15:33:02,370 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 3 to analyzed jobs list
2025-06-26 15:33:02,370 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 4/18
2025-06-26 15:33:02,487 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 4: Senior Data Scientist
Senior Data Scientist at huex
2025-06-26 15:33:02,487 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 4 to analyzed jobs list
2025-06-26 15:33:02,488 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 5/18
2025-06-26 15:33:02,632 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 5: Tableau Senior Administrator
Tableau Senior Administrator at Aiprus Software Private Limited
2025-06-26 15:33:02,632 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 5 to analyzed jobs list
2025-06-26 15:33:02,632 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 6/18
2025-06-26 15:33:02,724 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 6: Lead Data Scientist
Lead Data Scientist at Ekloud, Inc.
2025-06-26 15:33:02,725 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 6 to analyzed jobs list
2025-06-26 15:33:02,725 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 7/18
2025-06-26 15:33:02,819 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 7: Data Analyst – People & Performance Analytics
Data Analyst – People & Performance Analytics at Equinox Human Capital Partners
2025-06-26 15:33:02,837 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 7 to analyzed jobs list
2025-06-26 15:33:02,838 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 8/18
2025-06-26 15:35:33,630 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 8: Unknown Title at Unknown Company
2025-06-26 15:35:33,630 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - Job card 8 has unknown title, added with fallback title
2025-06-26 15:35:33,631 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 9/18
2025-06-26 15:38:00,894 - LinkedInEasyApplyPro - INFO - __init__:791 - LinkedInEasyApplyPro initialized with advanced features
2025-06-26 15:38:00,895 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [ROCKET] Starting Professional LinkedIn Easy Apply Automation
2025-06-26 15:38:00,895 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Target: 10 applications for 'data analyst' in 'remote'
2025-06-26 15:38:00,895 - LinkedInEasyApplyPro - INFO - setup:903 - Initializing professional Chrome WebDriver with stealth features...
2025-06-26 15:38:02,103 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Professional Chrome WebDriver initialized successfully with stealth features
2025-06-26 15:38:02,103 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [LOCK] Logging in with stealth features...
2025-06-26 15:38:02,103 - LinkedInEasyApplyPro - INFO - _safe_log:845 - No credentials found, prompting for input...
2025-06-26 15:38:07,321 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Entering credentials with human-like behavior...
2025-06-26 15:38:20,277 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - [WARNING] LinkedIn security challenge detected!
2025-06-26 15:38:20,278 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Please complete the security challenge manually in the browser.
2025-06-26 15:38:20,278 - LinkedInEasyApplyPro - INFO - _safe_log:845 - The automation will wait for you to complete it...
2025-06-26 15:38:37,944 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Security challenge completed successfully!
2025-06-26 15:38:37,944 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1215 - 🔍 Searching for 'data analyst' jobs with intelligent filtering...
2025-06-26 15:38:48,828 - LinkedInEasyApplyPro - INFO - search_jobs_intelligently:1240 - ✅ Search results loaded successfully
2025-06-26 15:38:48,828 - LinkedInEasyApplyPro - INFO - run_professional_automation:1000 - 📄 Processing page 1
2025-06-26 15:38:48,829 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Finding and analyzing job cards...
2025-06-26 15:38:48,868 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found 25 job cards using selector: .scaffold-layout__list-item
2025-06-26 15:38:48,868 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 1/25
2025-06-26 15:38:49,046 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 1: Marketing Data Analyst
Marketing Data Analyst at Magma Consultancy
2025-06-26 15:38:49,046 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 1 to analyzed jobs list
2025-06-26 15:38:49,046 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 2/25
2025-06-26 15:38:49,189 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 2: Data Entry & Analysis Executive
Data Entry & Analysis Executive at EvolveNow Media
2025-06-26 15:38:49,189 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 2 to analyzed jobs list
2025-06-26 15:38:49,189 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 3/25
2025-06-26 15:38:49,326 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 3: Business Intelligence Analyst
Business Intelligence Analyst with verification at VAYUZ Technologies
2025-06-26 15:38:49,326 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 3 to analyzed jobs list
2025-06-26 15:38:49,326 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 4/25
2025-06-26 15:38:49,476 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 4: Medical Data Analyst
Medical Data Analyst at Minivel Services
2025-06-26 15:38:49,476 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 4 to analyzed jobs list
2025-06-26 15:38:49,477 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 5/25
2025-06-26 15:38:49,635 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 5: Field information officer
Field information officer at Centre for Monitoring Indian Economy
2025-06-26 15:38:49,636 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 5 to analyzed jobs list
2025-06-26 15:38:49,636 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 6/25
2025-06-26 15:38:49,791 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 6: Anaplan Model Builder (6-9 years)
Anaplan Model Builder (6-9 years) at Creator City
2025-06-26 15:38:49,791 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 6 to analyzed jobs list
2025-06-26 15:38:49,792 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 7/25
2025-06-26 15:38:49,939 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 7: Data Modeler
Data Modeler with verification at VOIS
2025-06-26 15:38:49,939 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 7 to analyzed jobs list
2025-06-26 15:38:49,939 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 8/25
2025-06-26 15:38:59,962 - LinkedInEasyApplyPro - WARNING - _safe_log:849 - Job card 8 processing timeout - skipping
2025-06-26 15:38:59,962 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 9/25
2025-06-26 15:39:03,973 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 9: Data Analyst (8+ years of experience)
Data Analyst (8+ years of experience) with verification at Western Digital
2025-06-26 15:39:03,973 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 9 to analyzed jobs list
2025-06-26 15:39:03,974 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 10/25
2025-06-26 15:39:04,093 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 10: Data Scientist
Data Scientist at K&K Talents - India
2025-06-26 15:39:04,094 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 10 to analyzed jobs list
2025-06-26 15:39:04,094 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 11/25
2025-06-26 15:39:04,263 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 11: Lead Data Scientist
Lead Data Scientist with verification at Teradata
2025-06-26 15:39:04,263 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 11 to analyzed jobs list
2025-06-26 15:39:04,264 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 12/25
2025-06-26 15:39:04,353 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 12: Analyst
Analyst at modi mundi pharma beauty products pvt.ltd
2025-06-26 15:39:04,354 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 12 to analyzed jobs list
2025-06-26 15:39:04,354 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 13/25
2025-06-26 15:39:04,437 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 13: Data Scientist
Data Scientist at KANINI
2025-06-26 15:39:04,437 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 13 to analyzed jobs list
2025-06-26 15:39:04,437 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 14/25
2025-06-26 15:39:04,602 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 14: Data Modeler
Data Modeler at InfoSpruce Technologies Private Limited
2025-06-26 15:39:04,602 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 14 to analyzed jobs list
2025-06-26 15:39:04,603 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 15/25
2025-06-26 15:39:04,726 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 15: Sap Business Warehouse Business Intelligence Consultant
Sap Business Warehouse Business Intelligence Consultant at Resource Algorithm
2025-06-26 15:39:04,726 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 15 to analyzed jobs list
2025-06-26 15:39:04,726 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 16/25
2025-06-26 15:39:05,089 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 16: Senior Data Scientist
Senior Data Scientist at huex
2025-06-26 15:39:05,089 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 16 to analyzed jobs list
2025-06-26 15:39:05,090 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 17/25
2025-06-26 15:39:05,236 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 17: Survey Associate - Qualtrics Expertise
Survey Associate - Qualtrics Expertise with verification at Guidepoint
2025-06-26 15:39:05,236 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 17 to analyzed jobs list
2025-06-26 15:39:05,236 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 18/25
2025-06-26 15:39:05,331 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 18: Data Analyst
Data Analyst at Zafin
2025-06-26 15:39:05,332 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 18 to analyzed jobs list
2025-06-26 15:39:05,332 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 19/25
2025-06-26 15:39:05,418 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 19: Tableau Senior Administrator
Tableau Senior Administrator at Aiprus Software Private Limited
2025-06-26 15:39:05,418 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 19 to analyzed jobs list
2025-06-26 15:39:05,418 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 20/25
2025-06-26 15:39:05,484 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 20: Senior Data Scientist
Senior Data Scientist at MPM Infosoft Pvt. Ltd
2025-06-26 15:39:05,484 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 20 to analyzed jobs list
2025-06-26 15:39:05,485 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 21/25
2025-06-26 15:39:05,553 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 21: Data Scientist
Data Scientist with verification at TIGI HR
2025-06-26 15:39:05,553 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 21 to analyzed jobs list
2025-06-26 15:39:05,554 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 22/25
2025-06-26 15:39:05,635 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 22: Lead Data Scientist
Lead Data Scientist at Ekloud, Inc.
2025-06-26 15:39:05,636 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 22 to analyzed jobs list
2025-06-26 15:39:05,636 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 23/25
2025-06-26 15:39:05,748 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 23: Product Analyst-Gaming
Product Analyst-Gaming at Charles Technologies
2025-06-26 15:39:05,749 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 23 to analyzed jobs list
2025-06-26 15:39:05,749 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 24/25
2025-06-26 15:39:05,835 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 24: GenAI Data Scientist - 3 years - 20LPA - Doc Extraction, RAG, LLMOps
GenAI Data Scientist - 3 years - 20LPA - Doc Extraction, RAG, LLMOps at datavruti
2025-06-26 15:39:05,835 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 24 to analyzed jobs list
2025-06-26 15:39:05,835 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job card 25/25
2025-06-26 15:39:05,966 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Extracted job 25: Data Analyst
Data Analyst at DeHaat
2025-06-26 15:39:05,966 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Added job 25 to analyzed jobs list
2025-06-26 15:39:05,966 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [CHART] Successfully analyzed 24 job cards
2025-06-26 15:39:05,967 - LinkedInEasyApplyPro - INFO - _safe_log:845 - 📋 Ready to process 24 jobs for applications
2025-06-26 15:39:05,967 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Starting to process 24 jobs from page 1
2025-06-26 15:39:05,967 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Processing job 1/24 on page 1
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - ERROR - analyze_job_with_ai:1537 - Error in AI job analysis: 'NoneType' object has no attribute 'lower'
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Job analysis completed for: Marketing Data Analyst
Marketing Data Analyst
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Using fallback scoring for Marketing Data Analyst
Marketing Data Analyst: 0.40
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [TARGET] Decision for Marketing Data Analyst
Marketing Data Analyst: APPLY (Score: 0.32)
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Decision: APPLY to Marketing Data Analyst
Marketing Data Analyst
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - apply_with_advanced_features:1602 - 🚀 Applying to: Marketing Data Analyst
Marketing Data Analyst at Magma Consultancy
2025-06-26 15:39:18,716 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SEARCH] Looking for Easy Apply buttons...
2025-06-26 15:39:18,797 - LinkedInEasyApplyPro - INFO - _safe_log:845 - Found visible Easy Apply button
2025-06-26 15:39:19,014 - LinkedInEasyApplyPro - INFO - _safe_log:845 - [SUCCESS] Easy Apply button clicked successfully
2025-06-26 15:39:25,314 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'Search by title, skill, or company' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:39:25,392 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: invalid element state: Element is not currently interactable and may not be manipulated
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c9bca]
	(No symbol) [0x0x7ff7d69d15bd]
	(No symbol) [0x0x7ff7d69d460c]
	(No symbol) [0x0x7ff7d69d46df]
	(No symbol) [0x0x7ff7d6a16467]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:39:29,512 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'Search by title, skill, or company' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:39:39,610 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: element not interactable
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c99fc]
	(No symbol) [0x0x7ff7d6a15ad7]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:39:43,731 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'City, state, or zip code' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:39:43,768 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: invalid element state: Element is not currently interactable and may not be manipulated
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c9bca]
	(No symbol) [0x0x7ff7d69d15bd]
	(No symbol) [0x0x7ff7d69d460c]
	(No symbol) [0x0x7ff7d69d46df]
	(No symbol) [0x0x7ff7d6a16467]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:39:48,017 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'Add a company' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:39:58,067 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: element not interactable
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c99fc]
	(No symbol) [0x0x7ff7d6a15ad7]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:40:32,449 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'G-Recaptcha-Response' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:40:42,517 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: element not interactable
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c99fc]
	(No symbol) [0x0x7ff7d6a15ad7]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:40:42,517 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1869 - 🎯 AI handled 0 questions
2025-06-26 15:40:52,568 - LinkedInEasyApplyPro - WARNING - click_next_button_intelligent:2009 - Error with button selector button[aria-label*='Continue']: 'LinkedInEasyApplyPro' object has no attribute 'track_application_progress'
2025-06-26 15:41:12,662 - LinkedInEasyApplyPro - WARNING - click_next_button_intelligent:2009 - Error with button selector button[data-easy-apply-next-button]: 'LinkedInEasyApplyPro' object has no attribute 'track_application_progress'
2025-06-26 15:41:12,702 - LinkedInEasyApplyPro - WARNING - click_next_button_intelligent:2009 - Error with button selector footer button.artdeco-button--primary: 'LinkedInEasyApplyPro' object has no attribute 'track_application_progress'
2025-06-26 15:41:22,737 - LinkedInEasyApplyPro - INFO - fix_form_errors_intelligent:2070 - 🔧 Fixing error: Enter a valid phone number
2025-06-26 15:41:27,168 - LinkedInEasyApplyPro - INFO - fix_form_errors_intelligent:2095 - 🎯 Fixed 1 form errors with AI
2025-06-26 15:41:35,747 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'Search by title, skill, or company' -> Answer: 'Yes' (Confidence: 0.00)
2025-06-26 15:41:35,800 - LinkedInEasyApplyPro - WARNING - handle_questions_with_ai:1866 - Error handling question element: Message: invalid element state: Element is not currently interactable and may not be manipulated
  (Session info: chrome=137.0.7151.122)
Stacktrace:
	GetHandleVerifier [0x0x7ff7d6c0cda5+78885]
	GetHandleVerifier [0x0x7ff7d6c0ce00+78976]
	(No symbol) [0x0x7ff7d69c9bca]
	(No symbol) [0x0x7ff7d69d15bd]
	(No symbol) [0x0x7ff7d69d460c]
	(No symbol) [0x0x7ff7d69d46df]
	(No symbol) [0x0x7ff7d6a16467]
	(No symbol) [0x0x7ff7d6a48b8a]
	(No symbol) [0x0x7ff7d6a12f06]
	(No symbol) [0x0x7ff7d6a48da0]
	(No symbol) [0x0x7ff7d6a7122f]
	(No symbol) [0x0x7ff7d6a48963]
	(No symbol) [0x0x7ff7d6a116b1]
	(No symbol) [0x0x7ff7d6a12443]
	GetHandleVerifier [0x0x7ff7d6ee4eed+3061101]
	GetHandleVerifier [0x0x7ff7d6edf33d+3037629]
	GetHandleVerifier [0x0x7ff7d6efe592+3165202]
	GetHandleVerifier [0x0x7ff7d6c2730e+186766]
	GetHandleVerifier [0x0x7ff7d6c2eb3f+217535]
	GetHandleVerifier [0x0x7ff7d6c159b4+114740]
	GetHandleVerifier [0x0x7ff7d6c15b69+115177]
	GetHandleVerifier [0x0x7ff7d6bfc368+10728]
	BaseThreadInitThunk [0x0x7fff31f4e8d7+23]
	RtlUserThreadStart [0x0x7fff3381c34c+44]

2025-06-26 15:41:39,935 - LinkedInEasyApplyPro - INFO - handle_questions_with_ai:1850 - 🤖 AI Question: 'Search by title, skill, or company' -> Answer: 'Yes' (Confidence: 0.00)
