#!/usr/bin/env python
"""
LinkedIn Easy Apply Automation - Final Perfect Version
Professional-grade automation with AI features, Windows compatibility, and robust error handling
"""

import os
import sys
import json
import time
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Set Windows UTF-8 compatibility
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# Import the core automation
from linkedin_easy_apply import LinkedInEasyApply

def setup_logging() -> logging.Logger:
    """Setup logging with Windows compatibility"""
    logger = logging.getLogger('LinkedInAutomation')
    logger.setLevel(logging.INFO)
    
    # File handler with UTF-8 encoding
    file_handler = logging.FileHandler('linkedin_automation.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # Console handler with safe formatting
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(console_formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def load_config() -> Dict[str, Any]:
    """Load or create configuration"""
    config_file = "linkedin_config.json"
    
    default_config = {
        "credentials": {
            "email": "",
            "password": ""
        },
        "search_preferences": {
            "default_search_term": "data analyst",
            "default_location": "Remote",
            "max_applications_per_session": 25
        },
        "user_profile": {
            "skills": ["Python", "Data Analysis", "Machine Learning", "SQL", "Excel"],
            "experience_years": 3,
            "preferred_locations": ["Remote", "New York", "San Francisco"],
            "salary_range": [80000, 150000],
            "job_types": ["Full-time", "Contract"],
            "industries": ["Technology", "Finance", "Healthcare"]
        },
        "automation_settings": {
            "headless": False,
            "stealth_mode": True,
            "intelligent_filtering": True,
            "auto_optimization": True,
            "success_rate_target": 0.85,
            "max_retries": 3,
            "application_threshold": 0.3,
            "debug_mode": False
        }
    }
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # Validate and fix missing keys
            if "automation_settings" not in config:
                config["automation_settings"] = default_config["automation_settings"]
            elif "debug_mode" not in config["automation_settings"]:
                config["automation_settings"]["debug_mode"] = False

            print(f"[SUCCESS] Loaded configuration from {config_file}")
            return config
        except Exception as e:
            print(f"[WARNING] Error loading config: {e}")
    
    # Save default config
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2)
        print(f"[INFO] Created default configuration: {config_file}")
    except Exception as e:
        print(f"[WARNING] Could not save config: {e}")
    
    return default_config

def get_credentials(config: Dict[str, Any]) -> Dict[str, str]:
    """Get LinkedIn credentials"""
    email = config["credentials"].get("email")
    password = config["credentials"].get("password")
    
    if not email:
        email = input("Enter LinkedIn email: ").strip()
    
    if not password:
        import getpass
        password = getpass.getpass("Enter LinkedIn password: ")
    
    return {"email": email, "password": password}

def get_search_parameters(config: Dict[str, Any], args) -> tuple:
    """Get search parameters from args or user input"""
    if args.search and args.location and args.max_apps:
        return args.search, args.location, args.max_apps
    
    print(f"\n[SEARCH] Search Configuration:")
    
    search_term = input(f"Job search term (default: '{config['search_preferences']['default_search_term']}'): ").strip()
    if not search_term:
        search_term = config['search_preferences']['default_search_term']
    
    location = input(f"Location (default: '{config['search_preferences']['default_location']}'): ").strip()
    if not location:
        location = config['search_preferences']['default_location']
    
    max_apps = input(f"Max applications (default: {config['search_preferences']['max_applications_per_session']}): ").strip()
    if not max_apps:
        max_apps = config['search_preferences']['max_applications_per_session']
    else:
        try:
            max_apps = int(max_apps)
        except ValueError:
            max_apps = config['search_preferences']['max_applications_per_session']
    
    return search_term, location, max_apps

def display_results(results: Dict[str, Any], logger: logging.Logger):
    """Display comprehensive session results"""
    print("\n" + "="*60)
    print("[RESULTS] SESSION SUMMARY")
    print("="*60)
    
    print(f"[SUCCESS] Applications Submitted: {results['successful_applications']}")
    print(f"[FAILED] Applications Failed: {results['failed_applications']}")
    print(f"[RATE] Success Rate: {results['success_rate']:.1%}")
    print(f"[ANALYZED] Jobs Analyzed: {results['jobs_analyzed']}")
    print(f"[TIME] Session Duration: {results['session_duration']/60:.1f} minutes")
    
    if results.get('applications_per_hour', 0) > 0:
        print(f"[SPEED] Applications/Hour: {results['applications_per_hour']:.1f}")
    
    if results.get('recommendations'):
        print(f"\n[TIPS] Recommendations:")
        for rec in results['recommendations']:
            print(f"   • {rec}")
    
    if results.get('errors') and len(results['errors']) > 0:
        print(f"\n[ERRORS] Issues Encountered ({len(results['errors'])}):")
        for error in results['errors'][:3]:  # Show first 3 errors
            print(f"   • {error}")
        if len(results['errors']) > 3:
            print(f"   ... and {len(results['errors']) - 3} more")

def save_results(results: Dict[str, Any], logger: logging.Logger):
    """Save session results to file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"session_results_{timestamp}.json"
    
    try:
        # Convert datetime objects to strings for JSON serialization
        serializable_results = results.copy()
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        print(f"\n[SAVED] Session results saved to: {results_file}")
    except Exception as e:
        logger.warning(f"Could not save results: {e}")

def main():
    """Main automation function"""
    parser = argparse.ArgumentParser(description="LinkedIn Easy Apply Automation - Professional Version")
    parser.add_argument("--headless", action="store_true", help="Run in headless mode")
    parser.add_argument("--search", type=str, help="Job search term")
    parser.add_argument("--location", type=str, help="Job location")
    parser.add_argument("--max-apps", type=int, help="Maximum applications")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    
    print("🚀 LinkedIn Easy Apply Automation - Professional Version")
    print("=" * 60)
    
    try:
        # Load configuration
        config = load_config()
        
        # Ensure all required keys exist
        if "debug_mode" not in config["automation_settings"]:
            config["automation_settings"]["debug_mode"] = False

        # Override with command line arguments
        if args.headless:
            config["automation_settings"]["headless"] = True
        if args.debug:
            config["automation_settings"]["debug_mode"] = True
        
        # Get credentials
        credentials = get_credentials(config)
        
        # Get search parameters
        search_term, location, max_applications = get_search_parameters(config, args)
        
        print(f"\n[TARGET] Starting automation:")
        print(f"   Search: '{search_term}' in '{location}'")
        print(f"   Target: {max_applications} applications")
        print(f"   Mode: {'Headless' if config['automation_settings']['headless'] else 'Visible'}")
        print(f"   Debug: {'Enabled' if config['automation_settings']['debug_mode'] else 'Disabled'}")
        
        # Initialize automation
        automation = LinkedInEasyApply(
            headless=config["automation_settings"]["headless"],
            credentials=credentials
        )
        
        print(f"\n[LAUNCH] Starting Professional LinkedIn Automation...")
        
        # Run automation
        results = automation.run_easy_apply_process(
            search_term=search_term,
            location=location,
            max_applications=max_applications
        )
        
        # Display and save results
        display_results(results, logger)
        save_results(results, logger)
        
        # Exit with appropriate code
        if results['success_rate'] >= 0.5:
            print(f"\n[SUCCESS] Session completed successfully!")
            sys.exit(0)
        else:
            print(f"\n[WARNING] Session completed with low success rate")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n\n[STOP] Automation stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Critical error: {str(e)}")
        print(f"\n[ERROR] Critical error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
