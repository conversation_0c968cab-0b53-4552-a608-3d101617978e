{"jobs_applied": 10, "jobs_skipped": 13, "applied_jobs": [{"title": "Dot net azure developer", "company": "Tata Consultancy Services", "location": "Delhi, India", "description": "About the job\nJob Title – Dot net azure developer\nExperience-6-8 Years\nJob Location - Pan India\n  Dear Candidate,\n Greetings from TATA CONSULTANCY SERVICES LIMITED !!!\n Thank you for exploring career opportunities with Asia's largest IT company.\nRequired Skills# Experience in Dotnet azure Developer\n Make sure you have a valid EP number before interview. To create an EP Number, please visit https://ibegin.tcs.com/iBegin/register\nCandidate current location# Anywhere in INDIA\n Experience Range# 6yrs-8yrs\n Interview Location# Anywhere in INDIA\n Work Location # Pan India\n **Interested candidate please share an updated copy of resume & EP REFERENCE <NAME_EMAIL> by asap - with following details\n Please ignore if you are already employee of TCS (Full Time/ BA) or if you are not interested in said job role.\n  Please update the details:\n 1. Candidate Name:\n2. Total years of Exp:\n 3. Total years of relevant Exp:\n 4. Present Company:\n5. Email Address:\n 6. Mobile No.:\n7. Current Location:\n 8. preferred Location:\n 9. Current CTC:\n 10. Expected CTC:\n 11. Notice Period:\n 12.Working with TCS /CMC ( Direct Payroll) earlier (Yes/ NO):\n 13. No Of job change:\n14.Higest fulltime Qualification :\n 15. EP Reference Number (if already registered with TCS)\nThanks & Warm Regards,\nTCS iBegin\nWhatever your career goals, if you are passionate about technology, we at TCS are looking for you. Join us and future forward your career today. Experience energy. Come experience TCS.", "id": "4255598068", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 09:48"}, {"title": "Remote Python AI Engineer - 17852", "company": "Turing", "location": "Delhi, India", "description": "About the job\nWork on Real-World Problems with Global Tech Experts\nJoin a leading U.S.-based technology company as a Python Developer / AI Engineer, where you’ll tackle real-world challenges and build innovative solutions alongside top global experts. This is a fully remote, contract-based opportunity ideal for developers passionate about Python, data analysis, and AI-driven work.\n\nKey Responsibilities: \nWrite efficient, production-grade Python code to solve complex problems.\nAnalyze public datasets and extract meaningful insights using Python and SQL.\nCollaborate with researchers and global teams to iterate on data-driven ideas.\nDocument all code and development decisions in Jupyter Notebooks or similar platforms.\nMaintain high-quality standards and contribute to technical excellence.\n\nJob Requirements:\nOpen to all levels: junior, mid-level, or senior engineers.\nDegree in Computer Science, Engineering, or equivalent practical experience.\nProficient in Python programming for scripting, automation, or backend development.\nExperience with SQL/NoSQL databases is a plus.\nFamiliarity with cloud platforms (AWS, GCP, Azure) is advantageous.\nMust be able to work 5+ hours overlapping with Pacific Time (PST/PT).\nStrong communication and collaboration skills in a remote environment.\n\nPerks & Benefits:\nWork on cutting-edge AI and data projects impacting real-world use cases.\nCollaborate with top minds from Meta, Stanford, and Google.\n100% remote – work from anywhere.\nContract role with flexibility and no traditional job constraints.\nCompetitive compensation in USD, aligned with global tech standards.\n\nSelection Process:\nShortlisted developers may be asked to complete an assessment.\nIf you clear the assessment, you will be contacted for contract assignments with expected start dates, durations, and end dates.\nSome contract assignments require fixed weekly hours, averaging 20/30/40 hours per week for the duration of the contract assignment.", "id": "4256032483", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 09:53"}, {"title": "DevOps Security Engineer", "company": "Themesoft Inc.", "location": "Noida, Uttar Pradesh, India", "description": "About the job\nPosition Title: DevSecOps Engineer - Smart Products & IoT Innovation Center\n\nPosition Summary:\n<PERSON><PERSON> is currently seeking a <PERSON><PERSON><PERSON><PERSON>ps Engineer for IoT projects in the Smart Products & IoT Strategic Innovation Centre in India team. This role is responsible for end-to-end provisioning, management and support of infrastructure deployed in the cloud for IoT projects.\n\nDuties & Responsibilities:\nAzure Services Like:\nAzure Identity\nAzure Compute\nAzure Containers\nAzure App Service / Azure Functions / Azure Static Web Apps\nAzure Resource Manager (ARM\nAzure Container Registry (ACR)\nAzure Kubernetes Service (AKS) (for containerized apps)\nAzure Key Vault\nAzure Web Application Firewall (WAF)\nAzure DevOps\nAzure Virtual machines\nLoad balancing/Scaling.\nContainerization & Orchestration\nARM\nDoing RCA, Disaster recovery, Service outage management, and backup planning.\nGithub integration with Azure using Git Workflow\nHandling production workload spread across the globe.\nMust handle GDPR policy in CI/CD.\nDrive POCs (proof of concepts) in cloud services.\nTechnical responsibility of taking the implementation from POC to large rollout.\n\nQualifications and Experience:\nBachelor’s degree in electrical engineering, Software Engineering, Computer Science, Computer Engineering, or related Engineering discipline; master’s degree or higher from IIT/IISc or other premier institutes preferred.\n5 years of experience in technical architecture including 3+ years of experience in Azure.\nIn-depth knowledge and experience of Azure & AWS IoT platforms and services.\nHaving good experience in docker & Kubernetes and its deployment process.\nHands-on experience in building and deployment for Nodejs, reactjs, react native, GO, .net, typescript and Python code-based.\nHaving good experience in Cloud Security, Identity, & Compliance services.\nHaving good experience in Cloud Management & Governance services.\nGood to have general experience in deployment framework. (Ansible, terraform, Jenkins).\nAzure Professional Certified will get weightage.\nExposure to Kibana and experience in Red Hat.\nKnowledge of code promotion workflow where promotion/rollback of code should be integrated with any tool like Jira.\nHandled stack auto-scaling for any incident raised.\nAlso, have depth knowledge of Python and CloudFormation.\nHaving good experience in Azure DevOps tools services.\nMust be experienced in the creation and assignment of IAM roles and policies.\nMust have experience in IaaC (CLI and Boto lib).\nStrong understanding of techniques such as Continuous Integration, Continuous Delivery, Test Driven Development, Cloud Development, resiliency, security\nCloud Cost Optimization.\nCloud Monitoring and Scaling.\nHaving excellent knowledge in GIT workflow Deployment with staging environment using cloud DevOps tools.\nExperience in containerized deployments & container orchestration.\nExperience in provisioning environments, infrastructure management & monitoring.\nExperience in designing the HA Architecture and DC-DR setup.\nExperience in agile development, stage gate process, minimum viable product development, and DevOps tools.\n\nSkills and Abilities Required:\nCan-do positive attitude, always looking to accelerate development.\nDriven; commit to high standards of performance and demonstrate personal ownership for getting the job done.\nInnovative and entrepreneurial attitude; stays up to speed on all the latest technologies and industry trends; healthy curiosity to evaluate, understand and utilize new technologies.", "id": "**********", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:00"}, {"title": "Artificial Intelligence Engineer", "company": "LENS Corporation", "location": "Gurugram, Haryana, India", "description": "About the job\nExperience: 3+ years\n\nRequirements:\nExcellent knowledge of computer vision concepts, including but not limited to Image Classification, Object Detection, and Semantic Segmentation, developed using state-of-the-art deep learning algorithms.\nHands-on experience developing efficient and real-time convolutional neural network (CNN) models for computer vision tasks.\nStrong proficiency in at least one of the deep learning frameworks, such as PyTorch, TensorFlow, or Caffe, with the ability to apply them to computer vision problems.\nQuick prototyping skills in Python and coding and debugging proficiency in C++.\nGood communication and collaboration skills to work effectively in a team and communicate complex technical concepts.\n\nQualifications:\nA Ph.D. degree (including candidates at various stages of their Ph.D., such as thesis submission, thesis submitted, degree awaited, synopsis seminar completed, defense completed) in Deep Learning with hands-on coding skills and a passion for an industrial career will be preferred.\nMaster's or Bachelor's degree with thorough industrial work experience in developing computer vision applications using deep learning.\nPostgraduates or Undergraduates with a strong academic background in Deep Learning, Computer Vision, or related fields, and demonstrated coding skills, are also encouraged to apply.\n\nPreferred:\nPublications in top-tier computer vision conferences like CVPR, ICCV, ECCV, or major AI conferences like NeurIPS.\nKnowledge of computer vision libraries and tools, including OpenCV and DLib, and a solid understanding of image processing and computer vision fundamentals.\nHands-on experience with model compression and pruning techniques in deep learning.\nGood exposure to various deep learning architectures, such as Artificial Neural Networks (ANN), Deep Neural Networks (DNN), Convolutional Neural Networks (CNN), Recurrent Neural Networks (RNN), and Long Short-Term Memory (LSTM) networks.\nFamiliarity with GPU programming (e.g., CUDA, OpenCL) for efficient deep-learning computations.\n\nPay is competitive as per market standards.", "id": "4255506583", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:07"}, {"title": "Senior AWS Data Engineer", "company": "CYAN360", "location": "India", "description": "About the job\nPosition: Senior AWS Data Engineer\nLocation: Remote\nSalary: Open\nWork Timings: 2:30 PM to 11:30 PM IST\nNeed someone who can join immediately or in 15 days\n\nResponsibilities:\nDesign, develop, and deploy end-to-end data pipelines on AWS cloud infrastructure using services such as Amazon S3, AWS Glue, AWS Lambda, Amazon Redshift, etc.\nImplement data processing and transformation workflows using Apache Spark, and SQL to support analytics and reporting requirements.\nBuild and maintain orchestration workflows to automate data pipeline execution, scheduling, and monitoring.\nCollaborate with analysts, and business stakeholders to understand data requirements and deliver scalable data solutions.\nOptimize data pipelines for performance, reliability, and cost-effectiveness, leveraging AWS best practices and cloud-native technologies.\n Qualification:\nMinimum 8+ years of experience building and deploying large-scale data processing pipelines in a production environment.\nHands-on experience in designing and building data pipelines on AWS cloud infrastructure.\nStrong proficiency in AWS services such as Amazon S3, AWS Glue, AWS Lambda, Amazon Redshift, etc.\nStrong experience with Apache Spark for data processing and analytics.\nHands-on experience on orchestrating and scheduling data pipelines using AppFlow, Event Bridge and Lambda.\nSolid understanding of data modeling, database design principles, and SQL and Spark SQL.", "id": "4255599865", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:09"}, {"title": "Artificial Intelligence Engineer", "company": "Cozzera", "location": "India", "description": "About the job\nJob Title: Artificial Intelligence Engineer – GenAI & LLM\nExperience: 8+ Years\nLocation: Remote \nEmployment Type: Full-time\n\nRole Overview:\n We are seeking a highly experienced Artificial Intelligence Engineer with deep expertise in Generative AI and Large Language Models (LLMs) to lead the design, development, and deployment of cutting-edge AI solutions. The ideal candidate will play a critical role in driving innovation, building scalable models, and integrating advanced AI capabilities into enterprise-grade applications.\n\nKey Responsibilities:\nDesign, fine-tune, and deploy advanced LLM-based solutions for various business use cases.\nLead development of GenAI applications, such as content generation, summarization, conversational AI, and intelligent assistants.\nCollaborate with product, engineering, and data teams to define AI strategies and implement scalable models.\nOptimize LLMs for performance, cost-efficiency, and response quality using techniques like prompt engineering, transfer learning, and parameter tuning.\nEvaluate and integrate open-source and commercial LLMs (e.g., OpenAI, LLaMA, Mistral, Claude, Gemini).\nDevelop custom pipelines for training and inference using frameworks like Transformers, LangChain, Hugging Face, or OpenLLM.\nApply MLOps principles for continuous integration and deployment of AI models.\nEnsure adherence to ethical AI practices, data privacy, and responsible AI development standards.\n\nRequired Skills:\n8+ years of experience in AI/ML, with at least 2–3 years in GenAI and LLMs.\nHands-on experience with Transformer architectures, GPT, BERT, or other foundational models.\nStrong coding skills in Python; familiarity with TensorFlow, PyTorch, Hugging Face, LangChain.\nExperience with prompt engineering, model fine-tuning, and RAG pipelines.\nWorking knowledge of vector databases (e.g., FAISS, Pinecone, Weaviate).\nExperience with cloud-based AI services (AWS SageMaker, Azure AI, Google Vertex AI, etc.).\nFamiliarity with MLOps tools (e.g., MLflow, Kubeflow) and CI/CD for AI deployments.\nStrong problem-solving, research, and communication skills.", "id": "4255597327", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:12"}, {"title": "Data Privacy Engineer", "company": "Insight Global", "location": "India", "description": "About the job\nJob Description\nRequired Skills\n7+ years software development (Java and Python) \nPrior project expertise in a Data Privacy platform integration/implementation (OneTrust, TrustArc, Osano, Captain Compliance, DataGrail, Securiti.AI) \nProficient in writing complex SQL queries \nExpertise in deploying code into enterprise cloud provider (AWS, Google, and/or Azure) \nExpertise in working with multiple databases (relational and non-relational) \nExtensive RESTful API development \nEnglish proficiency (written and oral)\n Desired Skill\nPrior experience integrating Securiti.AI to a client \n Daily Role\nYou will be joining a Fortune 100 client, within their Data Governance/Stewardship organization. Currently, the client is implementing security.ai to manage their data privacy at the global level. There are 6 modules that need to be implemented and then there will be integration of various solutions. These two team members will be part of a larger team (which will expand in future months). As part of this team, you will assist in the development and implementation of securiti.ai in the scope of: there are several business applications, platforms, and data lakes processing personal data across different client markets that they may want to connect to Securiti.ai. Depending on the underlying technical architecture of each data source, we will choose to integrate them either via Informatica EDC (Enterprise Data Catalog) or Securiti SDI (Sensitive Data Intelligence), with the goal of automating personal data discovery. Upcoming integrations include connecting Securiti with Informatica Cloud, ServiceNow, Salesforce Consumer Data Source, and several local consumer data lakes. There are two immediate roles to be onboarded: Integration Lead & Technical Developer; to be located within India.", "id": "4255901107", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:15"}, {"title": "ServiceNow ITOM Developer", "company": "Coforge", "location": "Noida, Uttar Pradesh, India", "description": "About the job\nJob Title: ServiceNow ITOM Developer\nSkills: ITOM, Discovery, Event Management & Service Mapping\nExperience: 5 - 12 years\nLocation: Greater Noida, Pune & Bengaluru\nNotice: Immediate to 60 days joiners are preferable\n\nWe at Coforge are hiring ServiceNow ITOM Developers with the following skillset:\n\nAnalyzing business requirements, designing & developing implementing solutions on the ServiceNow platform.\nCollaborate with cross - functional teams to ensure the successful delivery of ServiceNow applications & modules, meeting the organization's needs.\n\nRequirements Analysis:\nCollaborate with business analysts & stakeholders to understand and translate business requirements into technical specifications.\n\nDesign and Development:\nDesign & develop ServiceNow applications and modules, ensuring adherence to best practices and industry standards.\nCustomize & configure workflows, forms, fields, scripts, business rules, UI policies and other ServiceNow components.\nImplement & configure ServiceNow IT Operations Management (ITOM) modules, such as discovery, Service Mapping, event management and CMDB (Configuration Management Database).\nCollaborate with infrastructure & operations teams to ensure accurate and up - to - date configuration data within the CMDB.\nMonitor & manage infrastructure events, alerts, and incidents using ServiceNow Event Management.\nDevelop & maintain integrations between ServiceNow and other monitoring tools and systems.\n\nIntegration:\nIntegrate ServiceNow with other systems & applications as needed.\nDevelop & maintain integrations using web services, APIs and other integration technologies.\n\nCollaboration:\nWork closely with cross - functional teams, including system administrators, business analysts, & quality assurance teams, to deliver high - quality solutions.\nCollaborate with stakeholders to gather feedback & make necessary adjustments to meet evolving business needs.\n\nTesting & Quality Assurance:\nConduct unit testing & participate in quality assurance activities to ensure the reliability and functionality of developed solutions.\n\nDocumentation:\nCreate & maintain technical documentation, including design specifications, user guides, and test plans.\n\nSupport & Maintenance:\nProvide ongoing support & maintenance for ServiceNow applications, troubleshooting & resolving issues promptly.\n\nTraining:\nTrain end - users & support teams on new features and functionality.\n\nQualifications:\nBachelor's degree in Computer Science, Information Technology, or a related field.\nProven experience as a ServiceNow Developer with expertise in ServiceNow platform configurations & customizations.\nStrong understanding of ITSM & business process automation concepts.\nProficient in JavaScript, HTML, CSS & other web technologies.\nExperience with ServiceNow integrations & web services.\nServiceNow certification(s) is a plus.\n\nSkills:\nServiceNow development skills.\nStrong analytical & problem - solving abilities.\nExcellent communication & collaboration skills.\nAbility to work in a fast - paced & dynamic environment.\nDetail - oriented with a commitment to delivering high-quality solutions.", "id": "4253385360", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:23"}, {"title": "Senior Software Developer - Microsoft Dynamics 365 + SSIS", "company": "VeriPark", "location": "India", "description": "About the job\nWe enable financial institutions to become digital leaders.\n\nAs a professional team of global scale, we work with best clients for great and exciting projects, in an environment where we learn amazing things every day. Each code, each voice, each contribution, each challenge, each success is celebrated here.\n\nWe welcome candidates who share our values, have the skills and are passionate to enjoy our journey to build the digital future of finance, together.\n\nAbout the Job:\nWe are looking for Senior Software Developer – Dynamics CRM / CE + SSIS experienced in designing and developing Microsoft based business solutions in cloud environments using Dynamics 365 and Power Platform, to join our rapidly growing team across regions.\n\nGet the opportunity to work with our global teams in implementing custom applications using the Canvas and Model Driven frameworks, as well as using out of the box CRM applications for business needs such as case management and task management. Banking experience will be preferred.\n\nWhat you will be doing:\n\nGathering and analyzing client requirements to design and implement complex CRM solutions using Microsoft Dynamics 365 and Power Platform.\nCurating and brainstorming approaches to a requirement when needed.\nMeticulously understanding the requirements with respect the solutions offered by CRM’s OOB components and those that need extensions like code.\nEstimating for the efforts around multiple approaches and narrating & quantifying the pros & cons with respect to such implementations.\nDeveloping and customizing various CRM components such as entities, forms, workflows, plugins, reports, dashboards\nImplement integrations with external systems.\nBe involved in development using CRM SDK, C#, ASP.NET, SQL Database, ADO.NET\nWriting clean and efficient code using programming languages such as C# and JavaScript while adhering to coding standards and best practices.\nDebugging and troubleshooting issues in the CRM system and providing timely resolution to ensure high system availability and performance.\nCollaborating with project managers, business analysts, testing teams, and other stakeholders to ensure successful project delivery on time and within budget.\nCalculating and raising such concerns in timely manner which might intervene the timely delivery of the deliverable.\nProviding technical guidance and mentorship to junior developers and team members to enhance their skills and knowledge in CRM development.\nRequirements gathering, development and deployment of SSIS\nConceptualizing and designing Data Migration projects\nData validation, cleansing, analysis and Transforming\nError and event handling: precedence Constraints, Check Points and Logging\nImporting/exporting data between different sources using SSIS\n\nWhat we are looking for:\n  5+ years of relevant working experience\nBachelor’s Degree in IT, Computer Science\nUnderstand fully what Microsoft’s Modules like Sales, Customer Service, Marketing have to offer out of the box before needing any extended customization.\nETL SSIS development experience\nBe able to timely communicate statuses and concerns down and up the responsibility hierarchy so as to ensure smoother delivery of the requirement.\nBe able to build D365 CE entities, forms, workflows, dashboards and reports\nBe able to develop plug-ins using C# and to code UI logic in JavaScript\nBe able to build and release solutions\nBe able to write technical documentation in clear and understandable way\nHave experience in conceptualizing and designing Data Migration projects\nExpert in data validation, cleansing, analysis and Transforming,\nHave experience in using SSIS Transformations like Lookup, Merge, Union ALL, Multicast.,\nSkilled in error and event handling: precedence Constraints, Check Points and Logging.,\nExperience in importing/exporting data between different sources using SSIS\nBanking experience is preferable\nOptionally develop Web resource, PowerApp apps and PowerBI reports\n\nWhat we are offering?\n\nPerformance-Linked Bonus: Your hard work doesn't go unnoticed! Enjoy a performance-linked bonus as a testament to your dedication!\nRewards Beyond the Job: Enjoy a comprehensive benefits package, including Remote Work Support, Health Insurance, Care Program, and Online Psychological Support. We care you!\nBirthday Leave, Because You Matter: We value your special moments! Take the day off on your birthday and treat yourself.\nGlobal Impact, Cutting-Edge Tech: Immerse yourself in global projects with top-tier clients and stay ahead with cutting-edge technologies. Your skills will shape the future of our industry.\nUnleash Your Potential: Develop yourself with VeriPark Academy opportunities; webinars, and in-house training sessions.\nDiverse, Vibrant Community: Be part of a dynamic environment that values diversity and inclusivity.\nTogether Culture: Even in a remote world, we cultivate connections through engaging face-to-face gatherings as well as online fun events. Special information sharing environment where you can update & align yourself.\n\nAbout VeriPark:\n\nWe are a global technology company with more than two decades of expertise in the Financial Services industry. Our mission is to enable financial institutions to become digital leaders by delivering world class customer journeys in digital and assisted channels. We develop omni-channel delivery, customer engagement (CRM), branch automation and loan origination solutions based on Microsoft technology (Azure, Dynamics 365, Power Platform) for banks and insurance companies worldwide.\nWith offices in 15 countries from Canada over Europe and the Middle East to Kuala Lumpur, our 1000+ colleagues are serving customers in more than 30 countries worldwide.\n\n\nhttps://www.veripark.com\n\nFeel free to reach me for any queries related to the employment (**********)", "id": "**********", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:26"}, {"title": "Senior Power Platform Developer (Copilot / PowerApps with AI/LLM)- Contract", "company": "Gravity Infosolutions", "location": "India", "description": "About the job\nRole: Senior Power Platform Developer (Copilot / PowerApps with AI/LLM)\nType: Contract\nDuration: 3-6 Months\nLocation: Remote\nTime zone: IST\nExperience: 4+ Years\n\nJob Description:\nKey Responsibilities:\nDesign, develop, and maintain solutions using Microsoft Copilot Studio for both declarative and custom agents\nCommunicate with business users to gather requirements and design Microsoft Copilot solution\nAct as a business solutions architect in the Microsoft Copilot to guide team members in unified approach on development\nDevelop custom connectors for Microsoft Copilot\nIntegrate Microsoft Copilot with Power Platform Solutions using Power Automate, Teams, SharePoint, different knowledge bases etc.\nProvide support for existing solutions and collaborate with cross-functional teams to investigate possible issues\nRequirements:\nProficiency in Computer Science, Engineering, Information Systems, or a related field.\nStrong experience with Microsoft Power Platform.\nStrong experience integrating REST APIs, custom connectors, and Azure services.\nExperience with Copilot AI, Azure Open AI, or similar AI-powered development tools, LLMs, familiarity with AI concepts\nStrong problem-solving skills and ability to translate business needs into technical solutions.", "id": "4255534410", "date_posted": "", "seniority": "", "job_functions": [], "application_date": "2025-06-25 10:33"}], "job_ids": ["4255901107", "**********", "4255534410", "4255506583", "4256032483", "4255599865", "**********", "4253385360", "4255598068", "4255597327"]}