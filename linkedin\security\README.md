# LinkedIn Project Security & Backup Toolkit: v3.0 - Final Edition

#step is to perform the one-time setup :

pip install -r requirements.txt (if you haven't already).
python run.py generate-salt
python run.py generate-keys (This is the new, critical step).

## 1. Core Philosophy: Unbreakable Integrity

This toolkit provides a military-grade, multi-layered security system for the project, built on four professional principles:
1.  **Defense in Depth:** Uses several independent layers of protection (password derivation, encryption, obfuscation).
2.  **Data Resilience:** Includes a robust, multi-format backup system with automated pruning.
3.  **Auditing:** A comprehensive logging system records all actions for a clear audit trail.
4.  **Unbreakable Integrity:** The system is now built on a **tamper-evident, unbreakable chain of trust.** A digitally signed manifest ensures that your encrypted files cannot be modified, corrupted, or have their integrity checks bypassed without your explicit, password-protected private key. Any unauthorized change renders the project inaccessible, immediately alerting you to a breach.

---

## 2. What's New in the Final Version 3.0

*   **Digitally Signed Manifests:** The file integrity system is now protected by RSA-4096 digital signatures. An attacker cannot tamper with your encrypted files and then fake the integrity check, as they would need your private key and its password to create a valid signature.
*   **Cryptographic Key Pair:** The system now relies on a `private_key.pem` (secret, password-protected) and `public_key.pem` (non-secret) for signing and verification.
*   **Mandatory Integrity Checks:** The decryption process **will not start** if the integrity files are missing or if the digital signature of the manifest is invalid. This is a non-negotiable security feature.
*   **New `generate-keys` Command:** A new, one-time command to create your unique signing key pair.
*   **Encrypted Integrity Files:** The public key and the manifest signature are themselves encrypted along with your project files, making them part of the secure payload and preventing tampering.

---

## 3. Final Folder Structure

```
security/
├── logs/
│   └── security.log
├── src/
│   ├── core.py           # Core crypto functions (symmetric & asymmetric)
│   ├── manager.py        # Main CLI logic for the manager
│   ├── utils.py          # Helper functions
│   └── backup.py         # Backup creation and management
├── run.py                # The single, user-facing entry point
├── config.ini            # All settings are configured here
├── requirements.txt      # Python dependencies
├── salt.bin              # Public salt for key derivation (auto-generated)
├── private_key.pem       # NEW! Your encrypted private key (DO NOT DELETE)
├── public_key.pem        # NEW! Your public key (auto-generated, will be encrypted)
└── README.md             # This documentation file
```

---

## 4. How to Use the Toolkit: The Definitive Guide

### Step 1: Initial One-Time Setup (MANDATORY)

You must perform these steps in order.

1.  **Navigate to the `security` directory** in your terminal.
2.  **Install Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
3.  **Generate the Salt:**
    ```bash
    python run.py generate-salt
    ```
4.  **Generate Signature Keys:** This is the most critical new step.
    ```bash
    python run.py generate-keys
    ```
    *   You will be prompted to create a **new, strong password** for your private key. **This password is the ultimate key to your project's integrity.**

### Step 2: Day-to-Day Workflow

All operations are handled through `run.py`.

1.  **Navigate to the `security` directory.**
2.  **Run the Manager:**
    ```bash
    python run.py
    ```
3.  **Follow the interactive menu:**
    *   **1. Encrypt Project:** Secures your code. This will now require your **private key password** to sign the integrity manifest.
    *   **2. Decrypt Project:** Makes your code readable. This now runs a **mandatory, non-skippable integrity and signature check** before decrypting.
    *   **3. Verify Project Integrity (Standalone):** A manual check. Note: This is less secure than the check performed during decryption because it doesn't use the encrypted-at-rest integrity files.
    *   **4. Create Project Backup:** Creates backups.
    *   **5. Manage Backups (Prune Old):** Cleans up old backups.
    *   **6. Obfuscate Project:** Creates a protected, distributable version.
    *   **7. Alternative Protection Methods:** Access additional code and data protection strategies beyond standard encryption and obfuscation.
    *   **8. Self-Destruct Project:** Irreversibly deletes the project.
    *   **9. Generate New Signature Keys:** **DANGEROUS.** Only use this if you have lost your private key password and are willing to start over. It will invalidate all currently encrypted data.
    *   **10. Check Setup Status:** Shows which critical files are present or missing.
    *   **11. Cleanup and Start New:** Resets the security state by deleting all generated security files, logs, and temporary data. Use this after cloning or if you want to start fresh. You can also run `python run.py cleanup` for the same effect.
    *   **12. Exit:** Closes the manager.

---

## Menu Option Reference (v3.1)

1. Encrypt Project
2. Decrypt Project
3. Verify Project Integrity (Standalone)
4. Create Project Backup
5. Manage Backups (Prune Old)
6. Obfuscate Project (PyArmor)
7. Alternative Protection Methods
8. Self-Destruct Project (DANGEROUS)
9. Generate New Signature Keys (DANGEROUS)
10. Check Setup Status
11. Cleanup and Start New
12. Exit

## Cleanup and Start New: When to Use

- **After cloning the project** to a new machine or directory.
- **If you want to reset all security state** and start fresh.
- **If you encounter unrecoverable errors** or want to clear all logs and keys before re-initializing.

This ensures you never leave old or partial security files behind.

## New: Cleanup and Start New

A new feature allows you to reset the security state after cloning or if you want to start fresh. This will remove all generated security files, logs, and temporary data.

- **To use from the menu:**
  1. Run `python run.py`
  2. Select **Option 11: Cleanup and Start New**
  3. Confirm when prompted

- **To use from the command line:**
  ```bash
  python run.py cleanup
  ```

This is especially useful after cloning the project or if you want to reinitialize security from scratch.

## Obfuscation & PyArmor Limitations

- The project uses PyArmor for code obfuscation. If you see errors about code object size (e.g., `Too big code object, the limitation is 32768 bytes in trial version`), you are hitting the trial version limit. Consider purchasing a full license for larger projects.
- If you see errors about command names, use `pyarmor-7` instead of `pyarmor` for legacy commands.

---

## Atomicity, Error Handling, and Recovery

- All encryption/decryption operations are now atomic: files are processed in a temporary directory and only moved into place if the operation completes successfully.
- If an error occurs, the project is left in a consistent state and no partial changes are committed.
- The system will never leave the project in an inconsistent or insecure state.
- Integrity files (manifest, signature, public key) are always handled safely and restored as needed.

---

## Advanced: Atomicity and Recovery Internals

- All file operations (encryption, decryption, cleanup) are performed in a temporary directory and only committed if successful.
- If any error occurs, the temp directory is deleted and no changes are made to your project files.
- This ensures you never end up with a half-encrypted or half-decrypted project.
- The Cleanup and Start New option will remove all temp, log, and key files, ensuring a truly fresh start.

---

## 5. The Two Passwords: A Critical Distinction

You now have TWO critical passwords. Do not confuse them.

1.  **The Private Key Password (Most Critical):**
    *   **Purpose:** Unlocks your `private_key.pem` to *sign* a new manifest during encryption.
    *   **Consequence of Loss:** **CATASTROPHIC.** If you forget this password, you can **NEVER** sign a new manifest. If you can't sign, you can't re-encrypt your project. Your existing data will be accessible (if you have the other password), but you cannot secure it again. You will be forced to generate new keys and start over.

2.  **The Encryption/Decryption Password (Still Critical):**
    *   **Purpose:** Used with the `salt.bin` to derive the key that encrypts and decrypts your files.
    *   **Consequence of Loss:** **PERMANENT DATA LOSS.** If you forget this password, your encrypted files are irrecoverable.

**Recommendation: Store both passwords in a secure, reputable password manager.**

---

## 6. Final Security Best Practices

*   **NEVER DELETE THE KEY FILES:** Do not delete `salt.bin`, `private_key.pem`, or `public_key.pem`. Deleting them will break the entire security system.
*   **WORKFLOW DISCIPLINE:** The cycle is: **Decrypt -> Edit -> Encrypt**. The system now enforces this by checking integrity on decryption. Trust the process.
*   **BACKUP STRATEGY (3-2-1 Rule):** Periodically copy the `backups` folder to **at least two other locations**, with **one of them being off-site** (e.g., external hard drive, cloud storage).
*   **USE FULL DISK ENCRYPTION:** Enable BitLocker (Windows) or FileVault (macOS) for protection against physical theft.
*   **GITIGNORE:** Ensure your project's root `.gitignore` file contains `/security/`, `/backups/`, and `/dist/`.

---

## Notes on Integrity Files

- The files `manifest.sha256`, `manifest.sha256.sig`, and `public_key.pem` are critical for verifying the integrity of your encrypted project.
- If any of these files are missing or corrupted, decryption will not proceed.
- The system now ensures these files are always handled atomically and restored as needed.

## Quick Reference: Security File Glossary

- `run.py`: Main entry point for the security manager.
- `config.ini`: All settings for the toolkit.
- `salt.bin`: Public salt for key derivation. **CRITICAL.**
- `private_key.pem`: Your encrypted private key. **CRITICAL. NEVER SHARE.**
- `public_key.pem`: Public key for signature verification. Encrypted with the project.
- `manifest.sha256`: List of file hashes for integrity verification.
- `manifest.sha256.sig`: Digital signature of the manifest.
- `logs/`: Contains detailed activity and error logs.
- `src/`: All core logic for the toolkit.
- `backups/`: All project backups.
- `dist/`: (If present) Contains obfuscated or distributable files.

## Troubleshooting

- If you see errors about missing or corrupted integrity files, use the Cleanup and Start New option and reinitialize security.
- For PyArmor errors, see the Obfuscation & PyArmor Limitations section above.
- Always keep backups of your `security/` folder and key files in a safe location.

## Version History

- **v3.0**: Added digitally signed manifests, atomic file operations, improved error handling, and the Cleanup and Start New feature. Menu expanded to 11 options. PyArmor obfuscation support and troubleshooting added.
- **v3.1**: Added Alternative Protection Methods as menu option 7, with updated documentation and folder structure.
