import transformers
import torch
from flask import Flask, request, jsonify

app = Flask(__name__)

model_id = "meta-llama/Meta-Llama-3.1-8B-Instruct"

# Read the content from the 'mydata.txt' file
try:
    with open("mydata.txt", "r") as file:
        content = file.read()
except FileNotFoundError:
    content = "Default content as the file was not found."

try:
    pipeline = transformers.pipeline(
        "text-generation",
        model=model_id,
        model_kwargs={"torch_dtype": torch.bfloat16},
        device_map="auto",
    )
except Exception as e:
    pipeline = None
    print(f"Error loading model: {e}")

@app.route('/generate', methods=['POST'])
def generate():
    if pipeline is None:
        return jsonify({"error": "Model not loaded"}), 500

    data = request.json
    if not data or 'message' not in data:
        return jsonify({"error": "Invalid input"}), 400

    user_message = data.get('message', '')

    messages = [
        {
            "role": "system",
            "content": "Act as if you are the person whose data is mentioned below. Answer questions using the information, "
                       "highlighting relevant details in a positive manner. "
                       "just answer, use your AI capabilities to respond positively, reflecting the persona described. "
                       "Keep answers concise, within 30 words. " + content,
        },
        {"role": "user", "content": user_message}
    ]

    try:
        outputs = pipeline(
            messages,
            max_new_tokens=50,
        )
        result = outputs[0]["generated_text"][-1]
        return jsonify({"response": result})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=10000)


# from transformers import pipeline
# from transformers import AutoModelForCausalLM, AutoTokenizer
# from transformers import LlamaForQuestionAnswering

# model_name = "meta-llama/Meta-Llama-3.1-8B-Instruct"
# model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True)
# tokenizer = AutoTokenizer.from_pretrained(model_name)

# # For text generation
# # device = "cuda" if torch.cuda.is_available() else "cpu"
# device="cpu"
# pipe = pipeline("text-generation", model=model, tokenizer=tokenizer, device=device)
# response = pipe("Who are you?")
# print(response)


# # Load a model that is specifically trained for question answering
# model_qa = LlamaForQuestionAnswering.from_pretrained("meta-llama/Meta-Llama-3.1-8B-Instruct", trust_remote_code=True)
# qa_pipeline = pipeline("question-answering", model=model_qa, tokenizer=tokenizer, device=device)

# response_qa = qa_pipeline({
#     'question': "What is AI?",
#     'context': "Artificial intelligence (AI) is intelligence demonstrated by machines, unlike the natural intelligence displayed by humans and animals."
# })
# print(response_qa)
