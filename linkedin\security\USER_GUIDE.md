# Project Security Toolkit - User Guide (v3.0)

## 1. Introduction

This guide provides comprehensive instructions for using the Project Security Toolkit. This system is designed to provide strong, multi-layered protection for your project through encryption, integrity verification, and robust backup mechanisms.

**Core Features:**
- **Two-Factor Encryption:** Requires a password AND a physical private key file to decrypt.
- **Signed Integrity Checks:** Ensures project files have not been tampered with using digital signatures.
- **Standalone Decryptor:** Allows you to safely share a decryption tool with third parties without exposing your source code or private keys.
- **Automated Backups:** Creates multi-format backups of your entire project.

---

## 2. First-Time Setup

Before using the toolkit for the first time, you must initialize it. This only needs to be done once.

1.  **Open a terminal** in the project's root directory (the `linkedin` folder).
2.  **Install Dependencies:**
    ```powershell
    pip install -r security/requirements.txt
    ```
3.  **Generate the Salt File:** The salt is essential for deriving encryption keys. Run:
    ```powershell
    python security/run.py generate-salt
    ```
4.  **Generate Your Master Keys:** These are your digital signature keys. The private key is also your second factor for decryption. Run:
    ```powershell
    python security/run.py generate-keys
    ```
    - You will be prompted to create a **strong password** for your private key. **DO NOT FORGET THIS PASSWORD.**

**Your toolkit is now initialized and ready for use.**

---

## 3. Core Workflows

All commands should be run from the project's root directory (`C:\Users\<USER>\linkedin\`).

### How to Check Setup Status

If you are unsure which setup steps have been completed, you can run the status checker at any time.

1.  Run the status command:
    ```powershell
    python security/run.py status
    ```
2.  The script will report which critical files (`salt.bin`, `private_key.pem`, `public_key.pem`) are present and which are missing, guiding you on the next command to run.

### How to Encrypt the Project

Use this when you want to secure your project files.

1.  Run the manager:
    ```powershell
    python security/run.py
    ```
2.  Select **Option 1: Encrypt Project**.
3.  Choose a security level (Default is 1).
4.  Enter the **password for your private key** when prompted. This is the password you created during the first-time setup. It is used to sign the project's integrity manifest.

### How to Decrypt the Project

This requires **two things**: the password you just used to encrypt, and your `private_key.pem` file must be present.

1.  Run the manager:
    ```powershell
    python security/run.py
    ```
2.  Select **Option 2: Decrypt Project**.
3.  Choose the same security level you used for encryption.
4.  Enter the **password for your private key**. The system will use this to unlock your private key and verify it exists before proceeding with the decryption of the actual files.

### How to Create a Backup

1.  Run the manager: `python security/run.py`
2.  Select **Option 4: Create Project Backup**.
3.  The system will automatically create `.zip`, `.tar.gz`, and `mirror` backups in the `backups` folder, based on your `config.ini` settings.

---

## 4. Sharing an Encrypted Project

This is the procedure for giving an encrypted project to someone else so they can decrypt it without having your private key.

### Step A: Your Actions (The Sender)

1.  **Encrypt the project** using the workflow above.
2.  **Build the Standalone Decryptor:** Open a terminal in the `security` folder and run the build script:
    ```powershell
    cd security
    .\build_decryptor.bat
    ```
    - This creates a single file: `security\dist\decryptor.exe`.

3.  **Prepare the Package:** You will give the recipient **ONLY TWO THINGS**:
    - The entire encrypted project folder (e.g., a zip of the `linkedin` folder).
    - The `decryptor.exe` file.

    **NEVER SHARE:** Your `security` folder, your `private_key.pem`, your `salt.bin`, or the `build_decryptor.bat` script.

### Step B: Recipient's Actions

You will provide these instructions to the recipient:

1.  Place the `decryptor.exe` file **outside** the main project folder (e.g., on your Desktop).
2.  Run `decryptor.exe`.
3.  The program will ask for the path to the encrypted project folder. Provide the full path.
4.  Enter the decryption password when prompted.
5.  The project files will be decrypted in place.

---

## 5. Disaster Recovery (What If...)

**Scenario 1: I deleted `salt.bin`.**
- **Problem:** You cannot decrypt your project. The salt is required to re-create the same encryption key from your password.
- **Solution:** **DO NOT RE-ENCRYPT.** Restore the `salt.bin` file from a backup. If you have no backup of the salt, the encrypted data is likely unrecoverable.

**Scenario 2: I deleted `private_key.pem`.**
- **Problem:** You cannot decrypt your project (it's your second factor) and you cannot sign any new encryptions.
- **Solution:** Restore `private_key.pem` and `public_key.pem` from a backup. If you have no backup, the data is unrecoverable. You would have to generate new keys, and you would only be able to encrypt new projects, not decrypt old ones.

**Scenario 3: I forgot my password.**
- **Problem:** You cannot derive the encryption key or unlock your private key.
- **Solution:** There is no recovery from a forgotten password. This is by design. Your data is permanently inaccessible.

**Scenario 4: My project files are corrupted.**
- **Problem:** The integrity check fails, and decryption is aborted to prevent further damage.
- **Solution:** This is the primary purpose of backups. Use the **Standalone Recovery Tool** to restore the project from your last known good backup.
    ```powershell
    python security\src\tools\restore_from_backup.py "C:\path\to\your\backup.zip" "C:\path\to\restore\location"
    ```

---

## 6. File Glossary

- `run.py`: The main entry point for the security manager.
- `config.ini`: All settings for the toolkit (exclusions, backup settings, key file names).
- `salt.bin`: A random value used to strengthen your password. **CRITICAL.**
- `private_key.pem`: Your secret key for signing and decryption. **CRITICAL. NEVER SHARE.**
- `public_key.pem`: The public part of your key, used for verifying signatures. It gets encrypted with the project.
- `decryptor.py`: The Python source code for the standalone decryptor. **DO NOT SHARE.**
- `build_decryptor.bat`: The script that builds `decryptor.exe` from `decryptor.py`. **DO NOT SHARE.**
- `USER_GUIDE.md`: This file.
- `/src/`: Contains all the core Python logic for the toolkit.
- `/logs/`: Contains detailed activity and error logs.
- `/dist/`: (Created by the build script) Contains the shareable `decryptor.exe`.
