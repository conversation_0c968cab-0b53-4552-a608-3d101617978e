[Encryption]
# Add file extensions to encrypt, separated by commas.
# Example: .py, .json, .txt, .html
file_extensions = .py, .json, .txt

# Choose the default security level for encryption/decryption.
# 1 = Standard (Fernet, AES-128-CBC)
# 2 = Paranoid (AES-256-GCM)
default_level = 1

[Exclusions]
# Add directory names to exclude from all operations (encryption, backup), separated by commas.
# Critical system and output folders are excluded by default.
excluded_dirs = .git, security, __pycache__, node_modules, dist, backups

# Add specific file names to exclude from encryption, separated by commas.
# These are typically files within the security folder that must remain readable.
excluded_files = manager.py, core.py, utils.py, requirements.txt, README.md, salt.bin, self_destruct.bat, decrypt_legacy.py, config.ini

[Backup]
# Settings for the automated backup management system.
# Enable or disable pruning of old backups.
enable_pruning = true

# The maximum age of a backup in days. Backups older than this will be deleted.
retention_days = 30

# The number of recent backups of each type (zip, tar, mirror) to always keep, regardless of age.
retention_count = 3

[Integrity]
# The name of the file used to store the SHA256 hashes of encrypted files.
manifest_file = manifest.sha256

# --- Digital Signature Keys (for manifest protection) ---
# The name of the encrypted private key file.
private_key_file = private_key.pem

# The name of the public key file.
public_key_file = public_key.pem
