# import getpas
import json
from multiprocessing import Process
from linkedin_easy_apply import LinkedInEasyApply

# Load credentials from credentials.json
with open('credentials.json', 'r') as cred_file:
    credentials = json.load(cred_file)

def run_easy_apply_instance(idx, total_runs, search_term, location, credentials, job_mode=None):
    print(f"\n{'='*30}\nRun {idx+1} of {total_runs}\n{'='*30}")
    easy_apply = LinkedInEasyApply(headless=False, credentials=credentials)
    try:
        easy_apply.run_easy_apply_process(
            search_term=search_term, 
            location=location, 
            job_mode=job_mode
        )
    except Exception as e:
        print(f"Error during run {idx+1}: {e}")
    finally:
        try:
            easy_apply.cleanup()
        except Exception:
            pass

if __name__ == "__main__":
    # Ask user for job search mode
    print("LinkedIn Easy Apply - Batch Mode")
    print("=" * 40)
    print("Choose job search mode:")
    print("1. Normal search (with specific search terms)")
    print("2. Top job picks for you (LinkedIn recommendations)")
    print("=" * 40)
    
    while True:
        mode_choice = input("Enter your choice (1 or 2): ").strip()
        if mode_choice == "1":
            job_mode = "normal"
            break
        elif mode_choice == "2":
            job_mode = "top_picks"
            break
        else:
            print("Invalid choice. Please enter 1 or 2.")
    
    search_configs = []
    
    if job_mode == "normal":
        while True:
            try:
                num_runs_str = input("How many different job searches do you want to run? ").strip()
                if not num_runs_str: # Handle empty input
                    print("Please enter a number.")
                    continue
                num_runs = int(num_runs_str)
                if num_runs > 0:
                    break
                else:
                    print("Please enter a positive number.")
            except ValueError:
                print("Invalid input. Please enter a whole number.")

        # Get search terms and locations for normal mode
        for i in range(num_runs):
            print(f"\n--- Configuration for Search {i+1} ---")
            search_term = input(f"  Enter job search term: ").strip()
            location = input(f"  Enter job location (leave blank for any): ").strip()
            search_configs.append({"search_term": search_term, "location": location, "job_mode": "normal"})
    else:
        # For top picks mode, we just need one configuration
        print("\n'Top job picks' mode selected. This will run once.")
        search_configs.append({"search_term": "", "location": "", "job_mode": "top_picks"})

    total_runs = len(search_configs)
    processes = []
    for i, config in enumerate(search_configs):
        p = Process(target=run_easy_apply_instance, args=(i, total_runs, config["search_term"], config["location"], credentials, config["job_mode"]))
        p.start()
        processes.append(p)

    for p in processes:
        p.join()

    print("\nAll batch runs completed.")
