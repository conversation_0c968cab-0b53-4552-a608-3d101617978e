#!/usr/bin/env python
"""
LinkedIn JobSearch Advanced Test Suite
A professional suite for testing LinkedIn job search functionality.
"""

import os
import sys
import time
import logging
import subprocess
import platform
import urllib.parse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from linkedin_scraper import actions
from linkedin_scraper.job_search import JobSearch


# Configure logging
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(levelname)s - %(message)s',
                   datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger('linkedin_job_search')

# Suppress selenium logging
selenium_logger = logging.getLogger('selenium')
selenium_logger.setLevel(logging.WARNING)

# Suppress urllib3 logging
urllib3_logger = logging.getLogger('urllib3')
urllib3_logger.setLevel(logging.WARNING)


class LinkedInJobSearchTester:
    """Advanced test suite for LinkedIn job search functionality"""
    
    def __init__(self, headless=False, credentials=None):
        self.driver = None
        self.job_search = None
        self.headless = headless
        self.credentials = credentials or {}
    
    def setup(self):
        """Initialize WebDriver with optimal settings"""
        logger.info("Initializing Chrome WebDriver")
        chrome_options = Options()
        
        # Suppress Chrome logging and WebGL errors
        chrome_options.add_argument("--log-level=3")  # Only show fatal errors
        chrome_options.add_argument("--enable-unsafe-swiftshader")
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--silent")
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        
        # Add standard options
        if self.headless:
            chrome_options.add_argument("--headless=new")  # New headless mode
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        
        try:
            if platform.system() == "Windows":
                # Redirect Chrome's stderr on Windows
                subprocess.run('powershell $env:PYTHONIOENCODING="UTF-8"', shell=True)
                
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(5)
            return True
        except Exception as e:
            logger.error("=" * 60)
            logger.error("DRIVER INITIALIZATION FAILED")
            logger.error("-" * 60)
            logger.error(f"Error: {str(e)}")
            logger.error(f"System: {platform.system()} {platform.release()}")
            logger.error("=" * 60)
            return False
    
    def login(self):
        """Authenticate with LinkedIn"""
        if not self.driver:
            logger.error("Cannot login: WebDriver not initialized")
            return False
            
        logger.info("Authenticating with LinkedIn")
        try:
            email = self.credentials.get('email', os.environ.get('LINKEDIN_EMAIL', None))
            password = self.credentials.get('password', os.environ.get('LINKEDIN_PASSWORD', None))
            
            if not email or not password:
                logger.warning("No credentials provided. Prompting for input...")
                email = input("Enter LinkedIn email: ").strip()
                password = input("Enter LinkedIn password: ").strip()
            
            self.driver.get("https://www.linkedin.com/login")
            time.sleep(2)  # Allow page to load
            
            # Check if we're on the login page
            try:
                if "login" not in self.driver.current_url.lower():
                    logger.info("Already logged in")
                    return True
                    
                logger.info("Entering credentials...")
                actions.login(self.driver, email, password)
                time.sleep(3)  # Allow login to complete
                
                # Verify successful login
                if "feed" in self.driver.current_url or "linkedin.com/feed" in self.driver.current_url:
                    logger.info("Login successful ✅")
                    return True
                else:
                    logger.error("Login failed - redirected to: " + self.driver.current_url)
                    return False
            except Exception as e:
                logger.error(f"Login process error: {str(e)}")
                return False
                
        except Exception as e:
            logger.error("=" * 60)
            logger.error("AUTHENTICATION ERROR")
            logger.error("-" * 60)
            logger.error(f"{str(e)}")
            logger.error("=" * 60)
            return False
    
    def test_job_search_init(self):
        """Test JobSearch initialization"""
        logger.info("Testing JobSearch initialization")
        try:
            self.job_search = JobSearch(driver=self.driver, scrape=False)
            logger.info(f"JobSearch initialized - Base URL: {self.job_search.base_url}")
            return True
        except Exception as e:
            logger.error(f"JobSearch initialization failed: {str(e)}")
            return False
    
    def test_job_search_scrape(self):
        """Test job scraping functionality"""
        if not self.job_search:
            logger.error("Cannot scrape jobs: JobSearch not initialized")
            return False
            
        logger.info("Testing job scraping functionality")
        try:
            self.job_search.scrape(close_on_complete=False, scrape_recommended_jobs=True)
            
            results = {
                "recommended": len(self.job_search.recommended_jobs),
                "still_hiring": len(self.job_search.still_hiring),
                "more_jobs": len(self.job_search.more_jobs)
            }
            
            # Print detailed results
            logger.info("-" * 50)
            logger.info("JOBS FOUND SUMMARY:")
            logger.info(f"  Recommended Jobs: {results['recommended']}")
            logger.info(f"  Still Hiring Jobs: {results['still_hiring']}")
            logger.info(f"  More Jobs: {results['more_jobs']}")
            logger.info("-" * 50)
            
            # Show sample of found jobs if any
            all_jobs = (
                self.job_search.recommended_jobs + 
                self.job_search.still_hiring + 
                self.job_search.more_jobs
            )
            
            if all_jobs:
                logger.info("Sample job listings:")
                for i, job in enumerate(all_jobs[:3], 1):
                    logger.info(f"  {i}. {job.job_title} at {job.company} ({job.location})")
            
            has_jobs = results["recommended"] > 0 or results["still_hiring"] > 0 or results["more_jobs"] > 0
            if has_jobs:
                logger.info("✅ Job scraping successful")
            else:
                logger.warning("⚠️ Job scraping completed but no jobs were found")
            
            return has_jobs
        except Exception as e:
            logger.error("=" * 60)
            logger.error("JOB SCRAPING ERROR")
            logger.error("-" * 60)
            logger.error(f"{str(e)}")
            logger.error("=" * 60)
            return False
    
    def test_job_card_scraping(self):
        """Test individual job card scraping"""
        if not self.job_search or not self.driver:
            logger.error("Cannot scrape job card: Driver or JobSearch not initialized")
            return False
            
        logger.info("Testing job card scraping")
        try:
            self.driver.get(self.job_search.base_url)
            logger.info("Waiting for job cards to load...")
            time.sleep(3)
            
            # Find first job card with fallback selectors
            selectors = [
                ".jobs-job-board-list__item", 
                ".job-card-container", 
                ".job-card-list", 
                "[data-job-id]"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        logger.info(f"Found {len(elements)} job cards with selector: {selector}")
                        job = self.job_search.scrape_job_card(elements[0])
                        
                        # Format job details nicely
                        logger.info("-" * 50)
                        logger.info("JOB CARD DETAILS:")
                        logger.info(f"  Title: {job.job_title}")
                        logger.info(f"  Company: {job.company}")
                        logger.info(f"  Location: {job.location}")
                        logger.info(f"  URL: {job.linkedin_url}")
                        logger.info("-" * 50)
                        
                        logger.info("✅ Job card scraping successful")
                        return True
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {str(e)}")
                    continue
                    
            logger.warning("⚠️ No job cards found to test with any selector")
            return False
        except Exception as e:
            logger.error("=" * 60)
            logger.error("JOB CARD SCRAPING ERROR")
            logger.error("-" * 60)
            logger.error(f"{str(e)}")
            logger.error("=" * 60)
            return False
    
    def test_job_search_query(self, query="Python Developer"):
        """Test job search query functionality"""
        if not self.job_search:
            logger.error("Cannot perform search: JobSearch not initialized")
            return False
            
        logger.info(f"Testing job search with query: '{query}'")
        try:
            logger.info("Executing search request...")
            
            # Using modified search approach with additional scrolling
            url = os.path.join(self.job_search.base_url, "search") + f"?keywords={urllib.parse.quote(query)}&refresh=true"
            self.driver.get(url)
            
            # Wait for initial page load
            logger.info("Waiting for search results to load...")
            time.sleep(5)  # Increased wait time
            
            # Scroll down twice to load more jobs
            logger.info("Scrolling to load more job listings...")
            scroll_count = 20
            for i in range(1, scroll_count + 1):
                logger.info(f"Scroll {i}/{scroll_count} to load more jobs")
                
                # Scroll to middle then bottom of page
                self.driver.execute_script(f"window.scrollTo(0, document.body.scrollHeight * {i/(scroll_count+1)});")
                time.sleep(2)
                
                # Ensure we see new content by scrolling up and down slightly
                self.driver.execute_script("window.scrollBy(0, -200);")
                time.sleep(1)
                self.driver.execute_script("window.scrollBy(0, 200);")
                time.sleep(2)
            
            # Final scroll to bottom to ensure maximum content loaded
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            
            # Parse job listings after scrolling
            job_listings = []
            
            # Simplified direct approach - find job cards directly by data-job-id attribute
            logger.info("Searching for job cards with direct selectors...")
            try:
                # Try direct selectors first - from the HTML snippet you provided
                direct_selectors = [
                    "data-job-id"
                    "[data-job-id]",
                    "div.job-card-container--clickable",
                    "div.job-card-list",
                    "li.jobs-search-results__list-item"
                ]
                
                for selector in direct_selectors:
                    try:
                        job_cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if job_cards and len(job_cards) > 0:
                            logger.info(f"Found {len(job_cards)} job listings with direct selector: {selector}")
                            
                            # Process found job cards
                            for job_card in job_cards:
                                try:
                                    job = self.job_search.scrape_job_card(job_card)
                                    if job.job_title != "Error scraping job":  # Only add valid jobs
                                        job_listings.append(job)
                                except Exception as e:
                                    logger.debug(f"Failed to parse a job card: {str(e)}")
                            break
                    except Exception as e:
                        logger.debug(f"Direct selector {selector} failed: {str(e)}")
                
                if not job_listings:
                    logger.info("No results with direct selectors, trying container approach...")
                    
                    # Try to find main container first, then job cards
                    container_selectors = [
                        ".jobs-search-results-list",
                        ".jobs-search__results-list",
                        ".jobs-search-two-pane__results",
                        ".scaffold-layout__list"  # New LinkedIn layout
                    ]
                    
                    for container_selector in container_selectors:
                        try:
                            container = self.driver.find_element(By.CSS_SELECTOR, container_selector)
                            logger.info(f"Found results container with: {container_selector}")
                            
                            # Look for job cards within container
                            for card_selector in direct_selectors:
                                try:
                                    job_cards = container.find_elements(By.CSS_SELECTOR, card_selector)
                                    if job_cards and len(job_cards) > 0:
                                        logger.info(f"Found {len(job_cards)} job listings within container")
                                        
                                        # Process found job cards
                                        for job_card in job_cards:
                                            try:
                                                job = self.job_search.scrape_job_card(job_card)
                                                if job.job_title != "Error scraping job":  # Only add valid jobs
                                                    job_listings.append(job)
                                            except Exception as e:
                                                logger.debug(f"Failed to parse job card: {str(e)}")
                                        break
                                except:
                                    continue
                            if job_listings:
                                break
                        except:
                            continue
                
                # Last resort - take screenshots of the search results page to debug
                if not job_listings:
                    logger.warning("Using last resort approach to find job listings...")
                    # Try to extract any links that look like job links
                    try:
                        all_links = self.driver.find_elements(By.TAG_NAME, "a")
                        job_links = [link for link in all_links if "/jobs/view/" in link.get_attribute("href") or "currentJobId" in link.get_attribute("href")]
                        
                        logger.info(f"Found {len(job_links)} potential job links")
                        
                        for link in job_links[:10]:  # Limit to first 10 links
                            try:
                                # Find parent element that might be a job card
                                parent = link
                                for _ in range(5):  # Go up to 5 levels up
                                    if parent.tag_name in ["div", "li"]:
                                        job = self.job_search.scrape_job_card(parent)
                                        if job.job_title != "Error scraping job":
                                            job_listings.append(job)
                                        break
                                    parent = parent.find_element(By.XPATH, "./..")
                            except:
                                continue
                    except Exception as e:
                        logger.error(f"Last resort approach failed: {str(e)}")
                
                # Take screenshot if no job listings found for debugging
                if not job_listings:
                    try:
                        screenshot_path = "search_results_debug.png"
                        self.driver.save_screenshot(screenshot_path)
                        logger.warning(f"Saved screenshot to {screenshot_path} for debugging")
                    except:
                        pass
                        
            except Exception as e:
                logger.error(f"Error finding job listings: {str(e)}")
                        
            # Display search results
            logger.info("-" * 50)
            logger.info(f"SEARCH RESULTS FOR '{query}':")
            logger.info(f"  Total results: {len(job_listings)}")
            
            if job_listings:
                logger.info("\nTop search results:")
                for i, job in enumerate(job_listings[:5], 1):
                    logger.info(f"  {i}. {job.job_title}")
                    logger.info(f"     Company: {job.company}")
                    logger.info(f"     Location: {job.location}")
                    logger.info("")
                logger.info("✅ Job search successful")
            else:
                logger.warning(f"⚠️ No results found for '{query}'")
            logger.info("-" * 50)
            
            return len(job_listings) > 0
        except Exception as e:
            logger.error("=" * 60)
            logger.error("JOB SEARCH ERROR")
            logger.error("-" * 60)
            logger.error(f"Query: '{query}'")
            logger.error(f"Error: {str(e)}")
            logger.error("=" * 60)
            return False
    
    def run_all_tests(self):
        """Execute all tests sequentially"""
        results = {}
        
        if not self.setup():
            return {"setup": False}
        results["setup"] = True
        
        if not self.login():
            self.cleanup()
            return {**results, "login": False}
        results["login"] = True
        
        results["init"] = self.test_job_search_init()
        results["scrape"] = self.test_job_search_scrape()
        results["job_card"] = self.test_job_card_scraping()
        
        search_query = input("\nEnter job search term (or press Enter for default 'Python Developer'): ").strip()
        if not search_query:
            search_query = "Python Developer"
            
        results["search"] = self.test_job_search_query(search_query)
        
        self.cleanup()
        return results
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            logger.info("Cleaning up resources")
            self.driver.quit()


def main():
    """Main execution function"""
    try:
        print("\n" + "="*70)
        print(" "*20 + "LINKEDIN JOB SEARCH TEST SUITE")
        print("="*70 + "\n")
        
        # Use environment variables for credentials if available
        email = os.environ.get('LINKEDIN_EMAIL')
        password = os.environ.get('LINKEDIN_PASSWORD')
        
        # Fallback to hardcoded credentials if needed
        if not email or not password:
            email = "<EMAIL>"
            password = "badshaah"  # In production, use environment variables
            logger.warning("Using hardcoded credentials. Consider using environment variables.")
        
        credentials = {'email': email, 'password': password}
        
        # Display test configuration
        print("-"*70)
        print("TEST CONFIGURATION:")
        print(f"  • User: {email}")
        print(f"  • Headless Mode: Off")
        print(f"  • Platform: {platform.system()} {platform.release()}")
        print(f"  • Python: {platform.python_version()}")
        print("-"*70 + "\n")
        
        tester = LinkedInJobSearchTester(headless=False, credentials=credentials)
        logger.info("Starting test suite execution...")
        results = tester.run_all_tests()
        
        # Display results in a formatted table
        print("\n" + "="*70)
        print(" "*20 + "TEST RESULTS SUMMARY")
        print("="*70)
        
        # Print header
        print(f"{'TEST':<30}{'STATUS':<15}{'RESULT':<25}")
        print("-"*70)
        
        # Print test results
        for test, passed in results.items():
            test_name = test.replace('_', ' ').title()
            status = "✅ PASSED" if passed else "❌ FAILED"
            result = "Success" if passed else "Failed - See log for details"
            print(f"{test_name:<30}{status:<15}{result:<25}")
        
        # Calculate overall status
        all_passed = all(results.values())
        passed_count = sum(1 for v in results.values() if v)
        total = len(results)
        
        print("-"*70)
        print(f"Total Tests: {total}   Passed: {passed_count}   Failed: {total - passed_count}")
        print("="*70)
        print(f"OVERALL STATUS: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        print("="*70 + "\n")
        
        return 0 if all_passed else 1
    
    except KeyboardInterrupt:
        print("\n\n" + "!"*70)
        print("Test execution interrupted by user.")
        print("!"*70 + "\n")
        return 1
    except Exception as e:
        print("\n\n" + "!"*70)
        print(f"UNEXPECTED ERROR: {str(e)}")
        print("!"*70 + "\n")
        return 1


if __name__ == "__main__":
    sys.exit(main())