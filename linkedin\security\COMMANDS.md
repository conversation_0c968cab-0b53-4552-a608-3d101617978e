# Command Reference: Alpha Secure Manager CLI

This section documents all available commands for the security manager (`run.py`). Use these commands from the `security` directory.

---

## Usage

```sh
python run.py [command]
```

If no command is provided, the interactive menu will launch.

---

## Available Commands

### 1. `generate-salt`
Generates a new `salt.bin` file for key derivation. **Run this first on a new setup.**

**Example:**
```sh
python run.py generate-salt
```

---

### 2. `generate-keys`
Generates a new RSA key pair (`private_key.pem` and `public_key.pem`). You will be prompted to set a password for your private key.

**Example:**
```sh
python run.py generate-keys
```

---

### 3. `status`
Shows the current setup status, including which critical files are present or missing.

**Example:**
```sh
python run.py status
```

---

### 4. `force-decrypt`
**Recovery mode.** Attempts to decrypt files even if integrity checks fail. Prompts for the decryption password. Use only if normal decryption is blocked.

**Example:**
```sh
python run.py force-decrypt
```

---

### 5. `cleanup`
Deletes all generated security files, logs, and temporary data. Use to reset the security state after cloning or to start fresh. Prompts for confirmation.

**Example:**
```sh
python run.py cleanup
```

---

## Interactive Menu (No Command)

If you run `python run.py` with no arguments, you will see a menu with options for encryption, decryption, backup, obfuscation, alternative protection, and more. See the main README sections above for details on each menu option.

---

## Notes
- All commands must be run from the `security` directory.
- For advanced troubleshooting, see the Troubleshooting section above.
- For automation or scripting, you can call these commands from other scripts or task runners.

---

*This documentation is auto-generated and should be kept up to date with any changes to `run.py`.*
