#!/usr/bin/env python
"""
LinkedIn Scraper - Comprehensive Test Script
This script tests all the main functionality of the LinkedIn Scraper package.
"""

import os
import sys
import time
import traceback
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException
from linkedin_scraper import Person, Company, Job, JobSearch, actions

def test_setup():
    """Test the setup process"""
    print("\n=== Testing Setup ===")
    try:
        chrome_options = Options()
        # Uncomment the line below if you want to run in headless mode
        # chrome_options.add_argument("--headless")
        
        # Increase page load timeout
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)  # Increase timeout to 30 seconds
        print("✅ Successfully created Chrome driver")
        return driver
    except Exception as e:
        print(f"❌ Failed to create Chrome driver: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

def test_login(driver, email=None, password=None):
    """Test login functionality"""
    print("\n=== Testing Login ===")
    try:
        # If email and password are not provided, they will be prompted in terminal
        if not email:
            email = input("Enter LinkedIn email: ")
        if not password:
            password = input("Enter LinkedIn password: ")
        
        actions.login(driver, email, password)
        # Give some extra time for login to complete
        time.sleep(5)
        print("✅ Successfully logged in to LinkedIn")
    except Exception as e:
        print(f"❌ Failed to login: {str(e)}")
        traceback.print_exc()
        driver.quit()
        sys.exit(1)

def test_person_scraping(driver):
    """Test Person scraping functionality"""
    print("\n=== Testing Person Scraping ===")
    try:
        # Using a public LinkedIn profile for testing
        person = Person(
            "https://www.linkedin.com/in/andrew-ng-stanford/", 
            driver=driver,
            close_on_complete=False,
            time_to_wait_after_login=5  # Add wait time after login
        )
        
        # Test Person attributes
        print(f"Name: {person.name}")
        print(f"About: {person.about[:100]}..." if person.about else "About: None")
        print(f"Experiences count: {len(person.experiences)}")
        print(f"Educations count: {len(person.educations)}")
        print(f"Current company: {person.company}")
        print(f"Current job title: {person.job_title}")
        print("✅ Successfully scraped Person profile")
        return person
    except Exception as e:
        print(f"❌ Failed to scrape Person profile: {str(e)}")
        traceback.print_exc()
        return None

def test_company_scraping(driver):
    """Test Company scraping functionality"""
    print("\n=== Testing Company Scraping ===")
    try:
        # Using a public company profile for testing
        company = Company(
            "https://www.linkedin.com/company/google/", 
            driver=driver, 
            close_on_complete=False,
            # Set get_employees to False to speed up testing 
            # as it can be time consuming for large companies
            get_employees=False
        )
        
        # Test Company attributes
        print(f"Name: {company.name}")
        print(f"About Us: {company.about_us[:100]}..." if company.about_us else "About Us: None")
        print(f"Website: {company.website}")
        print(f"Headquarters: {company.headquarters}")
        print(f"Founded: {company.founded}")
        print(f"Company Type: {company.company_type}")
        print(f"Company Size: {company.company_size}")
        print(f"Specialties count: {len(company.specialties.split(',')) if isinstance(company.specialties, str) else 0}")
        print(f"Showcase pages count: {len(company.showcase_pages)}")
        print(f"Affiliated companies count: {len(company.affiliated_companies)}")
        print("✅ Successfully scraped Company profile")
        return company
    except Exception as e:
        print(f"❌ Failed to scrape Company profile: {str(e)}")
        traceback.print_exc()
        return None

def test_job_scraping(driver):
    """Test Job scraping functionality"""
    print("\n=== Testing Job Scraping ===")
    try:
        # Navigate directly to a job listing URL 
        # (more reliable than finding one through search)
        job_url = "https://www.linkedin.com/jobs/view/engineer-at-microsoft-3823371368"
        job = Job(job_url, driver=driver, close_on_complete=False)
        
        # Test Job attributes
        print(f"Title: {job.job_title}")
        print(f"Company: {job.company}")
        print(f"Location: {job.location}")
        print(f"Description: {job.job_description[:100]}..." if job.job_description else "Description: None")
        print("✅ Successfully scraped Job listing")
        return job
    except Exception as e:
        print(f"❌ Failed to scrape Job listing: {str(e)}")
        traceback.print_exc()
        return None

def test_job_search(driver):
    """Test JobSearch functionality"""
    print("\n=== Testing Job Search ===")
    try:
        # Initialize JobSearch object with scrape=False to manually control scraping
        job_search = JobSearch(driver=driver, close_on_complete=False, scrape=False)
        
        # Search for jobs with a common term
        job_listings = job_search.search("Data Analyst")
        
        # Test JobSearch results
        print(f"Search returned {len(job_listings)} job listings")
        if job_listings and len(job_listings) > 0:
            print(f"First job title: {job_listings[0].job_title}")
            print(f"First job company: {job_listings[0].company}")
        
        print("✅ Successfully performed Job Search")
        return job_search
    except Exception as e:
        print(f"❌ Failed to perform Job Search: {str(e)}")
        traceback.print_exc()
        return None

def main():
    """Main test function"""
    print("LinkedIn Scraper - Test Suite")
    print("-" * 50)
    # Test setup
    driver = test_setup()
    # Test login (uncomment and add your credentials for automated login)
    # You can replace these with your actual LinkedIn credentials
    email = "<EMAIL>" 
    password = "badshaah"
    test_login(driver, email=email, password=password)
    # Allow some time for the login to complete fully
    time.sleep(5)
    # Run tests - if one test fails, try to continue with the others
    results = {}
    # # Test Person scraping
    # try:
    #     person = test_person_scraping(driver)
    #     results["person"] = person is not None
    # except:
    #     results["person"] = False
    #     print("Person test failed with an unhandled exception")
    
    # # Test Company scraping
    # try:
    #     company = test_company_scraping(driver)
    #     results["company"] = company is not None
    # except:
    #     results["company"] = False
    #     print("Company test failed with an unhandled exception")
    
    # # Test Job scraping
    # try:
    #     job = test_job_scraping(driver)
    #     results["job"] = job is not None
    # except:
    #     results["job"] = False
    #     print("Job test failed with an unhandled exception")
    # Test Job Search
    try:
        job_search = test_job_search(driver)
        results["job_search"] = job_search is not None
    except:
        results["job_search"] = False
        print("Job Search test failed with an unhandled exception")
    # Print test summary
    print("\n=== Test Summary ===")
    print(f"Person scraping: {'✅' if results.get('person', False) else '❌'}")
    print(f"Company scraping: {'✅' if results.get('company', False) else '❌'}")
    print(f"Job scraping: {'✅' if results.get('job', False) else '❌'}")
    print(f"Job search: {'✅' if results.get('job_search', False) else '❌'}")
    
    print("\n=== Closing browser ===")
    driver.quit()

if __name__ == "__main__":
    main()