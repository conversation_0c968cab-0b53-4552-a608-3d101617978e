@echo off
echo.
echo ====================================================================
echo  WARNING: IRREVERSIBLE ACTION
echo ====================================================================
echo.
echo  This script will permanently delete the entire project directory:
echo  %~dp0..\..
echo.
echo  All files and folders within will be lost forever.
echo.
echo  Press Ctrl+C to ABORT now, or press any other key to proceed...
echo ====================================================================
pause > nul

:: Create a temporary batch file in the %TEMP% directory to perform the deletion
:: This is necessary because a script cannot delete the directory it is running from.
echo @echo off > "%TEMP%\delete_project.bat"
echo echo Deleting project directory... Please wait. >> "%TEMP%\delete_project.bat"
echo timeout /t 2 /nobreak > nul >> "%TEMP%\delete_project.bat"
echo rmdir /s /q "%~dp0..\.." >> "%TEMP%\delete_project.bat"
echo del "%%~f0" >> "%TEMP%\delete_project.bat"

:: Start the temporary batch file in a new, minimized window and exit this script.
:: The current script terminates, releasing its lock on the directory.
start "" /min "%TEMP%\delete_project.bat"
