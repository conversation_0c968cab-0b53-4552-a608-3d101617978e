import os

def generate_salt():
    """Generates a 16-byte salt and saves it to salt.bin in the parent directory."""
    # We want salt.bin in the main 'security' folder, not 'security/src/tools'
    salt_path = os.path.join(os.path.dirname(__file__), '..', '..', 'salt.bin')
    salt = os.urandom(16)
    try:
        with open(salt_path, 'wb') as f:
            f.write(salt)
        print(f"Successfully generated 'salt.bin' in: {os.path.dirname(salt_path)}")
        print("Keep this file in the 'security' directory. It is needed for encryption and decryption.")
    except IOError as e:
        print(f"Error: Could not write salt file. {e}")

if __name__ == "__main__":
    generate_salt()
