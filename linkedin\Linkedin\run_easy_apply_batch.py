import getpass
import json
from multiprocessing import Process
from linkedin_easy_apply import LinkedInEasyApply

# Load credentials from credentials.json
with open('credentials.json', 'r') as cred_file:
    credentials = json.load(cred_file)

def run_easy_apply_instance(idx, search_term, location, credentials):
    print(f"\n{'='*30}\nRun {idx+1} of 6\n{'='*30}")
    easy_apply = LinkedInEasyApply(headless=False, credentials=credentials)
    try:
        easy_apply.run_easy_apply_process(search_term=search_term, location=location)
    except Exception as e:
        print(f"Error during run {idx+1}: {e}")
    finally:
        try:
            easy_apply.cleanup()
        except Exception:
            pass

if __name__ == "__main__":
    # Prompt for 6 sets of search term and location
    search_terms = []
    locations = []
    num_runs = 6
    for i in range(1, num_runs + 1):
        print(f"\nInput for run {i}:")
        search_terms.append(input(f"  Enter job search term for run {i}: ").strip())
        locations.append(input(f"  Enter job location for run {i} (leave blank for any): ").strip())

    processes = []
    for i in range(num_runs):
        p = Process(target=run_easy_apply_instance, args=(i, search_terms[i], locations[i], credentials))
        p.start()
        processes.append(p)

    for p in processes:
        p.join()

    print("\nAll runs completed.")
