import os
import base64
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken

# --- Symmetric Encryption ---

def derive_key(password: str, salt: bytes, key_length: int = 32) -> bytes:
    """Derives a key of specified length from a password and salt using PBKDF2."""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=key_length,
        salt=salt,
        iterations=480000, # NIST recommendation for PBKDF2
        backend=default_backend()
    )
    return kdf.derive(password.encode())

def get_key_from_password(password: str, salt: bytes) -> bytes:
    """Derives a Fernet-compatible key from a password and salt."""
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=480000,  # Increased iterations
        backend=default_backend()
    )
    return base64.urlsafe_b64encode(kdf.derive(password.encode()))

# --- Level 1: Fernet (AES-128-CBC) ---
def encrypt_fernet(data: bytes, key: bytes) -> bytes:
    """Encrypts data using Fernet (AES-128-CBC). Key must be 32 bytes, URL-safe base64-encoded."""
    f = Fernet(key)
    return f.encrypt(data)

def decrypt_fernet(token: bytes, key: bytes) -> bytes:
    """
    Decrypts data using Fernet.
    """
    f = Fernet(key)
    try:
        return f.decrypt(token)
    except InvalidToken:
        raise ValueError("Decryption failed. The primary data encryption key is invalid or the data is corrupt.")

# --- Level 2: AES-256-GCM (Paranoid Mode) ---
def encrypt_aes_gcm(data: bytes, key: bytes) -> bytes:
    """Encrypts data using AES-256-GCM. Returns nonce + ciphertext + tag."""
    if len(key) != 32:
        raise ValueError("AES-256-GCM requires a 32-byte key.")
    
    nonce = os.urandom(12)  # GCM recommended nonce size
    cipher = Cipher(algorithms.AES(key), modes.GCM(nonce), backend=default_backend())
    encryptor = cipher.encryptor()
    
    ct = encryptor.update(data) + encryptor.finalize()
    
    # Return nonce, ciphertext, and tag concatenated
    return nonce + ct + encryptor.tag

def decrypt_aes_gcm(token: bytes, key: bytes) -> bytes:
    """Decrypts data using AES-256-GCM."""
    if len(key) != 32:
        raise ValueError("AES-256-GCM requires a 32-byte key.")
    
    try:
        # Extract nonce, ciphertext, and tag
        nonce = token[:12]
        tag = token[-16:]
        ct = token[12:-16]

        cipher = Cipher(algorithms.AES(key), modes.GCM(nonce, tag), backend=default_backend())
        decryptor = cipher.decryptor()
        
        return decryptor.update(ct) + decryptor.finalize()
    except Exception as e:
        raise ValueError(f"Decryption failed. Invalid key, corrupted data, or authentication tag mismatch. Error: {e}")

# --- Asymmetric Encryption & Digital Signatures (for Integrity) ---

def generate_key_pair():
    """Generates a new RSA private/public key pair."""
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=4096,
        backend=default_backend()
    )
    public_key = private_key.public_key()
    return private_key, public_key

def save_keys(private_key, public_key, private_key_path, public_key_path, password):
    """Saves the private key (encrypted) and public key (unencrypted) to disk."""
    # Serialize and save the private key with password-based encryption
    pem_private = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.BestAvailableEncryption(password.encode())
    )
    with open(private_key_path, 'wb') as f:
        f.write(pem_private)

    # Serialize and save the public key
    pem_public = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    with open(public_key_path, 'wb') as f:
        f.write(pem_public)

def load_private_key(private_key_path, password):
    """Loads and decrypts a private key from a PEM file."""
    with open(private_key_path, 'rb') as f:
        private_key = serialization.load_pem_private_key(
            f.read(),
            password=password.encode(),
            backend=default_backend()
        )
    return private_key

def load_public_key(public_key_path):
    """Loads a public key from a PEM file."""
    with open(public_key_path, 'rb') as f:
        public_key = serialization.load_pem_public_key(
            f.read(),
            backend=default_backend()
        )
    return public_key

def sign_data(private_key, data):
    """Signs data using the private key."""
    signature = private_key.sign(
        data,
        padding.PSS(
            mgf=padding.MGF1(hashes.SHA256()),
            salt_length=padding.PSS.MAX_LENGTH
        ),
        hashes.SHA256()
    )
    return signature

def verify_signature(public_key, signature, data):
    """Verifies a signature against the data and public key. Returns True if valid."""
    try:
        public_key.verify(
            signature,
            data,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return True
    except Exception: # Catches InvalidSignature
        return False
