import getpass
import logging
import os
from logging.handlers import RotatingFileHandler

# --- Logger Setup ---
def setup_logger():
    """Sets up a rotating file logger and a console handler."""
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'security.log')

    logger = logging.getLogger('SecurityManager')
    logger.setLevel(logging.INFO)

    # Prevent duplicate handlers if logger is already configured
    if logger.hasHandlers():
        return logger

    # File Handler (for detailed logs)
    # Rotates logs when they reach 2MB, keeps 5 backup logs.
    fh = RotatingFileHandler(log_file, maxBytes=2*1024*1024, backupCount=5)
    fh.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    fh.setFormatter(file_formatter)
    logger.addHandler(fh)

    # Console Handler (for user-facing messages)
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    # Use a simpler format for the console to keep it clean
    console_formatter = logging.Formatter('%(message)s')
    ch.setFormatter(console_formatter)
    logger.addHandler(ch)

    return logger

# --- End Logger Setup ---

def get_password(prompt="Enter your secret password: ", confirm=False, confirm_prompt="Confirm your secret password: "):
    """Gets password securely from user input, allowing for a custom prompt and optional confirmation."""
    password = getpass.getpass(prompt)
    if confirm:
        password_confirm = getpass.getpass(confirm_prompt)
        if password != password_confirm:
            print("Passwords do not match. Aborting.")
            return None
    if not password:
        print("Password cannot be empty. Aborting.")
        return None
    return password
