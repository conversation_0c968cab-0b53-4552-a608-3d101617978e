2025-06-21 23:12:58,160 - INFO - --- Security Manager Started ---
2025-06-21 23:12:58,161 - INFO - 
--- Project Security & Backup Manager (v3.0 - Signed Integrity) ---
2025-06-21 23:12:58,161 - INFO - 1. Encrypt Project
2025-06-21 23:12:58,162 - INFO - 2. Decrypt Project
2025-06-21 23:12:58,162 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:12:58,163 - INFO - 4. Create Project Backup
2025-06-21 23:12:58,163 - INFO - 5. Manage Backups (<PERSON>run<PERSON> Old)
2025-06-21 23:12:58,164 - INFO - 6. Obfuscate Project
2025-06-21 23:12:58,164 - INFO - 7. Self-Destruct Project (DANGEROUS)
2025-06-21 23:12:58,165 - INFO - 8. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:12:58,165 - INFO - 9. Check Setup Status
2025-06-21 23:12:58,166 - INFO - 10. Cleanup and Start New
2025-06-21 23:12:58,166 - INFO - 11. Exit
2025-06-21 23:13:06,514 - INFO - User selected option: '6'
2025-06-21 23:13:06,515 - INFO - Initiating obfuscation process.
2025-06-21 23:13:06,516 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:13:06,518 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:13:06,520 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:13:06,520 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:13:08,464 - ERROR - An error occurred during obfuscation: Command '['pyarmor-7', 'obfuscate', '--recursive', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:13:08,465 - INFO - 
--- Project Security & Backup Manager (v3.0 - Signed Integrity) ---
2025-06-21 23:13:08,466 - INFO - 1. Encrypt Project
2025-06-21 23:13:08,468 - INFO - 2. Decrypt Project
2025-06-21 23:13:08,469 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:13:08,469 - INFO - 4. Create Project Backup
2025-06-21 23:13:08,470 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:13:08,471 - INFO - 6. Obfuscate Project
2025-06-21 23:13:08,473 - INFO - 7. Self-Destruct Project (DANGEROUS)
2025-06-21 23:13:08,474 - INFO - 8. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:13:08,475 - INFO - 9. Check Setup Status
2025-06-21 23:13:08,476 - INFO - 10. Cleanup and Start New
2025-06-21 23:13:08,476 - INFO - 11. Exit
2025-06-21 23:13:30,222 - INFO - User selected option: '10'
2025-06-21 23:13:37,182 - INFO - Cleanup aborted by user.
2025-06-21 23:13:37,183 - INFO - 
--- Project Security & Backup Manager (v3.0 - Signed Integrity) ---
2025-06-21 23:13:37,184 - INFO - 1. Encrypt Project
2025-06-21 23:13:37,185 - INFO - 2. Decrypt Project
2025-06-21 23:13:37,185 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:13:37,185 - INFO - 4. Create Project Backup
2025-06-21 23:13:37,186 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:13:37,186 - INFO - 6. Obfuscate Project
2025-06-21 23:13:37,186 - INFO - 7. Self-Destruct Project (DANGEROUS)
2025-06-21 23:13:37,187 - INFO - 8. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:13:37,188 - INFO - 9. Check Setup Status
2025-06-21 23:13:37,188 - INFO - 10. Cleanup and Start New
2025-06-21 23:13:37,188 - INFO - 11. Exit
2025-06-21 23:13:38,424 - INFO - User selected option: '11'
2025-06-21 23:13:38,425 - INFO - --- Exiting Security Manager ---
2025-06-21 23:20:23,902 - INFO - --- Security Manager Started ---
2025-06-21 23:20:23,902 - INFO - 
--- Project Security & Backup Manager (v3.0 - Signed Integrity) ---
2025-06-21 23:20:23,903 - INFO - 1. Encrypt Project
2025-06-21 23:20:23,905 - INFO - 2. Decrypt Project
2025-06-21 23:20:23,905 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:20:23,905 - INFO - 4. Create Project Backup
2025-06-21 23:20:23,906 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:20:23,907 - INFO - 6. Obfuscate Project
2025-06-21 23:20:23,907 - INFO - 7. Self-Destruct Project (DANGEROUS)
2025-06-21 23:20:23,908 - INFO - 8. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:20:23,908 - INFO - 9. Check Setup Status
2025-06-21 23:20:23,909 - INFO - 10. Cleanup and Start New
2025-06-21 23:20:23,909 - INFO - 11. Exit
2025-06-21 23:20:25,996 - INFO - User selected option: '6'
2025-06-21 23:20:25,996 - INFO - Initiating obfuscation process.
2025-06-21 23:20:25,997 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:20:25,998 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:20:25,998 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:20:25,999 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:20:25,999 - WARNING - Note: PyArmor trial version has a 32KB limit per code object.
2025-06-21 23:20:26,000 - WARNING - Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.
2025-06-21 23:20:26,001 - INFO - Attempting obfuscation with exclusions to avoid trial limitations...
2025-06-21 23:20:26,584 - ERROR - Obfuscation failed: Command '['pyarmor-7', 'obfuscate', '--recursive', '--exclude', 'Linkedin', '--exclude', '*.pyc', '--exclude', '__pycache__', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:20:26,584 - ERROR - 
--- TRIAL VERSION LIMITATION DETECTED ---
2025-06-21 23:20:26,584 - ERROR - The trial version of PyArmor cannot obfuscate files larger than 32KB.
2025-06-21 23:20:26,585 - INFO - 
Alternative solutions:
2025-06-21 23:20:26,586 - INFO - 1. Purchase PyArmor full version to remove size limitations
2025-06-21 23:20:26,587 - INFO - 2. Split large Python files into smaller modules
2025-06-21 23:20:26,587 - INFO - 3. Use alternative obfuscation tools
2025-06-21 23:20:26,588 - INFO - 4. Continue without obfuscation (encryption still provides protection)
2025-06-21 23:20:51,348 - INFO - 
--- Attempting Selective Obfuscation ---
2025-06-21 23:20:51,352 - INFO - Found 5 files suitable for obfuscation
2025-06-21 23:20:51,354 - INFO - Found 2 files too large for trial version:
2025-06-21 23:20:51,354 - INFO -   - linkedin_easy_apply.py (220,376 bytes)
2025-06-21 23:20:51,355 - INFO -   - Linkedin\linkedin_easy_apply.py (220,376 bytes)
2025-06-21 23:20:51,716 - WARNING - Copied unobfuscated: Linkedin\meta-llama_8B.py
2025-06-21 23:20:52,126 - WARNING - Copied unobfuscated: Linkedin\run_easy_apply_batch.py
2025-06-21 23:20:52,620 - WARNING - Copied unobfuscated: Linkedin\run_job_search.py
2025-06-21 23:20:53,260 - WARNING - Copied unobfuscated: Linkedin\test_all_job_search_functions.py
2025-06-21 23:20:54,054 - INFO - Obfuscated: Linkedin\test_results\linkedin_scraper_test.py
2025-06-21 23:20:54,054 - INFO - Successfully obfuscated 1/5 small files
2025-06-21 23:20:54,112 - INFO - 
Selective obfuscation complete. Output in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:20:54,113 - WARNING - Large files have been copied unobfuscated due to trial version limitations.
2025-06-21 23:20:54,115 - INFO - 
--- Project Security & Backup Manager (v3.0 - Signed Integrity) ---
2025-06-21 23:20:54,115 - INFO - 1. Encrypt Project
2025-06-21 23:20:54,117 - INFO - 2. Decrypt Project
2025-06-21 23:20:54,118 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:20:54,120 - INFO - 4. Create Project Backup
2025-06-21 23:20:54,121 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:20:54,123 - INFO - 6. Obfuscate Project
2025-06-21 23:20:54,124 - INFO - 7. Self-Destruct Project (DANGEROUS)
2025-06-21 23:20:54,124 - INFO - 8. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:20:54,125 - INFO - 9. Check Setup Status
2025-06-21 23:20:54,127 - INFO - 10. Cleanup and Start New
2025-06-21 23:20:54,129 - INFO - 11. Exit
2025-06-21 23:24:53,869 - INFO - --- Security Manager Started ---
2025-06-21 23:24:53,897 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:24:53,898 - INFO - 1. Encrypt Project
2025-06-21 23:24:53,899 - INFO - 2. Decrypt Project
2025-06-21 23:24:53,899 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:24:53,900 - INFO - 4. Create Project Backup
2025-06-21 23:24:53,900 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:24:53,900 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:24:53,901 - INFO - 7. Alternative Protection Methods
2025-06-21 23:24:53,901 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:24:53,901 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:24:53,902 - INFO - 10. Check Setup Status
2025-06-21 23:24:53,902 - INFO - 11. Cleanup and Start New
2025-06-21 23:24:53,903 - INFO - 12. Exit
2025-06-21 23:25:01,370 - INFO - User selected option: '6'
2025-06-21 23:25:01,371 - INFO - Initiating obfuscation process.
2025-06-21 23:25:01,371 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:25:01,372 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:25:01,373 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:25:01,373 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:25:01,373 - WARNING - Note: PyArmor trial version has a 32KB limit per code object.
2025-06-21 23:25:01,374 - WARNING - Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.
2025-06-21 23:25:01,374 - INFO - Attempting obfuscation with exclusions to avoid trial limitations...
2025-06-21 23:25:01,919 - ERROR - Obfuscation failed: Command '['pyarmor-7', 'obfuscate', '--recursive', '--exclude', 'Linkedin', '--exclude', '*.pyc', '--exclude', '__pycache__', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:25:01,920 - ERROR - 
--- TRIAL VERSION LIMITATION DETECTED ---
2025-06-21 23:25:01,920 - ERROR - The trial version of PyArmor cannot obfuscate files larger than 32KB.
2025-06-21 23:25:01,920 - INFO - 
Alternative solutions:
2025-06-21 23:25:01,922 - INFO - 1. Purchase PyArmor full version to remove size limitations
2025-06-21 23:25:01,922 - INFO - 2. Split large Python files into smaller modules
2025-06-21 23:25:01,923 - INFO - 3. Use alternative obfuscation tools
2025-06-21 23:25:01,924 - INFO - 4. Continue without obfuscation (encryption still provides protection)
2025-06-21 23:25:11,921 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:25:11,922 - INFO - 1. Encrypt Project
2025-06-21 23:25:11,923 - INFO - 2. Decrypt Project
2025-06-21 23:25:11,924 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:25:11,925 - INFO - 4. Create Project Backup
2025-06-21 23:25:11,925 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:25:11,926 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:25:11,928 - INFO - 7. Alternative Protection Methods
2025-06-21 23:25:11,929 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:25:11,930 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:25:11,930 - INFO - 10. Check Setup Status
2025-06-21 23:25:11,931 - INFO - 11. Cleanup and Start New
2025-06-21 23:25:11,932 - INFO - 12. Exit
2025-06-21 23:25:18,069 - INFO - User selected option: '6'
2025-06-21 23:25:18,070 - INFO - Initiating obfuscation process.
2025-06-21 23:25:18,071 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:25:18,072 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:25:18,072 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:25:18,073 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:25:18,074 - WARNING - Note: PyArmor trial version has a 32KB limit per code object.
2025-06-21 23:25:18,074 - WARNING - Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.
2025-06-21 23:25:18,075 - INFO - Attempting obfuscation with exclusions to avoid trial limitations...
2025-06-21 23:25:18,588 - ERROR - Obfuscation failed: Command '['pyarmor-7', 'obfuscate', '--recursive', '--exclude', 'Linkedin', '--exclude', '*.pyc', '--exclude', '__pycache__', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:25:18,589 - ERROR - 
--- TRIAL VERSION LIMITATION DETECTED ---
2025-06-21 23:25:18,589 - ERROR - The trial version of PyArmor cannot obfuscate files larger than 32KB.
2025-06-21 23:25:18,591 - INFO - 
Alternative solutions:
2025-06-21 23:25:18,592 - INFO - 1. Purchase PyArmor full version to remove size limitations
2025-06-21 23:25:18,593 - INFO - 2. Split large Python files into smaller modules
2025-06-21 23:25:18,594 - INFO - 3. Use alternative obfuscation tools
2025-06-21 23:25:18,595 - INFO - 4. Continue without obfuscation (encryption still provides protection)
2025-06-21 23:25:27,502 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:25:27,502 - INFO - 1. Encrypt Project
2025-06-21 23:25:27,504 - INFO - 2. Decrypt Project
2025-06-21 23:25:27,504 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:25:27,504 - INFO - 4. Create Project Backup
2025-06-21 23:25:27,505 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:25:27,506 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:25:27,506 - INFO - 7. Alternative Protection Methods
2025-06-21 23:25:27,507 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:25:27,507 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:25:27,508 - INFO - 10. Check Setup Status
2025-06-21 23:25:27,508 - INFO - 11. Cleanup and Start New
2025-06-21 23:25:27,509 - INFO - 12. Exit
2025-06-21 23:25:37,239 - INFO - User selected option: '6'
2025-06-21 23:25:37,240 - INFO - Initiating obfuscation process.
2025-06-21 23:25:37,240 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:25:37,241 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:25:37,241 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:25:37,242 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:25:37,242 - WARNING - Note: PyArmor trial version has a 32KB limit per code object.
2025-06-21 23:25:37,242 - WARNING - Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.
2025-06-21 23:25:37,243 - INFO - Attempting obfuscation with exclusions to avoid trial limitations...
2025-06-21 23:25:37,680 - ERROR - Obfuscation failed: Command '['pyarmor-7', 'obfuscate', '--recursive', '--exclude', 'Linkedin', '--exclude', '*.pyc', '--exclude', '__pycache__', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:25:37,680 - ERROR - 
--- TRIAL VERSION LIMITATION DETECTED ---
2025-06-21 23:25:37,681 - ERROR - The trial version of PyArmor cannot obfuscate files larger than 32KB.
2025-06-21 23:25:37,683 - INFO - 
Alternative solutions:
2025-06-21 23:25:37,684 - INFO - 1. Purchase PyArmor full version to remove size limitations
2025-06-21 23:25:37,685 - INFO - 2. Split large Python files into smaller modules
2025-06-21 23:25:37,685 - INFO - 3. Use alternative obfuscation tools
2025-06-21 23:25:37,686 - INFO - 4. Continue without obfuscation (encryption still provides protection)
2025-06-21 23:25:45,304 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:25:45,305 - INFO - 1. Encrypt Project
2025-06-21 23:25:45,306 - INFO - 2. Decrypt Project
2025-06-21 23:25:45,306 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:25:45,307 - INFO - 4. Create Project Backup
2025-06-21 23:25:45,307 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:25:45,308 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:25:45,308 - INFO - 7. Alternative Protection Methods
2025-06-21 23:25:45,309 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:25:45,309 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:25:45,310 - INFO - 10. Check Setup Status
2025-06-21 23:25:45,310 - INFO - 11. Cleanup and Start New
2025-06-21 23:25:45,311 - INFO - 12. Exit
2025-06-21 23:25:47,014 - INFO - --- Exiting Security Manager ---
2025-06-21 23:26:20,564 - INFO - --- Security Manager Started ---
2025-06-21 23:26:20,566 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:26:20,567 - INFO - 1. Encrypt Project
2025-06-21 23:26:20,568 - INFO - 2. Decrypt Project
2025-06-21 23:26:20,569 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:26:20,570 - INFO - 4. Create Project Backup
2025-06-21 23:26:20,572 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:26:20,572 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:26:20,572 - INFO - 7. Alternative Protection Methods
2025-06-21 23:26:20,574 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:26:20,574 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:26:20,576 - INFO - 10. Check Setup Status
2025-06-21 23:26:20,577 - INFO - 11. Cleanup and Start New
2025-06-21 23:26:20,578 - INFO - 12. Exit
2025-06-21 23:26:36,539 - INFO - User selected option: '7'
2025-06-21 23:26:36,539 - INFO - Opening alternative protection methods.
2025-06-21 23:26:36,540 - INFO - 
--- Alternative Code Protection Methods ---
2025-06-21 23:26:36,541 - INFO - 1. Split Large Files (PyArmor Compatible)
2025-06-21 23:26:36,541 - INFO - 2. Python Minifier + Obfuscation
2025-06-21 23:26:36,542 - INFO - 3. Nuitka Compilation (Recommended)
2025-06-21 23:26:36,543 - INFO - 4. PyInstaller + UPX Packing
2025-06-21 23:26:36,544 - INFO - 5. Custom Source Encryption
2025-06-21 23:26:36,545 - INFO - 6. Cython Compilation
2025-06-21 23:26:36,545 - INFO - 7. View Detailed Guide
2025-06-21 23:26:36,546 - INFO - 8. Back to Main Menu
2025-06-21 23:26:45,806 - INFO - User selected alternative protection: '3'
2025-06-21 23:26:45,807 - INFO - 
--- Nuitka Compilation Protection ---
2025-06-21 23:26:45,873 - WARNING - Nuitka not installed. Installing...
2025-06-21 23:28:46,626 - INFO - Available entry scripts:
2025-06-21 23:28:46,627 - INFO -   1. linkedin_easy_apply.py
2025-06-21 23:29:47,201 - INFO - Compiling linkedin_easy_apply.py with Nuitka...
2025-06-21 23:29:47,775 - ERROR - Nuitka compilation failed: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe', '-m', 'nuitka', '--standalone', '--output-dir', 'C:\\Users\\<USER>\\linkedin\\nuitka_dist', '--assume-yes-for-downloads', 'C:\\Users\\<USER>\\linkedin\\linkedin_easy_apply.py']' returned non-zero exit status 2.
2025-06-21 23:29:47,776 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:29:47,776 - INFO - 1. Encrypt Project
2025-06-21 23:29:47,777 - INFO - 2. Decrypt Project
2025-06-21 23:29:47,778 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:29:47,778 - INFO - 4. Create Project Backup
2025-06-21 23:29:47,778 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:29:47,780 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:29:47,781 - INFO - 7. Alternative Protection Methods
2025-06-21 23:29:47,782 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:29:47,783 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:29:47,784 - INFO - 10. Check Setup Status
2025-06-21 23:29:47,784 - INFO - 11. Cleanup and Start New
2025-06-21 23:29:47,785 - INFO - 12. Exit
2025-06-21 23:30:54,420 - INFO - --- Exiting Security Manager ---
2025-06-21 23:33:28,049 - INFO - --- Security Manager Started ---
2025-06-21 23:33:28,051 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:33:28,052 - INFO - 1. Encrypt Project
2025-06-21 23:33:28,053 - INFO - 2. Decrypt Project
2025-06-21 23:33:28,054 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:33:28,055 - INFO - 4. Create Project Backup
2025-06-21 23:33:28,056 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:33:28,057 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:33:28,058 - INFO - 7. Alternative Protection Methods
2025-06-21 23:33:28,059 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:33:28,060 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:33:28,061 - INFO - 10. Check Setup Status
2025-06-21 23:33:28,062 - INFO - 11. Cleanup and Start New
2025-06-21 23:33:28,063 - INFO - 12. Exit
2025-06-21 23:33:44,859 - INFO - User selected option: '7'
2025-06-21 23:33:44,860 - INFO - Opening alternative protection methods.
2025-06-21 23:33:44,861 - INFO - 
--- Alternative Code Protection Methods ---
2025-06-21 23:33:44,862 - INFO - 1. Split Large Files (PyArmor Compatible)
2025-06-21 23:33:44,863 - INFO - 2. Python Minifier + Obfuscation
2025-06-21 23:33:44,863 - INFO - 3. Nuitka Compilation (Recommended)
2025-06-21 23:33:44,864 - INFO - 4. PyInstaller + UPX Packing
2025-06-21 23:33:44,864 - INFO - 5. Custom Source Encryption
2025-06-21 23:33:44,865 - INFO - 6. Cython Compilation
2025-06-21 23:33:44,866 - INFO - 7. View Detailed Guide
2025-06-21 23:33:44,867 - INFO - 8. Back to Main Menu
2025-06-21 23:33:53,670 - INFO - User selected alternative protection: '2'
2025-06-21 23:33:53,671 - INFO - 
--- Python Minifier Protection ---
2025-06-21 23:33:53,703 - WARNING - python-minifier not installed. Installing...
2025-06-21 23:33:57,350 - INFO - Found 17 Python files to minify
2025-06-21 23:33:57,373 - ERROR - Minification failed: minify() got an unexpected keyword argument 'remove_variable_annotations'
2025-06-21 23:33:57,374 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:33:57,374 - INFO - 1. Encrypt Project
2025-06-21 23:33:57,375 - INFO - 2. Decrypt Project
2025-06-21 23:33:57,376 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:33:57,376 - INFO - 4. Create Project Backup
2025-06-21 23:33:57,377 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:33:57,377 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:33:57,378 - INFO - 7. Alternative Protection Methods
2025-06-21 23:33:57,379 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:33:57,381 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:33:57,381 - INFO - 10. Check Setup Status
2025-06-21 23:33:57,382 - INFO - 11. Cleanup and Start New
2025-06-21 23:33:57,383 - INFO - 12. Exit
2025-06-21 23:35:03,475 - INFO - --- Exiting Security Manager ---
2025-06-21 23:50:22,959 - INFO - --- Security Manager Started ---
2025-06-21 23:50:22,985 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:50:22,986 - INFO - 1. Encrypt Project
2025-06-21 23:50:22,986 - INFO - 2. Decrypt Project
2025-06-21 23:50:22,987 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:50:22,988 - INFO - 4. Create Project Backup
2025-06-21 23:50:22,988 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:50:22,989 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:50:22,990 - INFO - 7. Alternative Protection Methods
2025-06-21 23:50:22,990 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:50:22,991 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:50:22,991 - INFO - 10. Check Setup Status
2025-06-21 23:50:22,991 - INFO - 11. Cleanup and Start New
2025-06-21 23:50:22,992 - INFO - 12. Exit
2025-06-21 23:50:25,400 - INFO - User selected option: '6'
2025-06-21 23:50:25,400 - INFO - Initiating obfuscation process.
2025-06-21 23:50:25,401 - INFO - 
--- Starting Obfuscation with PyArmor ---
2025-06-21 23:50:25,401 - INFO - Obfuscating project root: C:\Users\<USER>\linkedin
2025-06-21 23:50:25,402 - INFO - Output will be in: C:\Users\<USER>\linkedin\dist
2025-06-21 23:50:25,403 - INFO - Main script to obfuscate: C:\Users\<USER>\linkedin\run_job_search.py
2025-06-21 23:50:25,403 - WARNING - Note: PyArmor trial version has a 32KB limit per code object.
2025-06-21 23:50:25,403 - WARNING - Large files may fail to obfuscate. Consider splitting large files or purchasing PyArmor.
2025-06-21 23:50:25,404 - INFO - Attempting obfuscation with exclusions to avoid trial limitations...
2025-06-21 23:50:26,152 - ERROR - Obfuscation failed: Command '['pyarmor-7', 'obfuscate', '--recursive', '--exclude', 'Linkedin', '--exclude', '*.pyc', '--exclude', '__pycache__', '--output', 'C:\\Users\\<USER>\\linkedin\\dist', 'C:\\Users\\<USER>\\linkedin\\run_job_search.py']' returned non-zero exit status 1.
2025-06-21 23:50:26,153 - ERROR - 
--- TRIAL VERSION LIMITATION DETECTED ---
2025-06-21 23:50:26,153 - ERROR - The trial version of PyArmor cannot obfuscate files larger than 32KB.
2025-06-21 23:50:26,154 - INFO - 
Alternative solutions:
2025-06-21 23:50:26,154 - INFO - 1. Purchase PyArmor full version to remove size limitations
2025-06-21 23:50:26,155 - INFO - 2. Split large Python files into smaller modules
2025-06-21 23:50:26,155 - INFO - 3. Use alternative obfuscation tools
2025-06-21 23:50:26,156 - INFO - 4. Continue without obfuscation (encryption still provides protection)
2025-06-21 23:50:30,357 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:50:30,358 - INFO - 1. Encrypt Project
2025-06-21 23:50:30,358 - INFO - 2. Decrypt Project
2025-06-21 23:50:30,359 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:50:30,360 - INFO - 4. Create Project Backup
2025-06-21 23:50:30,360 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:50:30,361 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:50:30,361 - INFO - 7. Alternative Protection Methods
2025-06-21 23:50:30,361 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:50:30,362 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:50:30,362 - INFO - 10. Check Setup Status
2025-06-21 23:50:30,363 - INFO - 11. Cleanup and Start New
2025-06-21 23:50:30,363 - INFO - 12. Exit
2025-06-21 23:50:32,977 - INFO - User selected option: '7'
2025-06-21 23:50:32,978 - INFO - Opening alternative protection methods.
2025-06-21 23:50:32,978 - INFO - 
--- Alternative Code Protection Methods ---
2025-06-21 23:50:32,978 - INFO - 1. Split Large Files (PyArmor Compatible)
2025-06-21 23:50:32,979 - INFO - 2. Python Minifier + Obfuscation
2025-06-21 23:50:32,979 - INFO - 3. Nuitka Compilation (Recommended)
2025-06-21 23:50:32,979 - INFO - 4. PyInstaller + UPX Packing
2025-06-21 23:50:32,980 - INFO - 5. Custom Source Encryption
2025-06-21 23:50:32,980 - INFO - 6. Cython Compilation
2025-06-21 23:50:32,981 - INFO - 7. View Detailed Guide
2025-06-21 23:50:32,981 - INFO - 8. Back to Main Menu
2025-06-21 23:50:34,885 - INFO - User selected alternative protection: '2'
2025-06-21 23:50:34,885 - INFO - 
--- Python Minifier Protection ---
2025-06-21 23:50:35,015 - INFO - Found 13 Python files to minify
2025-06-21 23:50:35,687 - INFO - Minified: linkedin_easy_apply.py
2025-06-21 23:50:35,696 - INFO - Minified: meta-llama_8B.py
2025-06-21 23:50:35,709 - INFO - Minified: run_easy_apply_batch.py
2025-06-21 23:50:35,798 - INFO - Minified: run_job_search.py
2025-06-21 23:50:35,831 - INFO - Minified: test_all_job_search_functions.py
2025-06-21 23:50:35,917 - INFO - Minified: dist\pytransform\__init__.py
2025-06-21 23:50:36,598 - INFO - Minified: Linkedin\linkedin_easy_apply.py
2025-06-21 23:50:36,606 - INFO - Minified: Linkedin\meta-llama_8B.py
2025-06-21 23:50:36,618 - INFO - Minified: Linkedin\run_easy_apply_batch.py
2025-06-21 23:50:36,706 - INFO - Minified: Linkedin\run_job_search.py
2025-06-21 23:50:36,737 - INFO - Minified: Linkedin\test_all_job_search_functions.py
2025-06-21 23:50:36,774 - INFO - Minified: Linkedin\test_results\linkedin_scraper_test.py
2025-06-21 23:50:36,815 - INFO - Minified: test_results\linkedin_scraper_test.py
2025-06-21 23:50:36,816 - INFO - Minification complete. Output in: C:\Users\<USER>\linkedin\minified
2025-06-21 23:50:36,816 - INFO - Files are now minified and partially obfuscated.
2025-06-21 23:50:36,817 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:50:36,818 - INFO - 1. Encrypt Project
2025-06-21 23:50:36,819 - INFO - 2. Decrypt Project
2025-06-21 23:50:36,820 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:50:36,821 - INFO - 4. Create Project Backup
2025-06-21 23:50:36,821 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:50:36,822 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:50:36,823 - INFO - 7. Alternative Protection Methods
2025-06-21 23:50:36,824 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:50:36,824 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:50:36,825 - INFO - 10. Check Setup Status
2025-06-21 23:50:36,825 - INFO - 11. Cleanup and Start New
2025-06-21 23:50:36,826 - INFO - 12. Exit
2025-06-21 23:51:06,238 - INFO - User selected option: 'clear'
2025-06-21 23:51:06,239 - WARNING - Invalid choice: 'clear'. Please enter a number between 1 and 12.
2025-06-21 23:51:06,239 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-21 23:51:06,240 - INFO - 1. Encrypt Project
2025-06-21 23:51:06,241 - INFO - 2. Decrypt Project
2025-06-21 23:51:06,241 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-21 23:51:06,241 - INFO - 4. Create Project Backup
2025-06-21 23:51:06,242 - INFO - 5. Manage Backups (Prune Old)
2025-06-21 23:51:06,243 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-21 23:51:06,243 - INFO - 7. Alternative Protection Methods
2025-06-21 23:51:06,244 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-21 23:51:06,244 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-21 23:51:06,245 - INFO - 10. Check Setup Status
2025-06-21 23:51:06,246 - INFO - 11. Cleanup and Start New
2025-06-21 23:51:06,246 - INFO - 12. Exit
2025-06-21 23:51:09,456 - INFO - User selected option: '12'
2025-06-21 23:51:09,456 - INFO - --- Exiting Security Manager ---
2025-06-22 00:07:29,320 - INFO - --- Security Manager Started ---
2025-06-22 00:07:29,345 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 00:07:29,346 - INFO - 1. Encrypt Project
2025-06-22 00:07:29,347 - INFO - 2. Decrypt Project
2025-06-22 00:07:29,348 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 00:07:29,350 - INFO - 4. Create Project Backup
2025-06-22 00:07:29,350 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 00:07:29,352 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 00:07:29,353 - INFO - 7. Alternative Protection Methods
2025-06-22 00:07:29,354 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 00:07:29,355 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 00:07:29,356 - INFO - 10. Check Setup Status
2025-06-22 00:07:29,357 - INFO - 11. Cleanup and Start New
2025-06-22 00:07:29,358 - INFO - 12. Exit
2025-06-22 00:07:34,558 - INFO - User selected option: '12'
2025-06-22 00:07:34,559 - INFO - --- Exiting Security Manager ---
2025-06-22 07:53:26,634 - INFO - --- Security Manager Started ---
2025-06-22 07:53:26,673 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:53:26,675 - INFO - 1. Encrypt Project
2025-06-22 07:53:26,675 - INFO - 2. Decrypt Project
2025-06-22 07:53:26,677 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:53:26,677 - INFO - 4. Create Project Backup
2025-06-22 07:53:26,678 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:53:26,679 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:53:26,680 - INFO - 7. Alternative Protection Methods
2025-06-22 07:53:26,682 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:53:26,683 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:53:26,684 - INFO - 10. Check Setup Status
2025-06-22 07:53:26,685 - INFO - 11. Cleanup and Start New
2025-06-22 07:53:26,685 - INFO - 12. Exit
2025-06-22 07:53:36,649 - INFO - User selected option: '10'
2025-06-22 07:53:36,650 - INFO - 
--- Checking Security Toolkit Setup Status ---
2025-06-22 07:53:36,651 - INFO - [FOUND] Salt File: C:\Users\<USER>\linkedin\security\salt.bin
2025-06-22 07:53:36,652 - INFO - [FOUND] Private Key: C:\Users\<USER>\linkedin\security\private_key.pem
2025-06-22 07:53:36,654 - INFO - [FOUND] Public Key: C:\Users\<USER>\linkedin\security\public_key.pem
2025-06-22 07:53:36,655 - INFO - 
--- Status Check Complete ---
2025-06-22 07:53:36,656 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:53:36,658 - INFO - 1. Encrypt Project
2025-06-22 07:53:36,658 - INFO - 2. Decrypt Project
2025-06-22 07:53:36,660 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:53:36,661 - INFO - 4. Create Project Backup
2025-06-22 07:53:36,662 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:53:36,663 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:53:36,666 - INFO - 7. Alternative Protection Methods
2025-06-22 07:53:36,667 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:53:36,669 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:53:36,671 - INFO - 10. Check Setup Status
2025-06-22 07:53:36,673 - INFO - 11. Cleanup and Start New
2025-06-22 07:53:36,674 - INFO - 12. Exit
2025-06-22 07:54:05,801 - INFO - --- Exiting Security Manager ---
2025-06-22 07:54:16,854 - INFO - --- Security Manager Started ---
2025-06-22 07:54:16,856 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:54:16,857 - INFO - 1. Encrypt Project
2025-06-22 07:54:16,857 - INFO - 2. Decrypt Project
2025-06-22 07:54:16,859 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:54:16,859 - INFO - 4. Create Project Backup
2025-06-22 07:54:16,860 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:54:16,861 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:54:16,862 - INFO - 7. Alternative Protection Methods
2025-06-22 07:54:16,863 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:54:16,864 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:54:16,865 - INFO - 10. Check Setup Status
2025-06-22 07:54:16,865 - INFO - 11. Cleanup and Start New
2025-06-22 07:54:16,866 - INFO - 12. Exit
2025-06-22 07:54:28,411 - INFO - --- Exiting Security Manager ---
2025-06-22 07:54:30,340 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:54:30,361 - INFO - [RECIPIENT] Copied: manifest.sha256
2025-06-22 07:54:30,363 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:54:30,363 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:54:30,365 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:54:30,366 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:54:30,369 - INFO - --- Security Manager Started ---
2025-06-22 07:54:30,371 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:54:30,372 - INFO - 1. Encrypt Project
2025-06-22 07:54:30,372 - INFO - 2. Decrypt Project
2025-06-22 07:54:30,375 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:54:30,376 - INFO - 4. Create Project Backup
2025-06-22 07:54:30,378 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:54:30,380 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:54:30,381 - INFO - 7. Alternative Protection Methods
2025-06-22 07:54:30,382 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:54:30,384 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:54:30,384 - INFO - 10. Check Setup Status
2025-06-22 07:54:30,386 - INFO - 11. Cleanup and Start New
2025-06-22 07:54:30,386 - INFO - 12. Exit
2025-06-22 07:54:48,017 - INFO - User selected option: '10'
2025-06-22 07:54:48,018 - INFO - 
--- Checking Security Toolkit Setup Status ---
2025-06-22 07:54:48,020 - INFO - [FOUND] Salt File: C:\Users\<USER>\linkedin\security\salt.bin
2025-06-22 07:54:48,020 - INFO - [FOUND] Private Key: C:\Users\<USER>\linkedin\security\private_key.pem
2025-06-22 07:54:48,021 - INFO - [FOUND] Public Key: C:\Users\<USER>\linkedin\security\public_key.pem
2025-06-22 07:54:48,022 - INFO - 
--- Status Check Complete ---
2025-06-22 07:54:48,024 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:54:48,025 - INFO - 1. Encrypt Project
2025-06-22 07:54:48,027 - INFO - 2. Decrypt Project
2025-06-22 07:54:48,028 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:54:48,029 - INFO - 4. Create Project Backup
2025-06-22 07:54:48,030 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:54:48,031 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:54:48,032 - INFO - 7. Alternative Protection Methods
2025-06-22 07:54:48,033 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:54:48,036 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:54:48,037 - INFO - 10. Check Setup Status
2025-06-22 07:54:48,039 - INFO - 11. Cleanup and Start New
2025-06-22 07:54:48,041 - INFO - 12. Exit
2025-06-22 07:54:52,878 - INFO - --- Exiting Security Manager ---
2025-06-22 07:55:01,697 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:55:01,701 - INFO - [RECIPIENT] Copied: manifest.sha256
2025-06-22 07:55:01,701 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:55:01,702 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:55:01,703 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:55:01,704 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:55:01,706 - INFO - --- Security Manager Started ---
2025-06-22 07:55:01,707 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:55:01,709 - INFO - 1. Encrypt Project
2025-06-22 07:55:01,709 - INFO - 2. Decrypt Project
2025-06-22 07:55:01,710 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:55:01,711 - INFO - 4. Create Project Backup
2025-06-22 07:55:01,711 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:55:01,712 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:55:01,714 - INFO - 7. Alternative Protection Methods
2025-06-22 07:55:01,714 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:55:01,715 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:55:01,716 - INFO - 10. Check Setup Status
2025-06-22 07:55:01,718 - INFO - 11. Cleanup and Start New
2025-06-22 07:55:01,719 - INFO - 12. Exit
2025-06-22 07:55:32,188 - INFO - User selected option: '11'
2025-06-22 07:55:35,530 - INFO - 
--- CLEANUP AND START NEW ---
2025-06-22 07:55:35,532 - INFO - [DELETED] salt.bin
2025-06-22 07:55:35,533 - INFO - [DELETED] private_key.pem
2025-06-22 07:55:35,535 - INFO - [DELETED] public_key.pem
2025-06-22 07:55:35,535 - INFO - [SKIPPED] public_key.pem.enc not found.
2025-06-22 07:55:35,537 - INFO - [DELETED] manifest.sha256
2025-06-22 07:55:35,538 - INFO - [DELETED] manifest.sha256.sig
2025-06-22 07:55:35,539 - INFO - [SKIPPED] manifest.sha256.sig.enc not found.
2025-06-22 07:55:35,543 - ERROR - [FAILED] Could not delete directory C:\Users\<USER>\linkedin\security\logs: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\linkedin\\security\\logs\\security.log'
2025-06-22 07:55:35,544 - INFO - [SKIPPED] Directory temp_processing not found.
2025-06-22 07:55:35,549 - INFO - [DELETED] Directory __pycache__
2025-06-22 07:55:35,555 - INFO - [DELETED] Directory src\__pycache__
2025-06-22 07:55:35,556 - INFO - 
--- Cleanup Complete ---
2025-06-22 07:55:35,557 - INFO - Project has been reset. To secure it again, please run:
2025-06-22 07:55:35,558 - INFO - 1. python run.py generate-salt
2025-06-22 07:55:35,559 - INFO - 2. python run.py generate-keys
2025-06-22 07:55:35,560 - INFO - 3. python run.py (and select 'Encrypt Project')
2025-06-22 07:55:35,562 - INFO - Please restart the manager to continue.
2025-06-22 07:55:35,565 - INFO - --- Exiting Security Manager ---
2025-06-22 07:56:01,822 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:56:01,822 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 07:56:01,824 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:56:01,825 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:56:01,825 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:56:01,826 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:56:01,827 - INFO - --- Security Manager Started ---
2025-06-22 07:56:01,828 - ERROR - Error: 'salt.bin' not found in the 'security' directory.
2025-06-22 07:56:01,829 - ERROR - Please run 'python run.py generate-salt' first to create it.
2025-06-22 07:56:01,830 - INFO - --- Exiting Security Manager ---
2025-06-22 07:56:05,097 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:56:05,099 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 07:56:05,100 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:56:05,101 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:56:05,102 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:56:05,110 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:56:21,294 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:56:21,296 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 07:56:21,298 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:56:21,298 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:56:21,300 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:56:21,300 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:56:21,303 - INFO - 
--- Generating New Digital Signature Keys ---
2025-06-22 07:56:29,356 - INFO - Successfully generated and saved keys:
2025-06-22 07:56:29,358 - INFO -   - Encrypted Private Key: C:\Users\<USER>\linkedin\security\private_key.pem
2025-06-22 07:56:29,359 - INFO -   - Public Key: C:\Users\<USER>\linkedin\security\public_key.pem
2025-06-22 07:56:29,360 - WARNING - IMPORTANT: You must now re-encrypt your project to use these new keys.
2025-06-22 07:56:35,159 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:56:35,160 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 07:56:35,162 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:56:35,164 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:56:35,164 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:56:35,166 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:56:35,169 - INFO - --- Security Manager Started ---
2025-06-22 07:56:35,184 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:56:35,185 - INFO - 1. Encrypt Project
2025-06-22 07:56:35,187 - INFO - 2. Decrypt Project
2025-06-22 07:56:35,189 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:56:35,189 - INFO - 4. Create Project Backup
2025-06-22 07:56:35,192 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:56:35,193 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:56:35,193 - INFO - 7. Alternative Protection Methods
2025-06-22 07:56:35,195 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:56:35,197 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:56:35,198 - INFO - 10. Check Setup Status
2025-06-22 07:56:35,199 - INFO - 11. Cleanup and Start New
2025-06-22 07:56:35,201 - INFO - 12. Exit
2025-06-22 07:57:05,350 - INFO - --- Exiting Security Manager ---
2025-06-22 07:58:08,311 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 07:58:08,313 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 07:58:08,314 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 07:58:08,315 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 07:58:08,316 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 07:58:08,317 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 07:58:08,318 - INFO - --- Security Manager Started ---
2025-06-22 07:58:08,320 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:58:08,321 - INFO - 1. Encrypt Project
2025-06-22 07:58:08,321 - INFO - 2. Decrypt Project
2025-06-22 07:58:08,322 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:58:08,322 - INFO - 4. Create Project Backup
2025-06-22 07:58:08,325 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:58:08,326 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:58:08,327 - INFO - 7. Alternative Protection Methods
2025-06-22 07:58:08,327 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:58:08,329 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:58:08,329 - INFO - 10. Check Setup Status
2025-06-22 07:58:08,330 - INFO - 11. Cleanup and Start New
2025-06-22 07:58:08,331 - INFO - 12. Exit
2025-06-22 07:59:30,094 - INFO - User selected option: '10'
2025-06-22 07:59:30,095 - INFO - 
--- Checking Security Toolkit Setup Status ---
2025-06-22 07:59:30,097 - INFO - [FOUND] Salt File: C:\Users\<USER>\linkedin\security\salt.bin
2025-06-22 07:59:30,098 - INFO - [FOUND] Private Key: C:\Users\<USER>\linkedin\security\private_key.pem
2025-06-22 07:59:30,099 - INFO - [FOUND] Public Key: C:\Users\<USER>\linkedin\security\public_key.pem
2025-06-22 07:59:30,100 - INFO - 
--- Status Check Complete ---
2025-06-22 07:59:30,101 - INFO - 
--- Project Security & Backup Manager (v3.1 - Enhanced Protection) ---
2025-06-22 07:59:30,102 - INFO - 1. Encrypt Project
2025-06-22 07:59:30,103 - INFO - 2. Decrypt Project
2025-06-22 07:59:30,104 - INFO - 3. Verify Project Integrity (Standalone)
2025-06-22 07:59:30,105 - INFO - 4. Create Project Backup
2025-06-22 07:59:30,106 - INFO - 5. Manage Backups (Prune Old)
2025-06-22 07:59:30,107 - INFO - 6. Obfuscate Project (PyArmor)
2025-06-22 07:59:30,108 - INFO - 7. Alternative Protection Methods
2025-06-22 07:59:30,108 - INFO - 8. Self-Destruct Project (DANGEROUS)
2025-06-22 07:59:30,110 - INFO - 9. Generate New Signature Keys (DANGEROUS)
2025-06-22 07:59:30,112 - INFO - 10. Check Setup Status
2025-06-22 07:59:30,113 - INFO - 11. Cleanup and Start New
2025-06-22 07:59:30,114 - INFO - 12. Exit
2025-06-22 07:59:52,732 - INFO - User selected option: '12'
2025-06-22 07:59:52,734 - INFO - --- Exiting Security Manager ---
2025-06-22 08:00:36,695 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 08:00:36,696 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 08:00:36,699 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 08:00:36,699 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 08:00:36,700 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 08:00:36,701 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 08:03:21,131 - INFO - User selected option: '12'
2025-06-22 08:03:21,132 - INFO - --- Exiting Security Manager ---
2025-06-22 08:03:24,826 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 08:03:24,829 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 08:03:24,830 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 08:03:24,834 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 08:03:24,837 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 08:03:24,839 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 08:05:04,586 - INFO - User selected option: '12'
2025-06-22 08:05:04,586 - INFO - --- Exiting Security Manager ---
2025-06-22 08:05:06,392 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 08:05:06,393 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 08:05:06,395 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 08:05:06,396 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 08:05:06,396 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 08:05:06,398 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 08:12:23,415 - INFO - User selected option: '12'
2025-06-22 08:12:23,417 - INFO - --- Exiting Security Manager ---
2025-06-22 08:12:25,472 - WARNING - [RECIPIENT] Not found: backups
2025-06-22 08:12:25,472 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-22 08:12:25,473 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-22 08:12:25,474 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-22 08:12:25,474 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-22 08:12:25,475 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-22 08:12:27,125 - INFO - User selected option: '12'
2025-06-22 08:12:27,125 - INFO - --- Exiting Security Manager ---
2025-06-23 10:06:10,868 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:06:10,869 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:06:10,870 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:06:10,870 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:06:10,871 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:06:10,872 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:07:45,177 - INFO - User selected option: '7'
2025-06-23 10:07:45,178 - INFO - Opening alternative protection methods.
2025-06-23 10:07:45,178 - INFO - 
--- Alternative Code Protection Methods ---
2025-06-23 10:07:45,180 - INFO - 1. Split Large Files (PyArmor Compatible)
2025-06-23 10:07:45,180 - INFO - 2. Python Minifier + Obfuscation
2025-06-23 10:07:45,181 - INFO - 3. Nuitka Compilation (Recommended)
2025-06-23 10:07:45,181 - INFO - 4. PyInstaller + UPX Packing
2025-06-23 10:07:45,181 - INFO - 5. Custom Source Encryption
2025-06-23 10:07:45,184 - INFO - 6. Cython Compilation
2025-06-23 10:07:45,185 - INFO - 7. View Detailed Guide
2025-06-23 10:07:45,186 - INFO - 8. Back to Main Menu
2025-06-23 10:08:06,137 - INFO - User selected alternative protection: '7'
2025-06-23 10:08:06,139 - INFO - 
Detailed guide available at: C:\Users\<USER>\linkedin\security\code_protection_alternatives.md
2025-06-23 10:08:35,641 - INFO - User selected option: '12'
2025-06-23 10:08:35,643 - INFO - --- Exiting Security Manager ---
2025-06-23 10:08:37,567 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:08:37,568 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:08:37,569 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:08:37,570 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:08:37,571 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:08:37,571 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:08:45,179 - INFO - --- Exiting Security Manager ---
2025-06-23 10:22:24,750 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:22:24,754 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:22:24,757 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:22:24,759 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:22:24,760 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:22:24,761 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:22:44,551 - INFO - User selected option: '11'
2025-06-23 10:22:46,277 - INFO - 
--- CLEANUP AND START NEW ---
2025-06-23 10:22:46,278 - INFO - [DELETED] salt.bin
2025-06-23 10:22:46,279 - INFO - [DELETED] private_key.pem
2025-06-23 10:22:46,281 - INFO - [DELETED] public_key.pem
2025-06-23 10:22:46,282 - INFO - [SKIPPED] public_key.pem.enc not found.
2025-06-23 10:22:46,282 - INFO - [SKIPPED] manifest.sha256 not found.
2025-06-23 10:22:46,283 - INFO - [SKIPPED] manifest.sha256.sig not found.
2025-06-23 10:22:46,283 - INFO - [SKIPPED] manifest.sha256.sig.enc not found.
2025-06-23 10:22:46,285 - ERROR - [FAILED] Could not delete directory C:\Users\<USER>\linkedin\security\logs: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\linkedin\\security\\logs\\security.log'
2025-06-23 10:22:46,286 - INFO - [SKIPPED] Directory temp_processing not found.
2025-06-23 10:22:46,286 - INFO - [SKIPPED] Directory __pycache__ not found.
2025-06-23 10:22:46,290 - INFO - [DELETED] Directory src\__pycache__
2025-06-23 10:22:46,292 - INFO - 
--- Cleanup Complete ---
2025-06-23 10:22:46,292 - INFO - Project has been reset. To secure it again, please run:
2025-06-23 10:22:46,293 - INFO - 1. python run.py generate-salt
2025-06-23 10:22:46,294 - INFO - 2. python run.py generate-keys
2025-06-23 10:22:46,296 - INFO - 3. python run.py (and select 'Encrypt Project')
2025-06-23 10:22:46,297 - INFO - Please restart the manager to continue.
2025-06-23 10:22:46,298 - INFO - --- Exiting Security Manager ---
2025-06-23 10:22:51,388 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:22:51,390 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:22:51,392 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:22:51,392 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:22:51,393 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:22:51,394 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:22:58,377 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:22:58,378 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:22:58,379 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:22:58,380 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:22:58,380 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:22:58,381 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:22:58,383 - INFO - 
--- Generating New Digital Signature Keys ---
2025-06-23 10:23:06,276 - INFO - Successfully generated and saved keys:
2025-06-23 10:23:06,277 - INFO -   - Encrypted Private Key: C:\Users\<USER>\linkedin\security\private_key.pem
2025-06-23 10:23:06,277 - INFO -   - Public Key: C:\Users\<USER>\linkedin\security\public_key.pem
2025-06-23 10:23:06,278 - WARNING - IMPORTANT: You must now re-encrypt your project to use these new keys.
2025-06-23 10:23:13,952 - WARNING - [RECIPIENT] Not found: backups
2025-06-23 10:23:13,953 - WARNING - [RECIPIENT] Not found: manifest.sha256
2025-06-23 10:23:13,954 - WARNING - [RECIPIENT] Not found: manifest.sha256.sig.enc
2025-06-23 10:23:13,954 - WARNING - [RECIPIENT] Not found: public_key.pem.enc
2025-06-23 10:23:13,955 - WARNING - [RECIPIENT] Not found: decryptor.exe
2025-06-23 10:23:13,955 - INFO - Recipient package prepared at: C:\Users\<USER>\linkedin\security\recipient
2025-06-23 10:23:29,922 - INFO - User selected option: '1'
2025-06-23 10:23:34,743 - INFO - User selected security level: '1'
2025-06-23 10:23:37,157 - INFO - Initiating encrypt process.
2025-06-23 10:23:37,158 - INFO - --- Starting Encrypting (Level 1) ---
2025-06-23 10:23:37,297 - INFO - Found 19 files to encrypt.
2025-06-23 10:23:37,307 - INFO - Processed: application_history.json
2025-06-23 10:23:37,312 - INFO - Processed: credentials.json
2025-06-23 10:23:37,318 - INFO - Processed: linkedin_easy_apply.py
2025-06-23 10:23:37,321 - INFO - Processed: meta-llama_8B.py
2025-06-23 10:23:37,353 - INFO - Processed: mydata.txt
2025-06-23 10:23:37,359 - INFO - Processed: run_easy_apply_batch.py
2025-06-23 10:23:37,404 - INFO - Processed: run_job_search.py
2025-06-23 10:23:37,410 - INFO - Processed: test_all_job_search_functions.py
2025-06-23 10:23:37,414 - INFO - Processed: Linkedin\application_history.json
2025-06-23 10:23:37,418 - INFO - Processed: Linkedin\credentials.json
2025-06-23 10:23:37,454 - INFO - Processed: Linkedin\linkedin_easy_apply.py
2025-06-23 10:23:37,462 - INFO - Processed: Linkedin\meta-llama_8B.py
2025-06-23 10:23:37,466 - INFO - Processed: Linkedin\mydata.txt
2025-06-23 10:23:37,469 - INFO - Processed: Linkedin\run_easy_apply_batch.py
2025-06-23 10:23:37,488 - INFO - Processed: Linkedin\run_job_search.py
2025-06-23 10:23:37,495 - INFO - Processed: Linkedin\test_all_job_search_functions.py
2025-06-23 10:23:37,515 - INFO - Processed: Linkedin\.vscode\settings.json
2025-06-23 10:23:37,534 - INFO - Processed: Linkedin\test_results\linkedin_scraper_test.py
2025-06-23 10:23:37,550 - INFO - Processed: test_results\linkedin_scraper_test.py
2025-06-23 10:23:37,551 - INFO - Finalizing encryption: creating and signing manifest...
2025-06-23 10:23:37,910 - INFO - Commiting changes...
2025-06-23 10:23:38,286 - INFO - --- Encrypting Complete ---
2025-06-23 10:24:05,648 - INFO - User selected option: '2'
2025-06-23 10:24:07,089 - INFO - User selected security level: '1'
2025-06-23 10:24:09,266 - INFO - Initiating decrypt process.
2025-06-23 10:24:09,267 - INFO - --- Starting Decrypting (Level 1) ---
2025-06-23 10:24:09,428 - INFO - --- Running Pre-Decryption Integrity Check ---
2025-06-23 10:24:09,499 - INFO - Manifest signature verified.
2025-06-23 10:24:09,668 - INFO - --- Pre-Decryption Integrity Check Passed ---
2025-06-23 10:24:09,671 - INFO - Found 19 files to decrypt.
2025-06-23 10:24:09,673 - INFO - Processed: application_history.json
2025-06-23 10:24:09,676 - INFO - Processed: credentials.json
2025-06-23 10:24:09,686 - INFO - Processed: linkedin_easy_apply.py
2025-06-23 10:24:09,689 - INFO - Processed: meta-llama_8B.py
2025-06-23 10:24:09,692 - INFO - Processed: mydata.txt
2025-06-23 10:24:09,695 - INFO - Processed: run_easy_apply_batch.py
2025-06-23 10:24:09,706 - INFO - Processed: run_job_search.py
2025-06-23 10:24:09,711 - INFO - Processed: test_all_job_search_functions.py
2025-06-23 10:24:09,716 - INFO - Processed: Linkedin\application_history.json
2025-06-23 10:24:09,720 - INFO - Processed: Linkedin\credentials.json
2025-06-23 10:24:09,727 - INFO - Processed: Linkedin\linkedin_easy_apply.py
2025-06-23 10:24:09,732 - INFO - Processed: Linkedin\meta-llama_8B.py
2025-06-23 10:24:09,736 - INFO - Processed: Linkedin\mydata.txt
2025-06-23 10:24:09,738 - INFO - Processed: Linkedin\run_easy_apply_batch.py
2025-06-23 10:24:09,741 - INFO - Processed: Linkedin\run_job_search.py
2025-06-23 10:24:09,744 - INFO - Processed: Linkedin\test_all_job_search_functions.py
2025-06-23 10:24:09,750 - INFO - Processed: Linkedin\.vscode\settings.json
2025-06-23 10:24:09,755 - INFO - Processed: Linkedin\test_results\linkedin_scraper_test.py
2025-06-23 10:24:09,758 - INFO - Processed: test_results\linkedin_scraper_test.py
2025-06-23 10:24:09,758 - INFO - Commiting changes...
2025-06-23 10:24:10,116 - INFO - --- Decrypting Complete ---
2025-06-23 11:09:51,521 - INFO - --- Exiting Security Manager ---
