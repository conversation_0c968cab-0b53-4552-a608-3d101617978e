# LinkedIn Easy Apply Batch Automation

This project automates the LinkedIn "Easy Apply" process for multiple job searches in parallel using Python and Selenium.

## Features
- Automates the LinkedIn Easy Apply process for up to 6 different job searches.
- Prompts for all job search terms and locations at the start.
- Runs all job searches in parallel (each in its own browser window).
- Credentials are securely loaded from a `credentials.json` file (not hardcoded).

## Requirements
- Python 3.7+
- Google Chrome browser
- ChromeDriver (compatible with your Chrome version)
- Selenium

## Setup

1. **Clone or download this repository.**

2. **Install dependencies:**
   ```bash
   pip install selenium
   ```

3. **Download ChromeDriver:**
   - Download the version matching your Chrome browser from: https://chromedriver.chromium.org/downloads
   - Place the `chromedriver.exe` in a directory in your PATH or in the project root.

4. **Create a `credentials.json` file in the project root:**
   ```json
   {
     "email": "<EMAIL>",
     "password": "your_linkedIn_password"
   }
   ```
   **Keep this file secure and do not share it.**

5. **Run the batch script:**
   ```bash
   python run_easy_apply_batch.py
   ```

## How It Works
- The script will prompt you for 6 job search terms and 6 locations (one for each run).
- After collecting all inputs, it will launch 6 browser windows in parallel, each running the Easy Apply process for the specified search term and location.
- Each process logs in using the credentials from `credentials.json`.
- Progress and errors for each run are printed in the terminal.

## How to Run a Single Job Search (One at a Time)
If you want to run the LinkedIn Easy Apply automation for a single job search (not in parallel), you can use the `linkedin_easy_apply.py` script directly. Make sure it uses credentials from `credentials.json` (see below for update instructions).

**To run a single job search:**
1. Open a terminal in your project directory.
2. Run:
   ```bash
   python linkedin_easy_apply.py
   ```
3. The script will prompt you for the job search term and location, and use your credentials from `credentials.json`.

---

## Credentials Management
Both the batch and single-run scripts should load credentials from `credentials.json` for security. Do not hardcode credentials in any script.

## Using the Question Answering API (meta-llama)

This automation uses an API endpoint for answering additional LinkedIn application questions:

```
API_URL = "http://*************:10000/generate"
```

- By default, this API is **turned off** and will not respond unless the meta-llama model is running on a GPU server.
- If you have a GPU, you can load and run the meta-llama model yourself. See the `meta-llama_8B.py` script for details on how to start the API server locally.
- The script `meta-llama_8B.py` is provided for running the meta-llama 8B model as a local API. You will also need the model weights and dependencies.
- The file `my_data.txt` is included for your own data or prompt customization.
- If you want to use your own local API, update the `API_URL` in your scripts to point to your local server (e.g., `http://localhost:10000/generate`).

**Note:**
- If the API is not running, the automation will fall back to default answers for application questions.
- Running large language models like meta-llama 8B requires a powerful GPU and significant memory.

## Notes & Warnings
- **Parallel runs:** All 6 browser windows will open at once. This is resource-intensive and may trigger LinkedIn's anti-bot measures. Use responsibly.
- **Account safety:** Too many parallel logins or rapid applications may result in LinkedIn account restrictions or bans. Use at your own risk.
- **Headless mode:** You can enable headless mode by changing `headless=False` to `headless=True` in the script, but this may reduce reliability for some LinkedIn forms.
- **Do not share your credentials.json file.**

## Customization
- To change the number of parallel runs, modify `num_runs` in `run_easy_apply_batch.py`.
- To add more advanced job filtering or answer customization, edit the `LinkedInEasyApply` class logic.

## Troubleshooting
- If ChromeDriver is not found, ensure it is in your PATH or the project root.
- If you see errors about login or navigation, check your credentials and network connection.
- For Selenium or browser errors, ensure your Chrome and ChromeDriver versions match.

## License
This project is for educational and personal automation use only. Use responsibly and respect LinkedIn's terms of service.
