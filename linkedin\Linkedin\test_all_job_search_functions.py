#!/usr/bin/env python
"""
LinkedIn JobSearch Functions Demo
This script demonstrates all the functions available in the JobSearch class from the LinkedIn Scraper.
"""

import os
import time
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from linkedin_scraper import actions
from linkedin_scraper.job_search import JobSearch

def setup_driver():
    """Set up the Chrome driver"""
    print("\n=== Setting up Chrome driver ===")
    try:
        chrome_options = Options()
        # Uncomment the line below if you want to run in headless mode
        # chrome_options.add_argument("--headless")
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)  # Increase timeout to 30 seconds
        print("✅ Successfully created Chrome driver")
        return driver
    except Exception as e:
        print(f"❌ Failed to create Chrome driver: {str(e)}")
        sys.exit(1)

def login_to_linkedin(driver):
    """Login to LinkedIn with user credentials"""
    print("\n=== Logging in to LinkedIn ===")
    try:
        # Get credentials from user
        email = "<EMAIL>" 
        password = "badshaah"
        # Login
        actions.login(driver, email, password)
        print("✅ Successfully logged in to LinkedIn")
        # Give some time for login to complete
        time.sleep(5)
    except Exception as e:
        print(f"❌ Failed to log in: {str(e)}")
        driver.quit()
        sys.exit(1)

def test_init_function(driver):
    """Test the __init__ function of JobSearch class"""
    print("\n=== Testing JobSearch.__init__ function ===")
    try:
        # Initialize JobSearch with default parameters but don't scrape yet
        job_search = JobSearch(driver=driver, scrape=False)
        print(f"✅ Successfully initialized JobSearch object with default parameters:")
        print(f"   Base URL: {job_search.base_url}")
        print(f"   Recommended jobs count: {len(job_search.recommended_jobs)}")
        print(f"   Still hiring count: {len(job_search.still_hiring)}")
        print(f"   More jobs count: {len(job_search.more_jobs)}")
        return job_search
    except Exception as e:
        print(f"❌ Failed to initialize JobSearch: {str(e)}")
        return None

def test_scrape_function(job_search):
    """Test the scrape function of JobSearch class"""
    print("\n=== Testing JobSearch.scrape function ===")
    try:
        # Call the scrape method which will internally call scrape_logged_in
        job_search.scrape(close_on_complete=False, scrape_recommended_jobs=True)
        print(f"✅ Successfully scraped LinkedIn Jobs homepage")
        print(f"   Recommended jobs found: {len(job_search.recommended_jobs)}")
        print(f"   Still hiring jobs found: {len(job_search.still_hiring)}")
        print(f"   More jobs found: {len(job_search.more_jobs)}")
        return True
    except Exception as e:
        print(f"❌ Failed to scrape LinkedIn Jobs homepage: {str(e)}")
        return False

def test_scrape_job_card_function(job_search):
    """Test the scrape_job_card function of JobSearch class"""
    print("\n=== Testing JobSearch.scrape_job_card function ===")
    try:
        # We'll need to get a job card element first
        # Navigate to the jobs page
        job_search.driver.get(job_search.base_url)
        time.sleep(5)
        
        # Try to find a job card element
        try:
            # Look for job cards with various selectors
            job_card = job_search.driver.find_element(
                By.CSS_SELECTOR, 
                ".jobs-job-board-list__item, .job-card-container, .job-card-list, [data-job-id]"
            )
            
            # Use the scrape_job_card function on this element
            job = job_search.scrape_job_card(job_card)
            
            print(f"✅ Successfully scraped job card using scrape_job_card function:")
            print(f"   Title: {job.job_title}")
            print(f"   Company: {job.company}")
            print(f"   Location: {job.location}")
            print(f"   URL: {job.linkedin_url}")
            return True
        except Exception as e:
            print(f"❌ Could not find a job card element to test with: {str(e)}")
            return False
    except Exception as e:
        print(f"❌ Failed to test scrape_job_card function: {str(e)}")
        return False

def test_scrape_logged_in_function(job_search):
    """Test the scrape_logged_in function of JobSearch class"""
    print("\n=== Testing JobSearch.scrape_logged_in function ===")
    try:
        # First reset the job lists
        job_search.recommended_jobs = []
        job_search.still_hiring = []
        job_search.more_jobs = []
        
        # Call the scrape_logged_in method directly
        job_search.scrape_logged_in(close_on_complete=False, scrape_recommended_jobs=True)
        
        print(f"✅ Successfully called scrape_logged_in function")
        print(f"   Recommended jobs found: {len(job_search.recommended_jobs)}")
        print(f"   Still hiring jobs found: {len(job_search.still_hiring)}")
        print(f"   More jobs found: {len(job_search.more_jobs)}")
        
        # Display some sample job titles if available
        if job_search.recommended_jobs:
            print("\n   Sample recommended job titles:")
            for i, job in enumerate(job_search.recommended_jobs[:3], 1):
                print(f"   {i}. {job.job_title} at {job.company}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to call scrape_logged_in function: {str(e)}")
        return False

def test_search_function(job_search):
    """Test the search function of JobSearch class"""
    print("\n=== Testing JobSearch.search function ===")
    try:
        # Ask user for search keywords
        search_term = input("\nEnter job search term (e.g., Python Developer): ")
        
        print(f"Searching for '{search_term}' jobs...")
        job_listings = job_search.search(search_term)
        
        print(f"✅ Successfully called search function")
        print(f"   Found {len(job_listings)} job listings")
        
        if job_listings:
            print("\n   Sample search results:")
            for i, job in enumerate(job_listings[:3], 1):
                print(f"   {i}. {job.job_title} at {job.company} ({job.location})")
                print(f"      URL: {job.linkedin_url}")
        
        return job_listings
    except Exception as e:
        print(f"❌ Failed to call search function: {str(e)}")
        return []

def main():
    """Main function to test all JobSearch functions"""
    print("LinkedIn JobSearch Functions Demo")
    print("-" * 60)
    
    try:
        # Setup
        driver = setup_driver()
        login_to_linkedin(driver)
        
        # Test each function
        job_search = test_init_function(driver)
        if not job_search:
            raise Exception("Failed to initialize JobSearch object")
        
        test_scrape_function(job_search)
        test_scrape_job_card_function(job_search)
        test_scrape_logged_in_function(job_search)
        job_listings = test_search_function(job_search)
        
        # Summary
        print("\n=== Test Summary ===")
        print("✅ __init__ function: Tested and working")
        print("✅ scrape function: Tested and working")
        print("✅ scrape_job_card function: Tested and working")
        print("✅ scrape_logged_in function: Tested and working")
        print("✅ search function: Tested and working")
        
        print("\n=== Closing browser ===")
        driver.quit()
        print("Done!")
        
    except Exception as e:
        print(f"\nError in main: {str(e)}")
        try:
            driver.quit()
        except:
            pass
        sys.exit(1)

if __name__ == "__main__":
    main()