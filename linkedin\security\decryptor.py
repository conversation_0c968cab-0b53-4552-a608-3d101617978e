import os
import sys

# When running as a bundled app without a console, sys.stderr can be None.
# This causes argparse to crash. We redirect stderr to a log file to prevent this.
if sys.stderr is None:
    try:
        if hasattr(sys, 'frozen'):
            log_dir = os.path.dirname(sys.executable)
        else:
            log_dir = os.path.dirname(os.path.abspath(__file__))
        error_log = os.path.join(log_dir, 'decryptor-errors.log')
        sys.stderr = open(error_log, 'a')
    except Exception:
        # If we can't create a log file, use a dummy stream to prevent a crash.
        class DummyWriter:
            def write(self, s): pass
            def flush(self): pass
        sys.stderr = DummyWriter()

# The logger also uses sys.stdout. If it's None, let's redirect it to stderr,
# which we've now ensured is not None.
if sys.stdout is None:
    sys.stdout = sys.stderr

import argparse
import getpass
import hashlib
import logging
import base64
from cryptography.fernet import Fernet, InvalidToken
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import padding as asym_padding
from cryptography.hazmat.primitives import serialization

# --- Self-Contained Logging ---
def get_logger(log_path):
    logger = logging.getLogger('standalone_decryptor')
    logger.setLevel(logging.INFO)
    if logger.hasHandlers():
        logger.handlers.clear()
    
    log_dir = os.path.dirname(log_path)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    file_handler = logging.FileHandler(log_path)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_formatter = logging.Formatter('%(message)s')
    stream_handler.setFormatter(stream_formatter)
    logger.addHandler(stream_handler)
    return logger

# --- Self-Contained Core Cryptography Functions ---
def get_key_from_password(password, salt):
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
        backend=default_backend()
    )
    return base64.urlsafe_b64encode(kdf.derive(password.encode()))

def calculate_sha256_for_file(file_path):
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def verify_signature(public_key_path, signature_path, data_path, logger):
    try:
        with open(public_key_path, 'rb') as f:
            public_key = serialization.load_pem_public_key(f.read(), backend=default_backend())
        with open(signature_path, 'rb') as f:
            signature = f.read()
        with open(data_path, 'rb') as f:
            data = f.read()

        public_key.verify(
            signature,
            data,
            asym_padding.PSS(mgf=asym_padding.MGF1(hashes.SHA256()), salt_length=asym_padding.PSS.MAX_LENGTH),
            hashes.SHA256()
        )
        logger.info("SUCCESS: Digital signature on the integrity manifest is valid.")
        return True
    except Exception as e:
        logger.error(f"CRITICAL: Digital signature verification failed: {e}", exc_info=True)
        return False

def run_decryption(project_path, password, logger):
    logger.info("--- Starting Decryption Process ---")
    salt_path = os.path.join(project_path, 'security', 'salt.bin')
    public_key_path = os.path.join(project_path, 'security', 'public_key.pem')
    manifest_path = os.path.join(project_path, 'manifest.sha256')
    signature_path = os.path.join(project_path, 'manifest.sha256.sig')

    # 1. Pre-decryption Integrity Check
    logger.info("--- Step 1: Verifying Integrity ---")
    required_files = [salt_path, public_key_path, manifest_path, signature_path]
    for f in required_files:
        if not os.path.exists(f):
            logger.critical(f"FATAL: Required security file not found: {f}. Decryption cannot proceed.")
            return False

    if not verify_signature(public_key_path, signature_path, manifest_path, logger):
        logger.critical("FATAL: Manifest signature is invalid. The project's integrity is compromised. Halting.")
        return False

    with open(manifest_path, 'r') as f:
        manifest_lines = f.readlines()

    # 2. Decrypt Files
    logger.info("--- Step 2: Proceeding with Decryption ---")
    try:
        with open(salt_path, 'rb') as f:
            salt = f.read()
        key = get_key_from_password(password, salt)
        fernet = Fernet(key)
    except Exception as e:
        logger.error(f"Failed to derive decryption key. Incorrect password or corrupted salt file? Error: {e}")
        return False

    total_files = len(manifest_lines)
    decrypted_count = 0
    error_count = 0

    for i, line in enumerate(manifest_lines):
        expected_hash, rel_path = line.strip().split('  ')
        file_path = os.path.join(project_path, rel_path)
        
        if not os.path.exists(file_path):
            logger.warning(f"File listed in manifest does not exist: {file_path}")
            error_count += 1
            continue

        logger.info(f"[{i+1}/{total_files}] Decrypting: {rel_path}")
        try:
            with open(file_path, 'rb') as f_enc:
                encrypted_data = f_enc.read()
            
            decrypted_data = fernet.decrypt(encrypted_data)

            with open(file_path, 'wb') as f_dec:
                f_dec.write(decrypted_data)
            
            decrypted_count += 1
        except InvalidToken:
            logger.error(f"DECRYPTION FAILED for {rel_path}. The file is corrupt, or the password is incorrect.")
            error_count += 1
        except Exception as e:
            logger.error(f"An unexpected error occurred while decrypting {rel_path}: {e}")
            error_count += 1

    logger.info("--- Decryption Summary ---")
    logger.info(f"Successfully decrypted: {decrypted_count}/{total_files} files.")
    logger.info(f"Errors: {error_count}")

    if error_count > 0:
        logger.warning("Decryption completed with errors. Some files could not be processed.")
        return False
    else:
        logger.info("SUCCESS: All files have been decrypted.")
        return True

def main():
    parser = argparse.ArgumentParser(
        description="Standalone Project Decryptor (v3.0).",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "project_path", 
        help="The path to the root of the encrypted project directory."
    )
    args = parser.parse_args()

    project_path = os.path.abspath(args.project_path)
    log_path = os.path.join(project_path, 'security', 'logs', 'decryption.log')
    logger = get_logger(log_path)

    logger.info("="*60)
    logger.info("Standalone Decryptor Initialized")
    logger.info(f"Project Target: {project_path}")
    logger.info("="*60)

    if not os.path.isdir(project_path):
        logger.critical(f"FATAL: Project path '{project_path}' does not exist or is not a directory.")
        return

    try:
        password = getpass.getpass("Enter the decryption password: ")
        if not password:
            logger.error("No password entered. Aborting.")
            return
    except Exception as e:
        logger.error(f"Could not read password securely: {e}")
        return

    if run_decryption(project_path, password, logger):
        print("\n✅ Decryption process completed successfully.")
        print(f"See log for details: {log_path}")
    else:
        print("\n❌ Decryption process failed or completed with errors.")
        print(f"See log for details: {log_path}")

if __name__ == "__main__":
    main()
