#!/usr/bin/env python
"""
LinkedIn Easy Apply Automation
This script automates the process of searching for jobs on LinkedIn and applying to those
that have "Easy Apply" buttons.
"""

import os
import time
import sys
import json
import re
import requests
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException, StaleElementReferenceException
from linkedin_scraper import actions

# API endpoint for question answering
API_URL = "http://localhost:10000/generate"

class LinkedInEasyApply:
    """Automates LinkedIn Easy Apply process"""

    def __init__(self, headless=False, credentials=None):
        """Initialize the Easy Apply automation with browser settings"""
        self.driver = None
        self.headless = headless
        self.credentials = credentials or {}
        self.jobs_applied = 0
        self.jobs_skipped = 0
        self.current_page = 1
        self.max_pages = 40  # LinkedIn typically shows up to 40 pages
        self.max_retries = 3  # Maximum number of retries for fixing form errors
        self.default_answers = {
            "years_of_experience": "1",
            "education_bachelors": "Yes",
            "willing_to_relocate": "Yes",
            "citizenship_status": "Yes, I am authorized to work in this country for any employer",
            "require_sponsorship": "No",
            "can_commute": "Yes"
        }
        # NEW: Track application step count for intelligent handling
        self.current_step = 0
        # NEW: Track jobs that were applied to with details for analytics
        self.applied_jobs_details = []
        # NEW: Set the number of initial steps to auto-progress
        self.auto_progress_steps = 2  # Auto-progress through first 2 steps
        # NEW: Set threshold score for job relevancy (0-100)
        self.relevancy_threshold = 70
        # NEW: Flag to enable/disable intelligent skipping
        self.use_intelligent_skip = True
        # NEW: Job application history to avoid duplicate applications
        self.job_history = set()
        # NEW: Track application progress to prevent infinite loops
        self.application_progress_tracker = {}
        self.max_same_step_attempts = 5

    def setup(self):
        """Initialize WebDriver with optimal settings"""
        print("Initializing Chrome WebDriver...")
        chrome_options = Options()

        # Add standard options
        if self.headless:
            chrome_options.add_argument("--headless=new")
        #chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")

        # Suppress WebRTC errors and other browser logging
        chrome_options.add_argument("--log-level=3")  # Only show fatal errors
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-web-security")

        # Disable WebRTC to prevent STUN server connection errors
        chrome_options.add_argument("--disable-webrtc")

        # Suppress console errors
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(5)
            print("Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing WebDriver: {str(e)}")
            return False

    def login(self):
        """Authenticate with LinkedIn"""
        if not self.driver:
            print("Cannot login: WebDriver not initialized")
            return False

        print("Logging in to LinkedIn...")
        try:
            email = self.credentials.get('email', os.environ.get('LINKEDIN_EMAIL', None))
            password = self.credentials.get('password', os.environ.get('LINKEDIN_PASSWORD', None))

            if not email or not password:
                print("No credentials provided. Prompting for input...")
                email = input("Enter LinkedIn email: ").strip()
                password = input("Enter LinkedIn password: ").strip()

            self.driver.get("https://www.linkedin.com/login")
            time.sleep(1)  # Reduced from 2 to 1 - Allow page to load

            # Check if already logged in
            if "feed" in self.driver.current_url:
                print("Already logged in")
                return True

            print("Entering credentials...")
            actions.login(self.driver, email, password)
            time.sleep(1.5)  # Reduced from 3 to 1.5 - Allow login to complete

            # Verify successful login
            if "feed" in self.driver.current_url:
                print("Login successful")
                return True
            else:
                print("Login failed - redirected to: " + self.driver.current_url)
                return False

        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return False

    def search_jobs(self, search_term="data analyst", location=""):
        """Search for jobs with the given search term and location"""
        print(f"Searching for '{search_term}' jobs...")

        try:
            # Construct the search URL
            base_url = "https://www.linkedin.com/jobs/search/"
            query_params = f"?keywords={search_term.replace(' ', '%20')}"
            if location:
                query_params += f"&location={location.replace(' ', '%20')}"
            search_url = base_url + query_params

            # Navigate to the search URL
            self.driver.get(search_url)
            time.sleep(2)  # Reduced from 3 to 2 - Wait for page to load

            # Apply "Easy Apply" filter
            try:
                print("Applying 'Easy Apply' filter...")
                # Wait until the Easy Apply filter is clickable
                easy_apply_filter = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button#searchFilter_applyWithLinkedin"))
                )

                # Click it
                easy_apply_filter.click()
                print("Easy Apply filter applied successfully")
                time.sleep(2)  # Wait for filtered results to load
            except Exception as e:
                print(f"Error applying Easy Apply filter: {str(e)}")
                # Continue with search even if filter fails

            # Check if search was successful
            job_count_elements = self.driver.find_elements(By.CSS_SELECTOR, ".jobs-search-results-list__subtitle")
            if job_count_elements:
                job_count_text = job_count_elements[0].text
                print(f"Search results: {job_count_text}")
            else:
                print("Search completed but couldn't determine job count")

            return True

        except Exception as e:
            print(f"Error searching for jobs: {str(e)}")
            return False

    def find_job_cards_on_page(self):
        """Find all job cards on the current page, with support for incremental loading"""
        print(f"Finding job cards on page {self.current_page}...")

        try:
            # Wait for job cards to load
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".job-card-container")))

            # Find job cards using multiple selectors for reliability
            selectors = [
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]"
            ]

            # Initialize variables for incremental loading
            job_cards = []
            previous_count = 0
            max_scroll_attempts = 10
            scroll_attempts = 0

            # Get initial job cards
            for selector in selectors:
                cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if cards and len(cards) > 0:
                    job_cards = cards
                    previous_count = len(job_cards)
                    print(f"Initially found {previous_count} job cards using selector: {selector}")
                    break

            # Incremental scrolling to load all job cards
            while scroll_attempts < max_scroll_attempts:
                # If we have job cards, scroll to the last one to trigger loading more
                if job_cards:
                    try:
                        last_card = job_cards[-1]
                        print(f"Scrolling to job card #{len(job_cards)} to load more...")
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", last_card)

                        # Scroll a bit more to trigger loading
                        self.driver.execute_script("window.scrollBy(0, 300);")
                        time.sleep(2)  # Wait for more cards to load
                    except Exception as e:
                        print(f"Error scrolling to last card: {str(e)}")

                # Re-find job cards to see if more have loaded
                for selector in selectors:
                    cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if cards and len(cards) > 0:
                        job_cards = cards
                        current_count = len(job_cards)
                        print(f"Now found {current_count} job cards (previously {previous_count})")
                        break

                # If we didn't find more cards, we've likely reached the end
                if len(job_cards) <= previous_count:
                    scroll_attempts += 1
                    print(f"No new cards found. Attempt {scroll_attempts}/{max_scroll_attempts}")

                    # Try scrolling a bit more to ensure we've loaded everything
                    self.driver.execute_script("window.scrollBy(0, 500);")
                    time.sleep(1.5)
                else:
                    # Reset scroll attempts if we found more cards
                    scroll_attempts = 0
                    previous_count = len(job_cards)

                    # If we've found a significant number of cards, we can stop
                    if len(job_cards) >= 25:  # LinkedIn typically shows 25 jobs per page
                        print(f"Found {len(job_cards)} job cards, which is enough to process")
                        break

            # Scroll back to top to start processing from the first job
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            if not job_cards:
                print("No job cards found on this page")
            else:
                print(f"Final count: {len(job_cards)} job cards found and ready to process")

            return job_cards

        except TimeoutException:
            print("Timeout waiting for job cards to load")
            return []
        except Exception as e:
            print(f"Error finding job cards: {str(e)}")
            return []

    def get_answer_for_question(self, question_label, input_type=None, input_name=None, placeholder=None, input_id=None):
        """Advanced answer selection for form questions, with robust phone number detection."""
        q = (question_label or "").lower()
        n = (input_name or "").lower() if input_name else ""
        p = (placeholder or "").lower() if placeholder else ""
        i = (input_id or "").lower() if input_id else ""
        # Robust phone number detection
        if any(x in q for x in ["phone", "mobile"]) or any(x in n for x in ["phone", "mobile"]) or any(x in p for x in ["phone", "mobile"]) or any(x in i for x in ["phone", "mobile"]) or (input_type and input_type == "tel"):
            return "7838630502"
        # Email
        if "email" in q or (input_type and input_type == "email"):
            return self.credentials.get("email", "<EMAIL>")
        # Name
        if "name" in q or (input_type == "text" and "name" in n):
            full_name = self.credentials.get("name", "Test User")
            if any(x in q for x in ["first name", "given name"]) or any(x in n for x in ["firstname", "givenname"]):
                return full_name.split()[0]
            elif any(x in q for x in ["last name", "surname"]) or any(x in n for x in ["lastname", "surname"]):
                return full_name.split()[-1] if len(full_name.split()) > 1 else ""
            return full_name
        # Years/experience
        if any(x in q for x in ["years", "experience", "how many", "how much", "number"]):
            return "1"
        # Yes/No questions
        if any(x in q for x in ["authorized", "sponsorship", "relocate", "commute", "citizen", "work in this country"]):
            if "sponsor" in q:
                return "No"
            return "Yes"
        # Default fallback
        return "Yes"

    def handle_additional_questions(self):
        """Handle additional questions that appear during the application process, with robust phone and error handling."""
        print("Checking for additional questions...")
        try:
            wait = WebDriverWait(self.driver, 3)
            questions_section = None
            selectors = [
                "div[data-test-form-element]",
                ".jobs-easy-apply-form-section",
                ".fb-dash-form-element"
            ]
            for selector in selectors:
                try:
                    questions_section = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    if questions_section:
                        print(f"Found questions section with selector: {selector}")
                        break
                except:
                    continue
            if not questions_section:
                print("No questions section found")
                return True
            processed_inputs = set()
            form_content = self.driver
            try:
                form_content = self.driver.find_element(By.CSS_SELECTOR, ".artdeco-modal__content.jobs-easy-apply-modal__content.p0.ember-view")
            except Exception:
                pass
            all_inputs = form_content.find_elements(By.CSS_SELECTOR, "input, textarea, select")
            print(f"Found {len(all_inputs)} input fields in form.")
            for input_elem in all_inputs:
                try:
                    input_type = input_elem.get_attribute("type")
                    input_name = input_elem.get_attribute("name")
                    input_id = input_elem.get_attribute("id")
                    placeholder = input_elem.get_attribute("placeholder")
                    value = input_elem.get_attribute("value")
                    if (input_id and input_id in processed_inputs) or (value and value.strip()):
                        continue
                    if input_id:
                        processed_inputs.add(input_id)
                    label = self.get_element_label_safe(input_elem)
                    answer = self.get_answer_for_question(label, input_type, input_name, placeholder)
                    print(f"Filling '{label}' (type={input_type}, name={input_name}, placeholder={placeholder}) with '{answer}'")
                    if input_elem.tag_name == "select":
                        options = input_elem.find_elements(By.TAG_NAME, "option")
                        for option in options:
                            if option.get_attribute("value") and "select" not in option.text.lower():
                                option.click()
                                break
                    elif input_type in ["radio", "checkbox"]:
                        if not input_elem.is_selected():
                            input_elem.click()
                    else:
                        # Special handling for phone number to avoid complex typing issues
                        if answer == "7838630502":
                            print("Using direct JavaScript injection for phone number.")
                            try:
                                # Use JavaScript to set the value directly
                                self.driver.execute_script("arguments[0].value = arguments[1];", input_elem, answer)
                                # Trigger events to let LinkedIn's JS know the field has changed
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", input_elem)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", input_elem)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('blur', { bubbles: true }));", input_elem)
                                print("Filled phone number using JavaScript.")
                            except Exception as e:
                                print(f"Error with JS phone typing, falling back to human_like_typing: {e}")
                                self.human_like_typing(input_elem, answer, clear_first=True)
                        else:
                            self.human_like_typing(input_elem, answer, clear_first=True)
                except Exception as e:
                    print(f"Error processing input: {str(e)}")
            return True
        except Exception as e:
            print(f"Error handling additional questions: {str(e)}")
            return False

    def get_element_label_safe(self, element):
        """Safe wrapper for get_element_label with fallback."""
        try:
            return self.get_element_label(element)
        except Exception as e:
            print(f"Error getting element label: {str(e)}")
            # Fallback strategies
            try:
                # Try aria-label
                aria_label = element.get_attribute("aria-label")
                if aria_label:
                    return aria_label.strip()

                # Try placeholder
                placeholder = element.get_attribute("placeholder")
                if placeholder:
                    return placeholder.strip()

                # Try name attribute
                name = element.get_attribute("name")
                if name:
                    return name.replace("_", " ").title()

                return "Unknown Field"
            except:
                return "Unknown Field"

    def click_element_robust(self, element, element_name="element"):
        """Robust element clicking with multiple fallback strategies."""
        max_attempts = 5

        for attempt in range(max_attempts):
            try:
                print(f"Attempting to click {element_name} (attempt {attempt + 1}/{max_attempts})")

                # Strategy 1: Scroll element into view and try regular click
                if attempt == 0:
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(0.5)
                    element.click()
                    print(f"Successfully clicked {element_name} with regular click")
                    return True

                # Strategy 2: JavaScript click
                elif attempt == 1:
                    self.driver.execute_script("arguments[0].click();", element)
                    print(f"Successfully clicked {element_name} with JavaScript click")
                    return True

                # Strategy 3: Move to element and click
                elif attempt == 2:
                    action_chains = ActionChains(self.driver)
                    action_chains.move_to_element(element).click().perform()
                    print(f"Successfully clicked {element_name} with ActionChains")
                    return True

                # Strategy 4: Force click with JavaScript after removing overlays
                elif attempt == 3:
                    # Remove potential overlay elements
                    self.driver.execute_script("""
                        var overlays = document.querySelectorAll('.artdeco-modal__overlay, .overlay, .modal-backdrop');
                        overlays.forEach(function(overlay) {
                            overlay.style.display = 'none';
                        });
                    """)
                    time.sleep(0.5)
                    self.driver.execute_script("arguments[0].click();", element)
                    print(f"Successfully clicked {element_name} after removing overlays")
                    return True

                # Strategy 5: Direct event dispatch
                elif attempt == 4:
                    self.driver.execute_script("""
                        arguments[0].dispatchEvent(new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        }));
                    """, element)
                    print(f"Successfully clicked {element_name} with event dispatch")
                    return True

            except Exception as e:
                print(f"Click attempt {attempt + 1} failed for {element_name}: {str(e)}")
                if attempt < max_attempts - 1:
                    time.sleep(1)  # Wait before next attempt
                continue

        print(f"❌ All click attempts failed for {element_name}")
        return False

    def track_application_progress(self, step_identifier):
        """Track application progress to prevent infinite loops."""
        if step_identifier not in self.application_progress_tracker:
            self.application_progress_tracker[step_identifier] = 0

        self.application_progress_tracker[step_identifier] += 1
        attempts = self.application_progress_tracker[step_identifier]

        if attempts > self.max_same_step_attempts:
            print(f"⚠️ Detected infinite loop at step '{step_identifier}' (attempted {attempts} times)")
            return False

        print(f"Progress tracking: Step '{step_identifier}' attempted {attempts} times")
        return True

    def reset_application_progress(self):
        """Reset application progress tracker for new job application."""
        self.application_progress_tracker = {}

    def check_for_form_errors(self):
        """Check for form validation errors and try to fix them, with advanced input logic."""
        print("Checking for form errors...")
        try:
            error_selectors = [
                ".artdeco-inline-feedback__message",
                ".fb-dash-form-element-error",
                "[data-test-form-element-error-messages]"
            ]
            error_messages = []
            for selector in error_selectors:
                error_messages.extend(self.driver.find_elements(By.CSS_SELECTOR, selector))
            error_messages = [e for e in error_messages if e.text.strip() and "applied" not in e.text.lower()]
            if not error_messages:
                print("No form errors found.")
                return True
            print(f"Found {len(error_messages)} form errors. Attempting to fix...")

            # Limit the number of errors we try to fix to prevent infinite loops
            max_errors_to_fix = min(len(error_messages), 10)
            errors_fixed = 0

            for i, error in enumerate(error_messages[:max_errors_to_fix]):
                try:
                    error_text = error.text.strip()
                    print(f"  Fixing error {i+1}/{max_errors_to_fix}: '{error_text}'")
                    form_element = error.find_element(By.XPATH, "./ancestor::div[.//input | .//select | .//textarea][1]")
                    input_fields = form_element.find_elements(By.CSS_SELECTOR, "input, textarea, select")
                    if not input_fields:
                        print("    Could not find associated input field. Skipping.")
                        continue
                    target_input = input_fields[0]
                    input_type = target_input.get_attribute('type')
                    tag_name = target_input.tag_name
                    input_name = target_input.get_attribute('name')
                    placeholder = target_input.get_attribute('placeholder')
                    label = self.get_element_label_safe(target_input)
                    new_answer = self.get_answer_for_question(label, input_type, input_name, placeholder)
                    print(f"    Retrying with answer: '{new_answer}' for '{label}'")

                    if tag_name == 'select':
                        options = target_input.find_elements(By.TAG_NAME, "option")
                        for option in options:
                            if option.get_attribute("value") and "select" not in option.text.lower():
                                option.click()
                                errors_fixed += 1
                                break
                    elif input_type in ['radio', 'checkbox']:
                        if not target_input.is_selected():
                            target_input.click()
                            errors_fixed += 1
                    else:
                        # Special handling for phone number to avoid complex typing issues
                        if new_answer == "7838630502":
                            print("    Using direct JavaScript injection for phone number.")
                            try:
                                # Use JavaScript to set the value directly
                                self.driver.execute_script("arguments[0].value = arguments[1];", target_input, new_answer)
                                # Trigger events to let LinkedIn's JS know the field has changed
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", target_input)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", target_input)
                                self.driver.execute_script("arguments[0].dispatchEvent(new Event('blur', { bubbles: true }));", target_input)
                                print("    Filled phone number using JavaScript.")
                                errors_fixed += 1
                            except Exception as e:
                                print(f"    Error with JS phone typing, falling back to human_like_typing: {e}")
                                self.human_like_typing(target_input, new_answer, clear_first=True)
                                errors_fixed += 1
                        else:
                            self.human_like_typing(target_input, new_answer, clear_first=True)
                            errors_fixed += 1
                except Exception as e:
                    print(f"    Error fixing a specific form error: {str(e)}")
                time.sleep(0.5)

            time.sleep(1.5)
            remaining_errors = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
            remaining_errors = [e for e in remaining_errors if e.text.strip() and "applied" not in e.text.lower()]
            print(f"Attempted to fix {errors_fixed} errors. {len(remaining_errors)} remaining.")

            # Return True if we fixed some errors or if there are fewer errors than before
            return len(remaining_errors) < len(error_messages) or errors_fixed > 0
        except Exception as e:
            print(f"Error checking for form errors: {str(e)}")
            return False

    def proceed_with_application(self, max_steps=10):
        """Navigate through the application process by clicking next/continue/review/submit buttons"""
        print("Proceeding with application...")

        # Reset progress tracker for new application
        self.reset_application_progress()

        steps_taken = 0
        retry_count = 0
        max_retries = 3
        auto_progress_steps_completed = 0  # Track how many steps we've auto-progressed through
        error_fix_attempts = 0  # Track how many times we've tried to fix errors
        max_error_fix_attempts = 3  # Limit error fixing attempts to prevent infinite loops
        last_error_count = 0  # Track error count to detect if we're making progress

        while steps_taken < max_steps and retry_count < max_retries:
            try:
                # NEW: Check if we should intelligently skip question handling for this step
                auto_progress = False

                # First two steps are always auto-progressed (typically contact info and basic info)
                if auto_progress_steps_completed < self.auto_progress_steps:
                    auto_progress = True
                    print(f"🚀 Auto-progressing step {auto_progress_steps_completed+1} without question handling")
                else:
                    # After the initial auto-progress steps, use the standard intelligent logic
                    auto_progress = self.should_auto_progress()

                # Look for "Applied X seconds ago" messages which are success indicators
                try:
                    applied_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message")
                    for msg in applied_messages:
                        if "applied" in msg.text.lower():
                            print(f"✅ Success: {msg.text}")
                            # Look for close button or done button to dismiss
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn], .artdeco-button--primary")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close button for 'already applied' message")
                                    close.click()
                                    time.sleep(1)
                                    return True
                            return True
                except Exception as e:
                    print(f"Error checking for 'Applied' messages: {str(e)}")

                # Look for application sent/success popup first
                try:
                    # Check for "Application sent" dialog header
                    success_headers = self.driver.find_elements(By.CSS_SELECTOR,
                        ".artdeco-modal__header h2, h2#post-apply-modal, .jpac-modal-header")

                    for header in success_headers:
                        if header.is_displayed() and any(text in header.text.lower() for text in ["application sent", "success", "applied"]):
                            print("Found application sent confirmation dialog")

                            # Look for "Done" button in the modal
                            done_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__actionbar .artdeco-button--primary, #ember554, button[data-test-dialog-primary-btn], .artdeco-button--primary")

                            for btn in done_buttons:
                                if btn.is_displayed() and "done" in btn.text.lower():
                                    print("Clicking 'Done' button on success dialog")
                                    btn.click()
                                    time.sleep(2)
                                    return True

                            # If no Done button found, try to close the modal with the X button
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn]")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close (X) button on success dialog")
                                    close.click()
                                    time.sleep(2)
                                    return True
                except Exception as e:
                    print(f"Error checking for success dialog: {str(e)}")

                # Check for form errors - if there are errors, we'll handle questions
                has_errors = False
                try:
                    # Only check for errors if not auto-progressing
                    if not auto_progress:
                        error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
                        current_error_count = len([e for e in error_messages if e.text.strip() and "applied" not in e.text.lower()])
                        has_errors = current_error_count > 0

                        if has_errors:
                            print(f"Form errors detected ({current_error_count} errors) - handling additional questions")

                            # Track error handling progress to prevent infinite loops
                            error_step_id = f"form_errors_{current_error_count}"
                            if not self.track_application_progress(error_step_id):
                                print(f"⚠️ Breaking out of infinite error handling loop")
                                auto_progress = False
                                has_errors = False
                            else:
                                # Check if we're stuck in a loop with the same number of errors
                                if current_error_count == last_error_count and error_fix_attempts >= max_error_fix_attempts:
                                    print(f"⚠️ Stuck with {current_error_count} errors after {error_fix_attempts} attempts - skipping error handling")
                                    auto_progress = False
                                    has_errors = False  # Skip error handling to break the loop
                                else:
                                    self.handle_additional_questions()
                                    # Validate errors were fixed
                                    error_check_result = self.check_for_form_errors()
                                    error_fix_attempts += 1
                                    last_error_count = current_error_count

                                    # If errors remain, we'll stop auto-progressing and handle manually
                                    if not error_check_result:
                                        print("⚠️ Errors remain after handling questions - switching to manual mode")
                                        auto_progress = False

                except Exception as e:
                    print(f"Error checking for form errors: {str(e)}")

                # Detect if we're on a question/review screen
                is_question_screen = False
                try:
                    # Only check for question screen if not auto-progressing
                    if not auto_progress:
                        # Check for common question screen indicators
                        question_indicators = [
                            "div[data-test-form-element]",
                            ".jobs-easy-apply-form-section",
                            ".fb-dash-form-element",
                            "input.artdeco-text-input--input:not([value])",  # Empty inputs needing values
                            "select:not(:disabled)",  # Enabled dropdowns
                            "[data-test-text-entity-list-form-component]"  # Forms
                        ]

                        for indicator in question_indicators:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and len([e for e in elements if e.is_displayed()]) > 0:
                                is_question_screen = True
                                break
                except Exception as e:
                    print(f"Error checking for question screen: {str(e)}")

                # Try multiple selectors for next/continue/review/submit buttons
                button_selectors = [
                    "button[aria-label='Review your application']",
                    "button[aria-label='Review']",
                    "button[aria-label='Submit application']",
                    "button[aria-label='Continue to next step']",
                    "button[aria-label='Next']",
                    "button[data-easy-apply-next-button]",
                    "button[aria-label='Submit']",
                    "button[aria-label='Done']",
                    "footer button.artdeco-button--primary",
                    "button.artdeco-button--primary",
                    ".artdeco-modal__footer button.artdeco-button--primary",
                    ".artdeco-modal__actionbar button.artdeco-button--primary",
                    "[data-control-name='continue_unify']",
                    "[data-control-name='submit_unify']",
                    "#ember554"  # Specific ID from your example
                ]

                # Look for a button to click
                button_found = False
                button_is_review = False
                for selector in button_selectors:
                    wait = WebDriverWait(self.driver, 3)
                    try:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            try:
                                if button.is_displayed() and button.is_enabled():
                                    button_text = button.text.strip()
                                    # Check if this is a "review" type button that might need question handling
                                    button_is_review = any(text.lower() in button_text.lower() for text in ["review"])

                                    # If it's a review button or we're on a question screen, handle questions first
                                    # Only if we're not auto-progressing
                                    if (button_is_review or is_question_screen) and not auto_progress:
                                        print(f"Found '{button_text}' button on a form screen - handling questions first")
                                        self.handle_additional_questions()

                                    # Click the button
                                    if any(text.lower() in button_text.lower() for text in ["review", "continue", "next", "submit", "apply", "done"]):
                                        # Track progress to prevent infinite loops
                                        step_id = f"button_{button_text.lower().replace(' ', '_')}"
                                        if not self.track_application_progress(step_id):
                                            print(f"⚠️ Breaking out of infinite loop for button '{button_text}'")
                                            return False

                                        print(f"Clicking button: '{button_text}'")
                                        button.click()
                                        steps_taken += 1

                                        # If we're auto-progressing, increment counter
                                        if auto_progress:
                                            auto_progress_steps_completed += 1
                                            print(f"Auto-progress step {auto_progress_steps_completed} completed")

                                        button_found = True
                                        time.sleep(2)  # Wait for next screen to load

                                        # For buttons with Next or Continue and we're auto-progressing,
                                        # immediately continue without waiting for next iteration
                                        # This allows continuous clicking through auto-progress steps
                                        next_or_continue = any(text.lower() in button_text.lower() for text in ["continue", "next"])
                                        if next_or_continue and auto_progress and auto_progress_steps_completed < self.auto_progress_steps:
                                            print(f"Continuing to auto-progress next step...")
                                            # Don't break or return, let the loop continue immediately
                                            # Reset retry count to ensure we can continue
                                            retry_count = 0
                                            # Skip the rest of the button selectors and continue with next iteration
                                            break  # Just break the inner button loop
                                        break  # Break the inner button loop
                            except StaleElementReferenceException:
                                print("Element became stale, retrying...")
                                continue
                            except Exception as e:
                                print(f"Error clicking button: {str(e)}")

                        if button_found:
                            # Reset retry count on successful button click
                            retry_count = 0
                            break
                    except:
                        continue

                if not button_found:
                    # If no buttons found, we might be done or stuck
                    print("No more buttons found - application may be complete or stuck")

                    # Look for success message or confirmation
                    success_indicators = [
                        ".artdeco-modal__content:contains('Application submitted')",
                        ".artdeco-modal__content:contains('applied')",
                        "h2:contains('Application submitted')",
                        ".artdeco-modal__confirm-dialog-btn",
                        "[data-test-modal-close-btn]",
                        ".jobs-details__main-content",
                        ".jpac-modal-header"  # From your example
                    ]

                    for indicator in success_indicators:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and any(e.is_displayed() for e in elements):
                                print("Found application success indicator or returned to job details")
                                return True
                        except:
                            continue

                    # If we can't find success indicators or buttons, increment retry count
                    retry_count += 1

                    if retry_count >= max_retries:
                        print(f"Reached maximum retries ({max_retries}) without finding buttons or success indicators")
                        # Try closing any open dialogs before giving up
                        try:
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button[aria-label='Dismiss'], button[aria-label='Close'], .artdeco-modal__dismiss")
                            for close in close_buttons:
                                if close.is_displayed():
                                    close.click()
                                    print("Closed dialog")
                                    time.sleep(1)
                        except:
                            pass
                        return False
                        
                    print(f"No buttons found, retry {retry_count}/{max_retries}")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"Error during application process: {str(e)}")
                
                # NEW: Try to handle any blocking dialogs with default "Yes" responses
                print("Attempting to handle any blocking dialogs...")
                dialog_handled = self.handle_blocking_dialogs()
                
                if dialog_handled:
                    print("Successfully handled blocking dialog, continuing...")
                    # Reset retry count since we handled the issue
                    retry_count = 0
                    time.sleep(1)
                    continue
                
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Reached maximum retries ({max_retries}) due to errors")
                    return False
                time.sleep(2)

        return steps_taken > 0  # Return True if we managed to take at least one step

    def process_job_card(self, job_card):
        """Process a single job card - click it and check for Easy Apply"""
        try:
            # First, scroll the job card into view to ensure it's visible
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", job_card)
                time.sleep(1)  # Wait for scroll to complete
                print("Scrolled job card into view")
            except Exception as e:
                print(f"Error scrolling to job card: {str(e)}")

            # Extract job info before clicking
            try:
                job_title = job_card.find_element(By.CSS_SELECTOR, ".job-card-list__title").text
            except:
                job_title = "Unknown Job Title"

            try:
                company = job_card.find_element(By.CSS_SELECTOR, ".job-card-container__company-name").text
            except:
                company = "Unknown Company"

            print(f"\nProcessing: {job_title} at {company}")

            # Store job card identifier to handle stale element reference
            try:
                job_id = job_card.get_attribute("data-job-id") or job_card.get_attribute("id")
                print(f"Job card ID: {job_id}")
            except:
                job_id = None

            # Click on the job card to view details - with retry mechanism
            max_click_attempts = 3
            click_success = False

            for attempt in range(max_click_attempts):
                try:
                    # Make sure the element is in view before clicking
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", job_card)
                    time.sleep(0.5)

                    # Try regular click first
                    job_card.click()
                    click_success = True
                    print(f"Clicked job card (attempt {attempt+1})")
                    time.sleep(2)  # Wait for job details to load
                    break
                except StaleElementReferenceException:
                    print(f"Stale element reference on attempt {attempt+1}, refreshing elements")

                    # Try to re-find the job card using its ID
                    if job_id:
                        try:
                            # Re-find the job card
                            refreshed_cards = self.driver.find_elements(By.CSS_SELECTOR, f"[data-job-id='{job_id}'], #{job_id}")
                            if refreshed_cards:
                                job_card = refreshed_cards[0]
                                continue  # Try clicking again with the refreshed element
                        except Exception as e:
                            print(f"Error re-finding job card: {str(e)}")

                    # If couldn't re-find by ID, try JavaScript click
                    try:
                        print("Failed to click job card, trying JavaScript click")
                        self.driver.execute_script("arguments[0].click();", job_card)
                        click_success = True
                        time.sleep(2)
                        break
                    except Exception as js_e:
                        print(f"JavaScript click also failed: {str(js_e)}")
                        time.sleep(1)
                except Exception as e:
                    print(f"Regular click failed (attempt {attempt+1}): {str(e)}")
                    # Try JavaScript click as fallback
                    try:
                        print("Using JavaScript click as fallback")
                        self.driver.execute_script("arguments[0].click();", job_card)
                        click_success = True
                        time.sleep(2)
                        break
                    except Exception as js_e:
                        print(f"JavaScript click also failed: {str(js_e)}")
                        time.sleep(1)

            if not click_success:
                print("❌ Failed to click job card after multiple attempts, skipping")
                self.jobs_skipped += 1
                return False

            # NEW: Check if we've already applied to this job - this check happens before any other processing
            try:
                # Look for the "Applied" indicator
                applied_indicators = self.driver.find_elements(By.CSS_SELECTOR,
                    ".artdeco-inline-feedback--success, .jobs-s-apply .artdeco-inline-feedback__message")

                for indicator in applied_indicators:
                    if indicator.is_displayed() and "applied" in indicator.text.lower():
                        print("⏭️ Already applied to this job (Applied indicator found) - skipping")
                        self.jobs_skipped += 1
                        return False

                # Also check for "See application" links which indicate we've already applied
                see_application_links = self.driver.find_elements(By.CSS_SELECTOR,
                    "a[href*='application'], button[data-test-app-aware-link*='application']")
                
                if see_application_links and any(link.is_displayed() for link in see_application_links):
                    print("⏭️ Already applied to this job (See application link found) - skipping")
                    self.jobs_skipped += 1
                    return False

                print("No 'Already applied' indicator found - continuing with application process")
            except Exception as e:
                print(f"Error checking if already applied: {str(e)}")

            # Extract detailed job information for advanced filtering
            job_details = self.extract_job_details()

            # Check if we've already applied to this job
            if job_details.get("id") and self.is_job_already_applied(job_details["id"]):
                print("⏭️ Already applied to this job - skipping")
                self.jobs_skipped += 1
                return False

            # Check if the job meets our relevancy criteria
            if not self.check_job_relevancy(job_details):
                print("⏭️ Job doesn't meet relevancy criteria - skipping")
                self.jobs_skipped += 1
                return False

            # Check if Easy Apply button exists
            try:
                # Wait for job details panel to load
                wait = WebDriverWait(self.driver, 5)

                # Check for Easy Apply button (using multiple potential selectors)
                easy_apply_selectors = [
                    ".jobs-apply-button:not([disabled])",
                    "button.jobs-apply-button",
                    "button[data-control-name='jobs_apply_button']",
                    ".jobs-s-apply button"
                ]

                easy_apply_button = None
                for selector in easy_apply_selectors:
                    try:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            if "Easy Apply" in button.text and button.is_displayed() and button.is_enabled():
                                easy_apply_button = button
                                break
                        if easy_apply_button:
                            break
                    except:
                        continue

                if easy_apply_button:
                    print("✅ Found Easy Apply button")

                    # Click the Easy Apply button with robust error handling
                    click_success = self.click_element_robust(easy_apply_button, "Easy Apply button")

                    if not click_success:
                        print("❌ Failed to click Easy Apply button after all attempts")
                        self.jobs_skipped += 1
                        return False

                    try:
                        time.sleep(2)

                        # Reset step counter for this new application
                        self.current_step = 0

                        # Process the multi-step application
                        if self.proceed_with_application(max_steps=10):
                            print("✅ Application completed successfully")
                            self.jobs_applied += 1

                            # Track job application for analytics and history
                            if job_details.get("id"):
                                self.job_history.add(job_details["id"])

                            # Store job details for analytics
                            job_details["application_date"] = time.strftime("%Y-%m-%d %H:%M")
                            self.applied_jobs_details.append(job_details)

                            # Save application history periodically
                            if len(self.applied_jobs_details) % 5 == 0:
                                try:
                                    self.save_application_history()
                                except Exception as e:
                                    print(f"Error saving application history: {str(e)}")
                        else:
                            print("⚠️ Application process was incomplete or encountered issues")
                            # Still count it as applied if we started the process
                            self.jobs_applied += 1

                        # Close the application modal if it appears
                        try:
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button[aria-label='Dismiss'], button[aria-label='Close'], .artdeco-modal__dismiss")
                            for close in close_buttons:
                                if close.is_displayed():
                                    close.click()
                                    print("Closed application modal")
                                    time.sleep(1)
                                    break
                        except:
                            pass

                        return True
                    except Exception as e:
                        print(f"Error during application process: {str(e)}")
                        return False
                else:
                    print("❌ No Easy Apply button found - skipping job")
                    self.jobs_skipped += 1
                    return False

            except Exception as e:
                print(f"Error checking for Easy Apply button: {str(e)}")
                self.jobs_skipped += 1
                return False

        except Exception as e:
            print(f"Error processing job card: {str(e)}")
            self.jobs_skipped += 1
            return False

    def save_application_history(self):
        """Save application history to a JSON file"""
        try:
            history_data = {
                "jobs_applied": self.jobs_applied,
                "jobs_skipped": self.jobs_skipped,
                "applied_jobs": self.applied_jobs_details,
                "job_ids": list(self.job_history)
            }

            with open("application_history.json", "w") as f:
                json.dump(history_data, f, indent=2)

            print(f"Saved application history ({self.jobs_applied} jobs)")
            return True
        except Exception as e:
            print(f"Error saving application history: {str(e)}")
            return False

    def go_to_next_page(self):
        """Navigate to the next page of job search results"""
        print(f"\nNavigating to page {self.current_page + 1}...")

        try:
            # Find the next page button using multiple selectors
            next_button = None
            selectors = [
                f"button[aria-label='Page {self.current_page + 1}']",
                ".artdeco-pagination__button--next",
                ".artdeco-pagination__button.artdeco-pagination__button--next",
                f"li[data-test-pagination-page-btn='{self.current_page + 1}'] button"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and len(elements) > 0:
                        next_button = elements[0]
                        break
                except:
                    continue

            if not next_button:
                print("Next page button not found - may be on last page")
                return False

            # Check if the button is disabled
            if next_button.get_attribute("disabled") == "true":  # Fixed getAttribute to get_attribute
                print("Next page button is disabled - reached last page")
                return False

            # Click the next page button
            next_button.click()
            print(f"Clicked next page button")
            self.current_page += 1
            time.sleep(3)  # Wait for next page to load            # Check if navigation was successful
            return True

        except Exception as e:
            print(f"Error navigating to next page: {str(e)}")
            return False

    def apply_date_posted_filter(self, date_range="Past 24 hours"):
        """NEW: Apply the 'Date posted' filter after a search with improved reliability."""
        print(f"\nApplying 'Date posted' filter: {date_range}...")
        try:
            # 1. Click the 'Date posted' filter button to open the dropdown
            date_posted_button_selector = "button#searchFilter_timePostedRange"
            date_posted_button = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, date_posted_button_selector))
            )
            self.driver.execute_script("arguments[0].click();", date_posted_button)
            print("Clicked 'Date posted' filter button.")
            time.sleep(1.5)  # Increased wait

            # 2. Select the desired date range
            date_range_options = {
                "Any time": "timePostedRange-",
                "Past 24 hours": "timePostedRange-r86400",
                "Past week": "timePostedRange-r604800",
                "Past month": "timePostedRange-r2592000"
            }

            if date_range not in date_range_options:
                print(f"Invalid date range '{date_range}'. Defaulting to 'Past 24 hours'.")
                date_range = "Past 24 hours"

            label_for = date_range_options[date_range]
            date_range_label_selector = f"label[for='{label_for}']"

            date_range_label = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, date_range_label_selector))
            )
            self.driver.execute_script("arguments[0].click();", date_range_label)
            print(f"Selected '{date_range}'.")
            time.sleep(1.5)  # Increased wait            # 3. Click the 'Show results' button to apply the filter (more robustly)
            show_results_button_selectors = [
                "button[aria-label*='Apply current filter to show'][aria-label*='results']",
                "button[data-control-name='filter_show_results']",
                ".reusable-search-filters-buttons button.artdeco-button--primary",
                "//button[contains(@aria-label, 'Apply current filter to show') and contains(@aria-label, 'results')]",
                "//button[contains(., 'Show') and contains(., 'results')]",
                "button[id^='ember'][aria-label*='show'][aria-label*='results']"
            ]

            button_clicked = False
            for selector in show_results_button_selectors:
                try:
                    by = By.XPATH if selector.startswith("//") else By.CSS_SELECTOR
                    show_results_button = WebDriverWait(self.driver, 7).until(
                        EC.element_to_be_clickable((by, selector))
                    )
                    print(f"Found 'Show results' button with selector: {selector}")
                    self.driver.execute_script("arguments[0].click();", show_results_button)
                    print(f"Successfully clicked 'Show results' button")
                    button_clicked = True
                    break
                except Exception as e:
                    print(f"Could not find 'Show results' button with selector: {selector} - {str(e)}")

            if not button_clicked:
                # Final fallback: try to find any button in the filter buttons container that contains "Show" or "results"
                try:
                    print("Trying fallback method to find Show results button...")
                    filter_buttons = self.driver.find_elements(By.CSS_SELECTOR, ".reusable-search-filters-buttons button")
                    for button in filter_buttons:
                        button_text = button.text.lower()
                        aria_label = button.get_attribute("aria-label").lower() if button.get_attribute("aria-label") else ""
                        if ("show" in button_text and "result" in button_text) or ("show" in aria_label and "result" in aria_label):
                            print(f"Found Show results button using fallback method: {button.text}")
                            self.driver.execute_script("arguments[0].click();", button)
                            button_clicked = True
                            break
                except Exception as e:
                    print(f"Fallback method also failed: {str(e)}")

            if not button_clicked:
                print("Warning: 'Show results' button not found or clicked. Assuming auto-refresh.")

            time.sleep(3)  # Wait for results to reload

            print("Successfully applied 'Date posted' filter.")
            return True

        except Exception as e:
            print(f"Error applying 'Date posted' filter: {str(e)}")
            try:
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
            except:
                pass
            return False

    def run_easy_apply_process(self, search_term="data analyst", location="", max_applications=10, job_mode=None):
        """Run the entire Easy Apply process"""
        print(f"\n{'=' * 60}")
        print(" LINKEDIN EASY APPLY AUTOMATION ")
        print(f"{'=' * 60}")

        if not self.setup():
            return False

        if not self.login():
            self.cleanup()
            return False

        # Get job search mode if not provided
        if job_mode is None:
            job_mode = self.get_job_mode_selection()
            if job_mode is None:  # User cancelled
                self.cleanup()
                return False

        # Navigate to jobs based on selected mode
        search_success = False
        if job_mode == "top_picks":
            print("\nUsing 'Top job picks for you' mode...")
            search_success = self.search_top_job_picks()
        else:
            print(f"\nUsing normal search mode with term: '{search_term}'...")
            search_success = self.search_jobs(search_term, location)

            # Apply date posted filter if search was successful
            if search_success:
                self.apply_date_posted_filter()

        if not search_success:
            self.cleanup()
            return False

        applications_count = 0
        keep_going = True

        print("\nStarting Easy Apply process...")
        try:
            while keep_going and self.current_page <= self.max_pages and applications_count < max_applications:
                print(f"\n{'=' * 40}")
                print(f" PAGE {self.current_page} ")
                print(f"{'=' * 40}")

                # Find and process job cards
                job_cards = self.find_job_cards_on_page()

                # Ensure we process each page completely before moving to the next
                if job_cards:
                    # Process all jobs on current page
                    jobs_processed_on_page = 0
                    for i, job_card in enumerate(job_cards):
                        print(f"Processing job {i+1}/{len(job_cards)} on page {self.current_page}")

                        # Process the job card
                        try:
                            job_processed = self.process_job_card(job_card)
                            jobs_processed_on_page += 1

                            # If we've applied, increment count
                            if job_processed:
                                applications_count += 1
                                print(f"✅ Application {applications_count}/{max_applications} completed")

                                # Take anti-detection measures every 3-5 applications
                                if applications_count % random.randint(3, 5) == 0:
                                    self.avoid_detection()

                                # Break if we've reached max applications
                                if applications_count >= max_applications:
                                    print(f"Reached maximum number of applications ({max_applications})")
                                    keep_going = False
                                    break

                        except StaleElementReferenceException:
                            print("⚠️ Stale element reference - job card may have changed. Continuing with next job.")
                            # Don't break, just continue to next job.
                            continue
                        except Exception as e:
                            print(f"⚠️ Error processing job card: {str(e)}")
                            # Don't break, just continue to next job.
                            continue

                        # Pause between job applications (3-5 seconds)
                        time.sleep(3 + random.random() * 2)

                    print(f"Completed processing {jobs_processed_on_page} jobs on page {self.current_page}")
                else:
                    print(f"No job cards found on page {self.current_page}")

                # Move to next page if we haven't reached max applications and processed all jobs on current page
                if keep_going and applications_count < max_applications:
                    if not self.go_to_next_page():
                        print("No more pages available - end of search results")
                        break

            # Print summary
            print(f"\n{'=' * 60}")
            print(" EASY APPLY PROCESS COMPLETED ")
            print(f"{'=' * 60}")
            print(f"Jobs applied: {self.jobs_applied}")
            print(f"Jobs skipped (no Easy Apply): {self.jobs_skipped}")
            print(f"Pages processed: {self.current_page}")
            print(f"{'=' * 60}")

            # Save application history
            try:
                self.save_application_history()
            except Exception as e:
                print(f"Error saving application history: {str(e)}")

        except Exception as e:
            print(f"Error during Easy Apply process: {str(e)}")
        finally:
            self.cleanup()

        return self.jobs_applied > 0

    def cleanup(self):
        """Close browser and clean up resources"""
        if self.driver:
            print("\nClosing browser...")
            self.driver.quit()

    def detect_application_step(self):
        """NEW: Detect which step of the application process we're currently on"""
        try:
            # Check for progress indicator
            progress_indicators = self.driver.find_elements(By.CSS_SELECTOR,
                ".artdeco-completeness-meter-linear__progress-element, .artdeco-progress-bar__progress")

            # Try to get the current step from the progress meter
            current_step = 0
            total_steps = 0

            if progress_indicators:
                for indicator in progress_indicators:
                    try:
                        # Try to parse step count from aria-valuenow attribute
                        current_step_str = indicator.get_attribute("aria-valuenow")
                        if current_step_str and current_step_str.isdigit():
                            current_step = int(current_step_str)

                        # Try to parse total from aria-valuemax attribute
                        total_steps_str = indicator.get_attribute("aria-valuemax")
                        if total_steps_str and total_steps_str.isdigit():
                            total_steps = int(total_steps_str)

                        if current_step and total_steps:
                            print(f"Detected application progress: Step {current_step}/{total_steps}")
                            return current_step, total_steps
                    except:
                        continue

            # If we couldn't get step from progress bar, try step headers
            step_headers = self.driver.find_elements(By.CSS_SELECTOR,
                ".artdeco-modal__header span, .jobs-easy-apply-modal h3, .artdeco-modal__title")

            if step_headers:
                for header in step_headers:
                    try:
                        header_text = header.text.lower()
                        if "step" in header_text and "of" in header_text:
                            # Parse Step X of Y format
                            step_match = re.search(r'step\s+(\d+)\s+of\s+(\d+)', header_text)
                            if step_match:
                                current_step = int(step_match.group(1))
                                total_steps = int(step_match.group(2))
                                print(f"Detected application progress from header: Step {current_step}/{total_steps}")
                                return current_step, total_steps
                    except:
                        continue

            # Otherwise estimate based on the form content
            form_content = self.driver.find_elements(By.CSS_SELECTOR, ".jobs-easy-apply-form-section__grouping")
            if form_content:
                # First step usually has contact info which is minimal
                if len(form_content) <= 2:
                    print("Estimated application step: 1 (contact info)")
                    return 1, 0  # 0 total means unknown total
                # Review step usually has many sections
                elif len(form_content) >= 5:
                    print("Estimated application step: Review step")
                    return 0, 0  # 0,0 means likely review step
                else:
                    print("Estimated application step: Middle step with questions")
                    return 2, 0  # Middle step

            print("Could not determine current application step")
            return 0, 0  # Unknown

        except Exception as e:
            print(f"Error detecting application step: {str(e)}")
            return 0, 0  # Unknown on error

    def should_auto_progress(self):
        """NEW: Determine if we should automatically progress to the next step without handling questions"""
        try:
            # Check if intelligent skip is enabled
            if not self.use_intelligent_skip:
                print("Intelligent skip is disabled, processing all steps normally")
                return False

            # Get current step
            current_step, total_steps = self.detect_application_step()

            # Store detected step for later use
            self.current_step = current_step

            # If we're in the first steps (usually just contact info), auto-progress
            if 1 <= current_step <= self.auto_progress_steps:
                print(f"🚀 Auto-progressing through step {current_step} without question handling")
                return True

            # Check if this appears to be a simple contact info screen with no questions
            try:
                # Check for indicators of a complex form that would need handling
                complex_form_indicators = [
                    "input[type='text']:not([value])",  # Empty text inputs
                    "select:not(:disabled)",            # Enabled dropdowns
                    "textarea:not([value])",            # Empty textareas
                    "input[type='radio']",              # Radio buttons
                    "input[type='checkbox']",           # Checkboxes
                    ".fb-dash-form-element-error"       # Error messages
                ]

                form_complexity = 0
                for indicator in complex_form_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    # Only count visible elements
                    visible_elements = [e for e in elements if e.is_displayed()]
                    form_complexity += len(visible_elements)

                # If form is very simple (just prefilled fields), auto-progress
                if form_complexity <= 1:
                    print("Form appears to be simple with prefilled fields - auto-progressing")
                    return True
            except Exception as e:
                print(f"Error checking form complexity: {str(e)}")

            print(f"Step {current_step}: Manual handling required, not auto-progressing")
            return False

        except Exception as e:
            print(f"Error determining auto-progress status: {str(e)}")
            return False

    def extract_job_details(self):
        """NEW: Extract key details about the current job for analysis and filtering"""
        job_details = {
            "title": "",
            "company": "",
            "location": "",
            "description": "",
            "id": "",
            "date_posted": "",
            "seniority": "",
            "job_functions": []
        }

        try:
            # Extract job title - updated with more comprehensive selectors
            title_selectors = [
                ".jobs-unified-top-card__job-title, .job-details-jobs-unified-top-card__job-title",
                ".t-24.job-details-jobs-unified-top-card__job-title h1",
                "h1.t-24.t-bold.inline",
                ".job-view-layout h1",
                "h1.jobs-unified-top-card__job-title"
            ]

            for selector in title_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].text.strip():
                    job_details["title"] = elements[0].text.strip()
                    print(f"Found job title using selector: {selector}")
                    break

            # Extract company name - updated with more comprehensive selectors
            company_selectors = [
                ".jobs-unified-top-card__company-name, .job-details-jobs-unified-top-card__company-name",
                ".job-details-jobs-unified-top-card__company-name a",
                ".jobs-unified-top-card__subtitle-primary-grouping a",
                ".jobs-top-card__company-url"
            ]

            for selector in company_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].text.strip():
                    job_details["company"] = elements[0].text.strip()
                    print(f"Found company name using selector: {selector}")
                    break

            # Extract location - updated with more comprehensive selectors
            location_selectors = [
                ".jobs-unified-top-card__bullet, .job-details-jobs-unified-top-card__bullet",
                ".job-details-jobs-unified-top-card__primary-description-container .t-black--light span",
                ".jobs-unified-top-card__subtitle-primary-grouping .jobs-unified-top-card__bullet",
                ".jobs-unified-top-card__subtitle-secondary-grouping .jobs-unified-top-card__bullet"
            ]

            for selector in location_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Try to find the location text within the elements
                    for element in elements:
                        text = element.text.strip()
                        # Skip empty text or text that's likely not location
                        if text and not any(skip in text.lower() for skip in ["ago", "applicant", "remote"]):
                            job_details["location"] = text
                            print(f"Found location: {text}")
                            break
                    if job_details["location"]:
                        break

            # If no specific location found, look for the entire location container
            if not job_details["location"]:
                location_containers = self.driver.find_elements(By.CSS_SELECTOR,
                    ".job-details-jobs-unified-top-card__primary-description-container")

                if location_containers:
                    full_text = location_containers[0].text
                    # Extract location from the full text using regex patterns
                    location_matches = re.search(r'(.*?)·\s+\d+\s+(?:hour|day|week|month)', full_text)
                    if location_matches:
                        job_details["location"] = location_matches.group(1).strip()
                        print(f"Extracted location from container: {job_details['location']}")

            # Extract job ID (for deduplication)
            try:
                current_url = self.driver.current_url
                # Try multiple URL patterns
               
                job_id_patterns = [
                    r'currentJobId=(\d+)',
                    r'jobs/view/(\d+)',
                    r'jobId=(\d+)'
                ]

                for pattern in job_id_patterns:
                    job_id_match = re.search(pattern, current_url)
                    if job_id_match:
                        job_details["id"] = job_id_match.group(1)
                        print(f"Found job ID: {job_details['id']}")
                        break
            except:
                pass

            # Extract job description
            description_elements = self.driver.find_elements(By.CSS_SELECTOR,
                ".jobs-description, .jobs-unified-description__content, .jobs-description-content__text")
            if description_elements:
                job_details["description"] = description_elements[0].text.strip()

            # Extract job criteria (seniority, job functions, etc.)
            criteria_elements = self.driver.find_elements(By.CSS_SELECTOR, ".description__job-criteria-item")
            for element in criteria_elements:
                try:
                    label_element = element.find_element(By.CSS_SELECTOR, ".description__job-criteria-subheader")
                    value_element = element.find_element(By.CSS_SELECTOR, ".description__job-criteria-text")

                    label = label_element.text.strip().lower()
                    value = value_element.text.strip()

                    if "seniority" in label:
                        job_details["seniority"] = value
                    elif "function" in label:
                        job_details["job_functions"] = [item.strip() for item in value.split(',')]
                    elif "posted" in label or "date" in label:
                        job_details["date_posted"] = value
                except:
                    continue

            # Try to identify job type (full-time, internship, etc.) from UI labels
            try:
                job_type_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".ui-label.ui-label--accent-3 span, .job-details-preferences-and-skills__pill .ui-label")

                for element in job_type_elements:
                    text = element.text.strip().lower()
                    if text and any(job_type in text for job_type in ["full-time", "part-time", "contract", "internship"]):
                        job_details["job_type"] = text
                        print(f"Found job type: {text}")
                        break
            except Exception as e:
                print(f"Error extracting job type: {e}")

            # Print found details for debugging
            print(f"Extracted job details: '{job_details['title']}' at '{job_details['company']}'")
            return job_details

        except Exception as e:
            print(f"Error extracting job details: {str(e)}")
            return job_details

    def is_job_already_applied(self, job_id):
        """NEW: Check if we've already applied to this job"""
        if not job_id:
            return False

        # Check our history
        if job_id in self.job_history:
            print(f"Already applied to job ID {job_id}")
            return True

        # Check LinkedIn's UI for "Applied" indicator
        try:
            applied_indicators = [
                ".jobs-s-apply__applied-tag",
                ".jobs-applied-badge",
                "[data-test-applied-date]",
                "[data-control-name='view_application']"
            ]

            for indicator in applied_indicators:
                elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                if elements and any(e.is_displayed() for e in elements):
                    print("Found 'Applied' indicator on job")
                    self.job_history.add(job_id)  # Add to history
                    return True
        except:
            pass

        return False

    def check_job_relevancy(self, job_details):
        """NEW: Analyze job details to determine if this job is relevant"""
        if not job_details or not job_details["title"] or not job_details["description"]:
            return True  # Default to accepting if we don't have enough details

        score = 100  # Start with perfect score and deduct points
        red_flags = []

        # Check seniority level - avoid senior positions if configured
        if "seniority" in job_details and job_details["seniority"]:
            seniority = job_details["seniority"].lower()
            if "senior" in seniority or "principal" in seniority or "director" in seniority:
                score -= 15
                red_flags.append(f"Senior position: {job_details['seniority']}")

        # Check for keywords in description that indicate excessive requirements
        description = job_details["description"].lower()
        requirement_indicators = [
            ("years", r'(\d+)\+?\s*(?:years|yrs)', 7),  # Avoid jobs requiring 7+ years experience
            ("phd", r'ph\.?d', 10),  # Avoid jobs requiring PhD
            ("master", r'master[\'s]?|msc|m.sc', 5),  # Small penalty for Master's requirement
        ]

        for keyword, pattern, penalty in requirement_indicators:
            matches = re.findall(pattern, description)
            if matches:
                if keyword == "years" and matches:
                    # Check if any year requirement exceeds our threshold
                    for match in matches:
                        try:
                            years = int(match)
                            if years > 5:  # Threshold for excessive experience
                                score -= penalty
                                red_flags.append(f"Requires {years}+ years experience")
                                break
                        except:
                            pass
                else:
                    score -= penalty
                    red_flags.append(f"Contains {keyword} requirement")

        # Return result with explanation
        if score < self.relevancy_threshold:
            print(f"⚠️ Job relevancy score {score}/100 - below threshold ({self.relevancy_threshold})")
            print(f"Red flags: {', '.join(red_flags)}")
            return False
        else:
            print(f"✅ Job relevancy score {score}/100 - meets threshold ({self.relevancy_threshold})")
            return True

    def human_like_typing(self, element, text, clear_first=True):
        """Type text into an element with random delays between keystrokes to appear more human-like"""
        try:
            if not element or not element.is_displayed() or not element.is_enabled():
                print("Element is not valid, visible or enabled")
                return False

            # First clear the field if requested
            if clear_first:
                try:
                    # Use our centralized clear_input_field utility
                    self.clear_input_field(element)
                except Exception as e:
                    print(f"Error clearing field: {str(e)}")

            # Click on the element to ensure focus
            element.click()
            time.sleep(0.3)

            # NEW APPROACH: First type some random characters and delete them
            # This helps overcome some validation issues by simulating genuine user interaction
            if random.random() > 0.7:  # 30% chance to use this technique
                # Type 2-3 random characters
                random_chars = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(2))
                for char in random_chars:
                    element.send_keys(char)
                    time.sleep(0.1)

                # Delete those characters with backspace
                for _ in range(len(random_chars)):
                    element.send_keys(Keys.BACKSPACE)
                    time.sleep(0.1)

                time.sleep(0.3)  # Brief pause after clearing

            # Type each character with a random delay
            for char in text:
                element.send_keys(char)
                # Random delay between keystrokes (50ms to 200ms)
                time.sleep(0.05 + (random.random() * 0.15))



            # Small pause after typing
            time.sleep(0.5)

            # NEW: Occasionally press a cursor key and then go back to simulate user behavior
            if random.random() > 0.8:  # 20% chance
                element.send_keys(Keys.ARROW_RIGHT)
                time.sleep(0.2)
                element.send_keys(Keys.ARROW_LEFT)
                time.sleep(0.2)

            # Verify if the field actually took our value
            actual_value = element.get_attribute("value")
            if actual_value != text:
                print(f"Field validation may have failed. Expected '{text}', got '{actual_value}'")

                # Try a different approach - use JavaScript to set the value directly
                print("Using JavaScript to set value directly...")
                try:
                    self.driver.execute_script(f"arguments[0].value = '{text}';", element)

                    # Trigger input event to activate validation
                    self.driver.execute_script("""
                        var event = new Event('input', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                    """, element)

                    # Trigger change event to activate validation
                    self.driver.execute_script("""
                        var event = new Event('change', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                    """, element)

                    time.sleep(0.5)
                    actual_value = element.get_attribute("value")
                    print(f"After JavaScript injection: '{actual_value}'")
                except Exception as js_error:
                    print(f"JavaScript approach failed: {str(js_error)}")

                if actual_value != text:
                    # Last resort - try character by character with tab focus
                    element.click()
                    time.sleep(0.3)
                    element.clear()
                    time.sleep(0.2)

                    for char in text:
                        element.send_keys(char)
                        time.sleep(0.15)

                    # Tab out and back to trigger validation
                    element.send_keys(Keys.TAB)
                    time.sleep(0.3)
                    element.send_keys(Keys.SHIFT + Keys.TAB)
                    time.sleep(0.3)

            # Tab out to trigger validation
            element.send_keys(Keys.TAB)
            time.sleep(0.5)
            return True

        except Exception as e:
            print(f"Error during human-like typing: {str(e)}")
            # Fallback to direct send_keys if human typing fails
            try:
                element.clear()
                element.send_keys(text)
                element.send_keys(Keys.TAB)
                print("Used fallback typing method")
                return True
            except Exception as fallback_error:
                print(f"Fallback typing also failed: {str(fallback_error)}")
                return False

    def avoid_detection(self):
        """Perform human-like browsing behavior to avoid being detected as automation"""
        print("🔄 Taking anti-detection measures...")
        try:
            # Click the LinkedIn home icon
            home_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a[href='/feed/'], .global-nav__logo, a[data-test-global-nav-item='home']")

            if home_buttons:
                print("Navigating to LinkedIn feed...")
                home_buttons[0].click()
                time.sleep(3)

                # Scroll the feed randomly for a bit
                scroll_count = random.randint(3, 8)
                print(f"Scrolling through feed {scroll_count} times...")

                for i in range(scroll_count):
                    # Random scroll distance
                    scroll_amount = random.randint(300, 800)
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")

                    # Random pause between scrolls
                    pause_time = 1 + (random.random() * 3)
                    time.sleep(pause_time)

                    # Occasionally like a post (10% chance)
                    if random.random() < 0.1:
                        try:
                            # Find like buttons
                            like_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button.react-button__trigger, button[aria-label*='Like']")

                            if like_buttons:
                                # Choose a random like button
                                random_index = random.randint(0, min(len(like_buttons)-1, 5))
                                if random_index < len(like_buttons):
                                    print("Clicking a random like button...")
                                    like_buttons[random_index].click()
                                    time.sleep(1)
                        except:
                            pass

                # Random hovering
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, ".feed-shared-update-v2")
                    if elements:
                        random_element = random.choice(elements)
                        actions = webdriver.ActionChains(self.driver)
                        actions.move_to_element(random_element).perform()
                        time.sleep(1.5)
                except:
                    pass

                print("Returning to job search...")
                self.driver.back()  # Go back to job search
                time.sleep(2)
                return True
            else:
                print("Could not find home button")
                return False

        except Exception as e:
            print(f"Error during anti-detection measures: {str(e)}")
            return False

    def search_top_job_picks(self):
        """Navigate to LinkedIn's 'Top job picks for you' section"""
        print("Navigating to 'Top job picks for you'...")

        try:
            # Navigate to the jobs homepage
            self.driver.get("https://www.linkedin.com/jobs/")
            time.sleep(3)  # Wait for page to load

            # Look for the "Show all" button for top job picks
            try:
                print("Looking for 'Show all' button for top job picks...")
                
                # Multiple selectors to find the "Show all" button
                show_all_selectors = [
                    "a[aria-label*='Show all Top job picks']",
                    "a[href*='/jobs/collections/recommended']",
                    ".discovery-templates-jobs-home-vertical-list__footer",
                    "a[data-test-app-aware-link][href*='recommended']"
                ]
                
                show_all_button = None
                for selector in show_all_selectors:
                    try:
                        show_all_button = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                        )
                        print(f"Found 'Show all' button using selector: {selector}")
                        break
                    except:
                        continue

                if show_all_button:
                    # Click the "Show all" button
                    self.driver.execute_script("arguments[0].click();", show_all_button)
                    print("Clicked 'Show all' button for top job picks")
                    time.sleep(3)  # Wait for page to load
                else:
                    # If we can't find the button, try to navigate directly to the recommended jobs URL
                    print("Could not find 'Show all' button, navigating directly to recommended jobs...")
                    self.driver.get("https://www.linkedin.com/jobs/collections/recommended/?discover=recommended&discoveryOrigin=JOBS_HOME_JYMBII")
                    time.sleep(3)
                
            except Exception as e:
                print(f"Error finding 'Show all' button: {str(e)}")
                # Fallback to direct navigation
                print("Fallback: Navigating directly to recommended jobs...")
                self.driver.get("https://www.linkedin.com/jobs/collections/recommended/?discover=recommended&discoveryOrigin=JOBS_HOME_JYMBII")
                time.sleep(3)

            # Apply "Easy Apply" filter if available
            try:
                print("Applying 'Easy Apply' filter...")
                easy_apply_filter = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button#searchFilter_applyWithLinkedin"))
                )
                easy_apply_filter.click()
                print("Easy Apply filter applied successfully")
                time.sleep(2)
            except Exception as e:
                print(f"Easy Apply filter not found or failed to apply: {str(e)}")
                # Continue without filter - some pages might not have this option

            # Check if we successfully navigated to top job picks
            current_url = self.driver.current_url
            if "recommended" in current_url or "collections" in current_url:
                print("Successfully navigated to top job picks section")
                
                # Check for job count
                try:
                    job_count_elements = self.driver.find_elements(By.CSS_SELECTOR, ".jobs-search-results-list__subtitle, .artdeco-entity-lockup__subtitle")
                    if job_count_elements:
                        job_count_text = job_count_elements[0].text
                        print(f"Top job picks results: {job_count_text}")
                    else:
                        print("Top job picks loaded successfully")
                except Exception as e:
                    print(f"Error checking for job count: {e}")
                    print("Top job picks loaded successfully")
                
                return True
            else:
                print(f"Navigation may have failed - current URL: {current_url}")
                return False

        except Exception as e:
            print(f"Error navigating to top job picks: {str(e)}")
            return False

    def get_job_mode_selection(self):
        """Ask user to choose between normal search or top job picks"""
        print("\n" + "=" * 50)
        print("LinkedIn Easy Apply - Job Search Mode")
        print("=" * 50)
        print("1. Normal job search (with search terms)")
        print("2. Top job picks for you (LinkedIn recommendations)")
        print("=" * 50)
        
        while True:
            try:
                choice = input("Please select your preferred job search mode (1 or 2): ").strip()
                if choice == "1":
                    return "normal"
                elif choice == "2":
                    return "top_picks"
                else:
                    print("Invalid choice. Please enter 1 or 2.")
            except KeyboardInterrupt:
                print("\nOperation cancelled by user.")
                return None
            except Exception as e:
                print(f"Error getting user input: {str(e)}")
                return None

    def handle_blocking_dialogs(self):
        """Handle blocking dialogs with default 'Yes' responses"""
        try:
            # Check for modal dialogs and popups
            dialog_selectors = [
                ".artdeco-modal",
                "[role='dialog']",
                ".modal-dialog",
                ".confirmation-dialog",
                ".cookie-banner"
            ]
            
            for selector in dialog_selectors:
                dialogs = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for dialog in dialogs:
                    if dialog.is_displayed():
                        print(f"Found blocking dialog: {selector}")
                        
                        # Look for positive action buttons
                        button_selectors = [
                            "button:contains('Yes')", "button:contains('Accept')", 
                            "button:contains('Continue')", "button:contains('OK')", 
                            "button:contains('Agree')", ".artdeco-button--primary"
                        ]
                        
                        for btn_selector in button_selectors:
                            buttons = dialog.find_elements(By.CSS_SELECTOR, btn_selector)
                            for button in buttons:
                                if button.is_displayed() and button.is_enabled():
                                    button_text = button.text.lower()
                                    if any(word in button_text for word in ['yes', 'accept', 'continue', 'ok', 'agree']):
                                        print(f"Clicking positive button: {button.text}")
                                        button.click()
                                        time.sleep(1)
                                        return True
                        
                        # If no positive buttons, try close buttons
                        close_buttons = dialog.find_elements(By.CSS_SELECTOR, 
                            "button[aria-label*='close'], .artdeco-modal__dismiss")
                        for button in close_buttons:
                            if button.is_displayed():
                                print("Closing dialog")
                                button.click()
                                time.sleep(1)
                                return True
            return False
        except Exception as e:
            print(f"Error handling dialogs: {str(e)}")
            return False

    def get_element_label(self, element):
        """Get the label associated with a form element."""
        try:
            # Try to find a label by the 'for' attribute
            element_id = element.get_attribute("id")
            if element_id:
                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{element_id}']")
                if labels and labels[0].text.strip():
                    return labels[0].text.strip()

            # If no 'for' attribute, try to find a parent label
            parent_label = element.find_element(By.XPATH, "./ancestor::label")
            if parent_label and parent_label.text.strip():
                return parent_label.text.strip()

            # If no parent, look for a label in the parent div
            parent_div = element.find_element(By.XPATH, "./ancestor::div[1]")
            labels_in_div = parent_div.find_elements(By.TAG_NAME, "label")
            if labels_in_div and labels_in_div[0].text.strip():
                return labels_in_div[0].text.strip()
                
            # Look for aria-label as a fallback
            aria_label = element.get_attribute("aria-label")
            if aria_label:
                return aria_label.strip()

        except Exception:
            pass  # Ignore if we can't find a label

        return "" # Return empty string if no label is found

    def clear_input_field(self, element):
        """Clear the input field robustly."""
        try:
            element.clear()
        except Exception:
            # Fallback to sending backspace
            try:
                value = element.get_attribute('value')
                if value:
                    element.send_keys(Keys.CONTROL + 'a')
                    element.send_keys(Keys.BACKSPACE)
            except Exception as e:
                print(f"Could not clear field: {e}")
