#!/usr/bin/env python
"""
LinkedIn Easy Apply Automation
This script automates the process of searching for jobs on LinkedIn and applying to those
that have "Easy Apply" buttons.
"""

import os
import time
import sys
import json
import re
import requests
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException, StaleElementReferenceException
from linkedin_scraper import actions

# API endpoint for question answering
API_URL = "http://*************:10000/generate"

class LinkedInEasyApply:
    """Automates LinkedIn Easy Apply process"""

    def __init__(self, headless=False, credentials=None):
        """Initialize the Easy Apply automation with browser settings"""
        self.driver = None
        self.headless = headless
        self.credentials = credentials or {}
        self.jobs_applied = 0
        self.jobs_skipped = 0
        self.current_page = 1
        self.max_pages = 40  # LinkedIn typically shows up to 40 pages
        self.max_retries = 3  # Maximum number of retries for fixing form errors
        self.default_answers = {
            "years_of_experience": "1",
            "education_bachelors": "Yes",
            "willing_to_relocate": "Yes",
            "citizenship_status": "Yes, I am authorized to work in this country for any employer",
            "require_sponsorship": "No",
            "can_commute": "Yes"
        }
        # NEW: Track application step count for intelligent handling
        self.current_step = 0
        # NEW: Track jobs that were applied to with details for analytics
        self.applied_jobs_details = []
        # NEW: Set the number of initial steps to auto-progress
        self.auto_progress_steps = 2  # Auto-progress through first 2 steps
        # NEW: Set threshold score for job relevancy (0-100)
        self.relevancy_threshold = 70
        # NEW: Flag to enable/disable intelligent skipping
        self.use_intelligent_skip = True
        # NEW: Job application history to avoid duplicate applications
        self.job_history = set()

    def setup(self):
        """Initialize WebDriver with optimal settings"""
        print("Initializing Chrome WebDriver...")
        chrome_options = Options()

        # Add standard options
        if self.headless:
            chrome_options.add_argument("--headless=new")
        #chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")

        # Suppress WebRTC errors and other browser logging
        chrome_options.add_argument("--log-level=3")  # Only show fatal errors
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-web-security")

        # Disable WebRTC to prevent STUN server connection errors
        chrome_options.add_argument("--disable-webrtc")

        # Suppress console errors
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(5)
            print("Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            print(f"Error initializing WebDriver: {str(e)}")
            return False

    def login(self):
        """Authenticate with LinkedIn"""
        if not self.driver:
            print("Cannot login: WebDriver not initialized")
            return False

        print("Logging in to LinkedIn...")
        try:
            email = self.credentials.get('email', os.environ.get('LINKEDIN_EMAIL', None))
            password = self.credentials.get('password', os.environ.get('LINKEDIN_PASSWORD', None))

            if not email or not password:
                print("No credentials provided. Prompting for input...")
                email = input("Enter LinkedIn email: ").strip()
                password = input("Enter LinkedIn password: ").strip()

            self.driver.get("https://www.linkedin.com/login")
            time.sleep(1)  # Reduced from 2 to 1 - Allow page to load

            # Check if already logged in
            if "feed" in self.driver.current_url:
                print("Already logged in")
                return True

            print("Entering credentials...")
            actions.login(self.driver, email, password)
            time.sleep(1.5)  # Reduced from 3 to 1.5 - Allow login to complete

            # Verify successful login
            if "feed" in self.driver.current_url:
                print("Login successful")
                return True
            else:
                print("Login failed - redirected to: " + self.driver.current_url)
                return False

        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return False

    def search_jobs(self, search_term="data analyst", location=""):
        """Search for jobs with the given search term and location"""
        print(f"Searching for '{search_term}' jobs...")

        try:
            # Construct the search URL
            base_url = "https://www.linkedin.com/jobs/search/"
            query_params = f"?keywords={search_term.replace(' ', '%20')}"
            if location:
                query_params += f"&location={location.replace(' ', '%20')}"
            search_url = base_url + query_params

            # Navigate to the search URL
            self.driver.get(search_url)
            time.sleep(2)  # Reduced from 3 to 2 - Wait for page to load

            # Apply "Easy Apply" filter
            try:
                print("Applying 'Easy Apply' filter...")
                # Wait until the Easy Apply filter is clickable
                easy_apply_filter = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button#searchFilter_applyWithLinkedin"))
                )

                # Click it
                easy_apply_filter.click()
                print("Easy Apply filter applied successfully")
                time.sleep(2)  # Wait for filtered results to load
            except Exception as e:
                print(f"Error applying Easy Apply filter: {str(e)}")
                # Continue with search even if filter fails

            # Check if search was successful
            job_count_elements = self.driver.find_elements(By.CSS_SELECTOR, ".jobs-search-results-list__subtitle")
            if job_count_elements:
                job_count_text = job_count_elements[0].text
                print(f"Search results: {job_count_text}")
            else:
                print("Search completed but couldn't determine job count")

            return True

        except Exception as e:
            print(f"Error searching for jobs: {str(e)}")
            return False

    def find_job_cards_on_page(self):
        """Find all job cards on the current page, with support for incremental loading"""
        print(f"Finding job cards on page {self.current_page}...")

        try:
            # Wait for job cards to load
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, ".job-card-container")))

            # Find job cards using multiple selectors for reliability
            selectors = [
                ".job-card-container",
                ".jobs-search-results__list-item",
                "[data-job-id]"
            ]

            # Initialize variables for incremental loading
            job_cards = []
            previous_count = 0
            max_scroll_attempts = 10
            scroll_attempts = 0

            # Get initial job cards
            for selector in selectors:
                cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if cards and len(cards) > 0:
                    job_cards = cards
                    previous_count = len(job_cards)
                    print(f"Initially found {previous_count} job cards using selector: {selector}")
                    break

            # Incremental scrolling to load all job cards
            while scroll_attempts < max_scroll_attempts:
                # If we have job cards, scroll to the last one to trigger loading more
                if job_cards:
                    try:
                        last_card = job_cards[-1]
                        print(f"Scrolling to job card #{len(job_cards)} to load more...")
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", last_card)

                        # Scroll a bit more to trigger loading
                        self.driver.execute_script("window.scrollBy(0, 300);")
                        time.sleep(2)  # Wait for more cards to load
                    except Exception as e:
                        print(f"Error scrolling to last card: {str(e)}")

                # Re-find job cards to see if more have loaded
                for selector in selectors:
                    cards = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if cards and len(cards) > 0:
                        job_cards = cards
                        current_count = len(job_cards)
                        print(f"Now found {current_count} job cards (previously {previous_count})")
                        break

                # If we didn't find more cards, we've likely reached the end
                if len(job_cards) <= previous_count:
                    scroll_attempts += 1
                    print(f"No new cards found. Attempt {scroll_attempts}/{max_scroll_attempts}")

                    # Try scrolling a bit more to ensure we've loaded everything
                    self.driver.execute_script("window.scrollBy(0, 500);")
                    time.sleep(1.5)
                else:
                    # Reset scroll attempts if we found more cards
                    scroll_attempts = 0
                    previous_count = len(job_cards)

                    # If we've found a significant number of cards, we can stop
                    if len(job_cards) >= 25:  # LinkedIn typically shows 25 jobs per page
                        print(f"Found {len(job_cards)} job cards, which is enough to process")
                        break

            # Scroll back to top to start processing from the first job
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            if not job_cards:
                print("No job cards found on this page")
            else:
                print(f"Final count: {len(job_cards)} job cards found and ready to process")

            return job_cards

        except TimeoutException:
            print("Timeout waiting for job cards to load")
            return []
        except Exception as e:
            print(f"Error finding job cards: {str(e)}")
            return []

    def handle_additional_questions(self):
        """Handle additional questions that appear during the application process"""
        print("Checking for additional questions...")
        try:
            # Wait for questions to appear (if any)
            wait = WebDriverWait(self.driver, 3)

            # Look for the additional questions section using multiple selectors
            questions_section = None
            selectors = [
                "div[data-test-form-element]",
                ".jobs-easy-apply-form-section",
                ".fb-dash-form-element"
            ]

            for selector in selectors:
                try:
                    questions_section = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
                    if questions_section:
                        print(f"Found questions section with selector: {selector}")
                        break
                except:
                    continue

            if not questions_section:
                print("No questions section found")
                return True

            # Track processed questions to avoid duplicates
            processed_questions = set()
            processed_inputs = set()

            # First, try to get all form elements using the specific modal content selector
            try:
                form_content = self.driver.find_element(By.CSS_SELECTOR, ".artdeco-modal__content.jobs-easy-apply-modal__content.p0.ember-view")
                print("Found form content using primary selector")
            except NoSuchElementException:
                # Fallback selectors if the specific one doesn't work
                try:
                    # Check for the specific ID in the HTML
                    form_content = self.driver.find_element(By.ID, "ember393")
                    print("Found form content using ID selector")
                except NoSuchElementException:
                    try:
                        # Try alternative selectors based on the form structure
                        selectors = [
                            ".artdeco-modal__content form",
                            ".jobs-easy-apply-modal__content",
                            ".bUxAKmEDcdaKWzLwwuoOhRjpvTRFAtxc",  # Main form container from HTML
                            "form:has(.fb-dash-form-element)",     # Form containing form elements
                            "div[class*='artdeco-modal__content']",
                            "div[id^='ember'] form"
                        ]
                        for selector in selectors:
                            try:
                                form_content = self.driver.find_element(By.CSS_SELECTOR, selector)
                                print(f"Found form content using fallback selector: {selector}")
                                break
                            except:
                                continue
                        else:
                            # If we get here, none of the selectors worked
                            print("Could not find form content with any selector")
                            form_content = self.driver
                    except:
                        print("Using driver as fallback for form content")
                        form_content = self.driver

            # Process all text inputs and selects in one go
            try:
                # Find all form elements
                all_text_inputs = form_content.find_elements(By.CSS_SELECTOR, "input.artdeco-text-input--input, input[type='text'], input[type='number']")
                all_selects = form_content.find_elements(By.CSS_SELECTOR, "select")
                all_radios = form_content.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                all_checkboxes = form_content.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")

                print(f"Found {len(all_text_inputs)} text inputs, {len(all_selects)} dropdowns, {len(all_radios)} radio buttons, and {len(all_checkboxes)} checkboxes")

                # Process all text inputs first
                for text_input in all_text_inputs:
                    try:
                        # Skip if we've already processed this input element
                        try:
                            input_id = text_input.get_attribute("id") or text_input.get_attribute("name")
                            input_value = text_input.get_attribute("value")

                            # If the input already has a value or we've processed this ID, skip it
                            if (input_id and input_id in processed_inputs) or (input_value and input_value.strip()):
                                print(f"Skipping input with ID {input_id}, already has value: {input_value}")
                                continue

                            if input_id:
                                processed_inputs.add(input_id)
                        except:
                            pass

                        # Get the question text using the centralized utility function
                        question_text = self.get_element_label(text_input)

                        # Clean up the question text
                        clean_question = question_text.strip()
                        if "?" in clean_question:
                            parts = clean_question.split("?")
                            if len(parts) > 1 and parts[0].strip() in parts[1]:
                                clean_question = parts[0].strip() + "?"

                        # Check if we've already processed this question
                        duplicate = False
                        for processed_q in processed_questions:
                            if (clean_question in processed_q or processed_q in clean_question) and clean_question != "Unknown question":
                                print(f"Skipping duplicate question: '{clean_question}'")
                                duplicate = True
                                break

                        if duplicate:
                            continue

                        # Skip "Unknown question" after the first occurrence
                        if clean_question == "Unknown question" and "Unknown question" in processed_questions:
                            continue

                        # Add to processed questions
                        processed_questions.add(clean_question)

                        print(f"Processing text input: '{clean_question}'")

                        # Use the classify_question utility function to get appropriate answer
                        question_info = self.classify_question(clean_question)

                        # Handle special case for location fields which need dropdown interaction
                        if question_info["type"] == "location":
                            self.handle_location_dropdown(text_input, question_info["value"])
                            continue

                        # For all other types, use our fill_input_field utility
                        self.fill_input_field(text_input, question_info["value"])

                    except Exception as e:
                        print(f"Error processing text input: {str(e)}")

                # Process all dropdowns using the dropdown handler utility
                for select_element in all_selects:
                    try:
                        # Skip if already processed
                        try:
                            select_id = select_element.get_attribute("id") or select_element.get_attribute("name")
                            if select_id and select_id in processed_inputs:
                                continue
                            if select_id:
                                processed_inputs.add(select_id)
                        except:
                            pass

                        # Get question text using our utility function
                        question_text = self.get_element_label(select_element)

                        # Check for duplicates
                        clean_question = question_text.strip()
                        duplicate = False
                        for processed_q in processed_questions:
                            if clean_question in processed_q or processed_q in clean_question:
                                duplicate = True
                                break

                        if duplicate:
                            continue

                        processed_questions.add(clean_question)

                        print(f"Processing dropdown: '{clean_question}'")

                        # Use our dropdown handler utility
                        self.handle_select_dropdown(select_element, clean_question)

                    except Exception as e:
                        print(f"Error processing dropdown: {str(e)}")

                # Process all radio button groups
                if all_radios:
                    # Group radio buttons by name attribute
                    radio_groups = {}
                    for radio in all_radios:
                        try:
                            radio_name = radio.get_attribute("name")
                            if radio_name:
                                if radio_name not in radio_groups:
                                    radio_groups[radio_name] = []
                                radio_groups[radio_name].append(radio)
                        except:
                            pass

                    # Process each radio button group
                    for group_name, radios in radio_groups.items():
                        if group_name in processed_inputs:
                            continue

                        processed_inputs.add(group_name)

                        # Get the question text for this radio group
                        question_text = "Unknown question"
                        try:
                            # Try to get parent container that holds both the question and radios
                            parent = self.driver.execute_script("return arguments[0].parentElement.parentElement;", radios[0])
                            if parent:
                                parent_text = parent.text.strip()
                                if parent_text:
                                    question_text = parent_text.split('\n')[0].strip()
                        except:
                            pass

                        # Use our radio button handler
                        self.handle_radio_buttons(radios, question_text)

                # Process all checkboxes
                if all_checkboxes:
                    # Group checkboxes by name or parent container
                    checkbox_groups = {}
                    for checkbox in all_checkboxes:
                        try:
                            checkbox_name = checkbox.get_attribute("name")
                            group_key = checkbox_name if checkbox_name else "unnamed_checkboxes"
                            if group_key not in checkbox_groups:
                                checkbox_groups[group_key] = []
                            checkbox_groups[group_key].append(checkbox)
                        except:
                            pass

                    # Process each checkbox group
                    for group_name, checkboxes in checkbox_groups.items():
                        if group_name in processed_inputs:
                            continue

                        processed_inputs.add(group_name)

                        # Get the question text for this checkbox group
                        question_text = "Unknown question"
                        try:
                            # Try to find the parent container for this group
                            parent = self.driver.execute_script("return arguments[0].parentElement.parentElement;", checkboxes[0])
                            if parent:
                                parent_text = parent.text.strip()
                                if parent_text:
                                    question_lines = parent_text.split('\n')
                                    question_text = question_lines[0].strip()
                        except:
                            pass

                        # Use our checkbox handler
                        self.handle_checkboxes(checkboxes, question_text)

                # Wait briefly after processing form elements
                time.sleep(1)

                # Check for form errors to see if we need to retry
                self.check_for_form_errors()

            except Exception as e:
                print(f"Error during form processing: {str(e)}")

            print("Completed filling additional questions")
            return True

        except Exception as e:
            print(f"Error handling additional questions: {str(e)}")
            # Try to recover from stale element reference errors
            try:
                time.sleep(2)  # Wait a bit for page to stabilize
                self.driver.refresh()  # As a last resort, refresh the page
                time.sleep(3)
            except:
                pass
            return False

    def get_answer_for_question(self, question, options=None, input_type=None):
        """Get an answer for a given question from the API or default answers"""
        print(f"Getting answer for: '{question}'")

        # Check default answers first based on keywords
        lower_question = question.lower()

        # Check if we should skip processing for questions already handled in handle_additional_questions
        if any(key in lower_question for key in ["notice period", "current ctc", "current salary", "expected ctc", "expected salary"]):
            # Just return the previous answer without printing or processing again
            if "notice period" in lower_question:
                return "15"  # 15 days for notice period questions
            elif "current ctc" in lower_question or "current salary" in lower_question:
                return "3"   # 3 for current CTC questions
            elif "expected ctc" in lower_question or "expected salary" in lower_question:
                return "9"   # 9 for expected CTC questions

        # Also avoid duplicate handling for experience-related questions
        elif any(phrase in lower_question for phrase in ["years of experience", "work experience", "experience with", "experience in"]):
            return "1"  # Return the same value used in handle_additional_questions for consistency

        # Handle numeric input specially - default to "1" for all numeric questions
        if input_type == "numeric" or any(word in lower_question for word in ["how many", "how much", "years", "month", "number"]):
            return "1"  # Default for other numeric questions

        # Match question keywords with default answers
        if any(keyword in lower_question for keyword in ["years", "experience"]):
            return "1"
        elif any(keyword in lower_question for keyword in ["bachelor", "education", "degree"]):
            return "Yes"
        elif any(keyword in lower_question for keyword in ["relocate", "relocation"]):
            return "Yes"
        elif any(keyword in lower_question for keyword in ["authorized", "citizenship", "legally"]):
            return self.default_answers["citizenship_status"]
        elif any(keyword in lower_question for keyword in ["sponsor", "sponsorship"]):
            return "No"
        elif any(keyword in lower_question for keyword in ["commute", "travel", "remote"]):
            return "Yes"

        # If options are provided and one of them is "Yes", default to that
        if options and "Yes" in options:
            return "Yes"

        try:
            # Prepare query for API
            query = {
                "message": question
            }

            if options:
                query["message"] += f"\nOptions: {', '.join(options)}\nChoose one of the options."

            # Call API
            response = requests.post(API_URL, json=query, timeout=100)

            if response.status_code == 200:
                api_response = response.json()
                if "response" in api_response and "content" in api_response["response"]:
                    answer = api_response["response"]["content"]
                    print(f"API answered: {answer}")

                    # If we have options, try to match the answer to one of them
                    if options:
                        for option in options:
                            if option.lower() in answer.lower():
                                return option
                        # Default to Yes or first option if no match
                        if "Yes" in options:
                            return "Yes"
                        return options[0]

                    # If numeric input is needed, try to extract a number
                    if input_type == "numeric":
                        # Try to extract a numeric value from the API response
                        numbers = re.findall(r'\d+(?:\.\d+)?', answer)
                        if numbers:
                            return numbers[0]
                        return "5"  # Default numeric answer

                    return answer

            # If API fails or doesn't give useful answer, use defaults
            print("Using default answer")
            if input_type == "numeric" or "years" in lower_question or "experience" in lower_question:
                # For numeric questions
                return "5"
            else:
                # For yes/no questions default to yes
                return "Yes"

        except Exception as e:
            print(f"Error getting answer from API: {str(e)}")
            return "Yes" if "?" in question else "5"  # Default fallbacks

    def check_for_form_errors(self):
        """Check for form validation errors and try to fix them"""
        print("Checking for form errors...")
        try:
            # Look for error messages
            error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")

            if not error_messages:
                print("No form errors found")
                return True

            error_count = 0
            for error_index, error in enumerate(error_messages):
                try:
                    # Refresh error elements to avoid stale references
                    if error_index > 0:
                        # Re-find all error messages to avoid stale reference errors
                        error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
                        if error_index >= len(error_messages):
                            continue
                        error = error_messages[error_index]

                    error_text = error.text.strip()
                    if not error_text:
                        continue

                    error_count += 1
                    print(f"Found form error: '{error_text}'")

                    # Find the input field related to this error
                    parent_element = error
                    related_input = None
                    related_select = None
                    related_radio = None

                    # Go up several levels to find the form group container
                    for _ in range(5):
                        try:
                            parent_element = parent_element.find_element(By.XPATH, "./..")
                            # Check for input fields in this parent
                            inputs = parent_element.find_elements(By.CSS_SELECTOR, "input[type='text'], input[type='number']")
                            if inputs:
                                related_input = inputs[0]
                                break

                            # Check for select elements
                            selects = parent_element.find_elements(By.CSS_SELECTOR, "select")
                            if selects:
                                related_select = selects[0]
                                break

                            # Check for radio buttons
                            radios = parent_element.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                            if radios:
                                related_radio = radios
                                break
                        except Exception as e:
                            print(f"Error finding related form element: {str(e)}")
                            break

                    # Analyze parent text to determine context of the field
                    field_context = ""
                    try:
                        if parent_element:
                            parent_text = parent_element.text.lower()
                            field_context = parent_text
                    except:
                        pass

                    # Handle different types of errors
                    if "whole number" in error_text.lower():
                        # Handle whole number field errors (integers, not decimals)
                        if related_input:
                            # Get a fresh reference to the input to avoid stale element
                            input_id = related_input.get_attribute("id")
                            if input_id:
                                try:
                                    # Re-find the element to avoid stale reference
                                    related_input = self.driver.find_element(By.ID, input_id)
                                except:
                                    # If can't find by ID, try to find by XPath relative to error
                                    try:
                                        related_input = error.find_element(By.XPATH, "../..//input")
                                    except:
                                        print("Could not refresh stale input element")
                                        continue

                            try:
                                related_input.clear()
                                time.sleep(0.5)
                            except Exception as e:
                                print(f"Could not clear input: {str(e)}")

                            # Extract the required range if present
                            range_match = re.search(r'larger than (\d+)', error_text)
                            if range_match:
                                # Use appropriate values based on the threshold and field context
                                min_val = int(range_match.group(1))

                                # Check field context to determine appropriate value
                                if "ctc" in field_context.lower() or "compensation" in field_context.lower() or "salary" in field_context.lower():
                                    if "current" in field_context.lower():
                                        value = "300000"  # Current CTC
                                        print(f"Current CTC field detected, using value: {value}")
                                    elif "expected" in field_context.lower():
                                        value = "900000"  # Expected CTC
                                        print(f"Expected CTC field detected, using value: {value}")
                                    else:
                                        value = "500000"  # Generic CTC value
                                        print(f"Generic CTC field detected, using value: {value}")
                                else:
                                    # For other fields requiring a minimum value
                                    value = str(min_val + 10)  # Use threshold + 10 to be safe
                                    print(f"Range constraint detected, using value {min_val}+10: {value}")
                            else:
                                # For fields with no explicit threshold
                                if "notice period" in field_context.lower():
                                    value = "15"  # Notice period in days
                                    print(f"Notice period field detected, using value: {value}")
                                elif any(term in field_context.lower() for term in ["experience", "year"]):
                                    value = "1"  # Always use 1 for experience
                                    print(f"Experience field detected, using value: {value}")
                                else:
                                    value = "1"  # Default to 1 for other fields

                            # Make sure field is empty first
                            try:
                                self.driver.execute_script("arguments[0].value = '';", related_input)
                                time.sleep(0.3)
                            except:
                                pass

                            try:
                                self.driver.execute_script("arguments[0].value = arguments[1];", related_input, value)
                                time.sleep(0.3)
                                related_input.send_keys(Keys.TAB)  # Trigger validation
                                print(f"Fixed whole number field with value: {value}")
                            except Exception as e:
                                print(f"Error setting whole number value: {str(e)}")
                                try:
                                    # Fallback to direct sending keys
                                    related_input.send_keys(value)
                                    related_input.send_keys(Keys.TAB)
                                    print("Used fallback method for whole number")
                                except:
                                    pass
                    elif "decimal" in error_text.lower() or "number" in error_text.lower():
                        # Handle numeric field errors for decimal values
                        if related_input:
                            # Get a fresh reference to the input to avoid stale element
                            input_id = related_input.get_attribute("id")
                            if input_id:
                                try:
                                    # Re-find the element to avoid stale reference
                                    related_input = self.driver.find_element(By.ID, input_id)
                                except:
                                    # If can't find by ID, try to find by XPath relative to error
                                    try:
                                        related_input = error.find_element(By.XPATH, "../..//input")
                                    except:
                                        print("Could not refresh stale input element")
                                        continue

                            try:
                                related_input.clear()
                                time.sleep(0.5)
                            except Exception as e:
                                print(f"Could not clear input: {str(e)}")

                            # Extract the minimum required value if present in error message
                            min_value_match = re.search(r'larger than (\d+\.?\d*)', error_text)
                            min_value = 0.1  # Default minimum for decimal fields

                            if min_value_match:
                                try:
                                    min_value = float(min_value_match.group(1))
                                    print(f"Detected minimum value requirement: {min_value}")
                                    # Add a small amount to ensure we're above the minimum
                                    min_value = round(min_value + 0.1, 1)
                                except:
                                    pass

                            # Check for specific field types to use appropriate values
                            if "notice period" in field_context or "joining time" in field_context:
                                value = "15.0"  # Always use 15.0 for notice period
                            elif any(term in field_context for term in ["salary", "ctc", "compensation", "package", "lpa"]):
                                if any(term in field_context for term in ["current", "present"]):
                                    value = "300000.0"  # Current compensation
                                elif any(term in field_context for term in ["expected", "desired"]):
                                    value = "900000.0"  # Expected compensation
                                else:
                                    value = "500000.0"  # Generic compensation value
                            elif any(term in field_context for term in ["experience", "year"]):
                                value = "1.0"  # Always use 1.0 for experience
                            else:
                                # For other decimal fields, use the calculated minimum value
                                value = str(min_value)

                            # Make sure field is empty first
                            try:
                                self.driver.execute_script("arguments[0].value = '';", related_input)
                                time.sleep(0.3)
                            except:
                                pass

                            try:
                                self.driver.execute_script("arguments[0].value = arguments[1];", related_input, value)
                                time.sleep(0.3)
                                related_input.send_keys(Keys.TAB)  # Trigger validation
                                print(f"Fixed decimal field with value: {value}")
                            except Exception as e:
                                print(f"Error setting decimal value: {str(e)}")
                                try:
                                    # Fallback to direct sending keys
                                    related_input.send_keys(value)
                                    related_input.send_keys(Keys.TAB)
                                    print("Used fallback method for decimal")
                                except:
                                    pass

                    elif "select" in error_text.lower() or "choose" in error_text.lower():
                        # Handle select/dropdown errors
                        if related_select:
                            select = Select(related_select)
                            options = related_select.find_elements(By.CSS_SELECTOR, "option")
                            if len(options) > 1:  # Skip first option which is usually the placeholder
                                select.select_by_index(1)
                                print(f"Fixed select field error by choosing first valid option")

                    elif "required" in error_text.lower() or "fill" in error_text.lower():
                        # Handle required field errors
                        if related_input:
                            # Get a fresh reference to avoid stale element
                            input_id = related_input.get_attribute("id")
                            if input_id:
                                try:
                                    related_input = self.driver.find_element(By.ID, input_id)
                                except:
                                    try:
                                        related_input = error.find_element(By.XPATH, "../..//input")
                                    except:
                                        print("Could not refresh stale input element")
                                        continue

                            try:
                                related_input.clear()
                                time.sleep(0.5)
                            except Exception as e:
                                print(f"Could not clear input: {str(e)}")

                            # Use JavaScript to ensure the field is completely cleared
                            try:
                                self.driver.execute_script("arguments[0].value = '';", related_input)
                                time.sleep(0.3)
                            except:
                                pass

                            # Check context and provide appropriate answer
                            if "notice" in field_context and "period" in field_context:
                                value = "15.0"  # Notice period in days
                                print(f"Notice period field detected, using value: {value}")
                            elif any(term in field_context for term in ["experience", "year"]):
                                value = "1.0"  # Always use 1.0 for experience
                                print(f"Experience field detected, using value: {value}")
                            elif any(term in field_context for term in ["salary", "ctc", "compensation"]):
                                if "current" in field_context or "present" in field_context:
                                    value = "300000.0"  # Current compensation
                                elif "expected" in field_context or "desired" in field_context:
                                    value = "900000.0"  # Expected compensation
                                else:
                                    value = "500000.0"  # Generic compensation value
                                print(f"Compensation field detected, using value: {value}")
                            else:
                                value = "Yes"  # Default text value
                                print(f"Generic text field detected, using value: {value}")

                            try:
                                self.driver.execute_script("arguments[0].value = arguments[1];", related_input, value)
                                time.sleep(0.3)
                                related_input.send_keys(Keys.TAB)  # Trigger validation
                                print(f"Fixed required field with value: {value}")
                            except Exception as e:
                                print(f"Error setting value: {str(e)}")
                                try:
                                    # Fallback to direct sending keys
                                    related_input.send_keys(value)
                                    related_input.send_keys(Keys.TAB)
                                    print("Used fallback method")
                                except:
                                    pass
                        elif related_select:
                            select = Select(related_select)
                            options = related_select.find_elements(By.CSS_SELECTOR, "option")
                            if len(options) > 1:
                                select.select_by_index(1)
                                print(f"Fixed required select error by choosing first valid option")
                        elif related_radio and len(related_radio) > 0:
                            related_radio[0].click()
                            print(f"Fixed required radio error by selecting first option")

                except Exception as e:
                    print(f"Error fixing form error: {str(e)}")

                # Brief pause between fixing errors to let the form update
                time.sleep(0.5)

            # Wait for form to update before checking for errors again
            time.sleep(1.5)

            # Re-check for errors after fixing them
            remaining_errors = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
            remaining_errors = [e for e in remaining_errors if e.text.strip() and "applied" not in e.text.lower()]

            print(f"Found and attempted to fix {error_count} errors")
            return len(remaining_errors) == 0  # Return True only if no errors remain

        except Exception as e:
            print(f"Error checking for form errors: {str(e)}")
            return False

    def proceed_with_application(self, max_steps=10):
        """Navigate through the application process by clicking next/continue/review/submit buttons"""
        print("Proceeding with application...")

        steps_taken = 0
        retry_count = 0
        max_retries = 3
        auto_progress_steps_completed = 0  # Track how many steps we've auto-progressed through

        while steps_taken < max_steps and retry_count < max_retries:
            try:
                # NEW: Check if we should intelligently skip question handling for this step
                auto_progress = False

                # First two steps are always auto-progressed (typically contact info and basic info)
                if auto_progress_steps_completed < self.auto_progress_steps:
                    auto_progress = True
                    print(f"🚀 Auto-progressing step {auto_progress_steps_completed+1} without question handling")
                else:
                    # After the initial auto-progress steps, use the standard intelligent logic
                    auto_progress = self.should_auto_progress()

                # Look for "Applied X seconds ago" messages which are success indicators
                try:
                    applied_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message")
                    for msg in applied_messages:
                        if "applied" in msg.text.lower():
                            print(f"✅ Success: {msg.text}")
                            # Look for close button or done button to dismiss
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn], .artdeco-button--primary")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close button for 'already applied' message")
                                    close.click()
                                    time.sleep(1)
                                    return True
                            return True
                except Exception as e:
                    print(f"Error checking for 'Applied' messages: {str(e)}")

                # Look for application sent/success popup first
                try:
                    # Check for "Application sent" dialog header
                    success_headers = self.driver.find_elements(By.CSS_SELECTOR,
                        ".artdeco-modal__header h2, h2#post-apply-modal, .jpac-modal-header")

                    for header in success_headers:
                        if header.is_displayed() and any(text in header.text.lower() for text in ["application sent", "success", "applied"]):
                            print("Found application sent confirmation dialog")

                            # Look for "Done" button in the modal
                            done_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__actionbar .artdeco-button--primary, #ember554, button[data-test-dialog-primary-btn], .artdeco-button--primary")

                            for btn in done_buttons:
                                if btn.is_displayed() and "done" in btn.text.lower():
                                    print("Clicking 'Done' button on success dialog")
                                    btn.click()
                                    time.sleep(2)
                                    return True

                            # If no Done button found, try to close the modal with the X button
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn]")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close (X) button on success dialog")
                                    close.click()
                                    time.sleep(2)
                                    return True
                except Exception as e:
                    print(f"Error checking for success dialog: {str(e)}")

                # Check for form errors - if there are errors, we'll handle questions
                has_errors = False
                try:
                    # Only check for errors if not auto-progressing
                    if not auto_progress:
                        error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
                        has_errors = any(error for error in error_messages if error.text.strip() and "applied" not in error.text.lower())
                        if has_errors:
                            print("Form errors detected - handling additional questions")
                            self.handle_additional_questions()
                            # Validate errors were fixed
                            error_check_result = self.check_for_form_errors()

                            # If errors remain, we'll stop auto-progressing and handle manually
                            if not error_check_result:
                                print("⚠️ Errors remain after handling questions - switching to manual mode")
                                auto_progress = False

                except Exception as e:
                    print(f"Error checking for form errors: {str(e)}")

                # Detect if we're on a question/review screen
                is_question_screen = False
                try:
                    # Only check for question screen if not auto-progressing
                    if not auto_progress:
                        # Check for common question screen indicators
                        question_indicators = [
                            "div[data-test-form-element]",
                            ".jobs-easy-apply-form-section",
                            ".fb-dash-form-element",
                            "input.artdeco-text-input--input:not([value])",  # Empty inputs needing values
                            "select:not(:disabled)",  # Enabled dropdowns
                            "[data-test-text-entity-list-form-component]"  # Forms
                        ]

                        for indicator in question_indicators:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and len([e for e in elements if e.is_displayed()]) > 0:
                                is_question_screen = True
                                break
                except Exception as e:
                    print(f"Error checking for question screen: {str(e)}")

                # Try multiple selectors for next/continue/review/submit buttons
                button_selectors = [
                    "button[aria-label='Review your application']",
                    "button[aria-label='Review']",
                    "button[aria-label='Submit application']",
                    "button[aria-label='Continue to next step']",
                    "button[aria-label='Next']",
                    "button[data-easy-apply-next-button]",
                    "button[aria-label='Submit']",
                    "button[aria-label='Done']",
                    "footer button.artdeco-button--primary",
                    "button.artdeco-button--primary",
                    ".artdeco-modal__footer button.artdeco-button--primary",
                    ".artdeco-modal__actionbar button.artdeco-button--primary",
                    "[data-control-name='continue_unify']",
                    "[data-control-name='submit_unify']",
                    "#ember554"  # Specific ID from your example
                ]

                # Look for a button to click
                button_found = False

            except Exception as e:
                print(f"Error during application process: {str(e)}")
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Reached maximum retries ({max_retries}) due to errors")
                    return False
                time.sleep(2)
                continue

    def proceed_with_application(self, max_steps=10):
        """Navigate through the application process by clicking next/continue/review/submit buttons"""
        print("Proceeding with application...")

        steps_taken = 0
        retry_count = 0
        max_retries = 3
        auto_progress_steps_completed = 0  # Track how many steps we've auto-progressed through

        while steps_taken < max_steps and retry_count < max_retries:
            try:
                # NEW: Check if we should intelligently skip question handling for this step
                auto_progress = False

                # First two steps are always auto-progressed (typically contact info and basic info)
                if auto_progress_steps_completed < self.auto_progress_steps:
                    auto_progress = True
                    print(f"🚀 Auto-progressing step {auto_progress_steps_completed+1} without question handling")
                else:
                    # After the initial auto-progress steps, use the standard intelligent logic
                    auto_progress = self.should_auto_progress()

                # Look for "Applied X seconds ago" messages which are success indicators
                try:
                    applied_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message")
                    for msg in applied_messages:
                        if "applied" in msg.text.lower():
                            print(f"✅ Success: {msg.text}")
                            # Look for close button or done button to dismiss
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn], .artdeco-button--primary")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close button for 'already applied' message")
                                    close.click()
                                    time.sleep(1)
                                    return True
                            return True
                except Exception as e:
                    print(f"Error checking for 'Applied' messages: {str(e)}")

                # Look for application sent/success popup first
                try:
                    # Check for "Application sent" dialog header
                    success_headers = self.driver.find_elements(By.CSS_SELECTOR,
                        ".artdeco-modal__header h2, h2#post-apply-modal, .jpac-modal-header")

                    for header in success_headers:
                        if header.is_displayed() and any(text in header.text.lower() for text in ["application sent", "success", "applied"]):
                            print("Found application sent confirmation dialog")

                            # Look for "Done" button in the modal
                            done_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__actionbar .artdeco-button--primary, #ember554, button[data-test-dialog-primary-btn], .artdeco-button--primary")

                            for btn in done_buttons:
                                if btn.is_displayed() and "done" in btn.text.lower():
                                    print("Clicking 'Done' button on success dialog")
                                    btn.click()
                                    time.sleep(2)
                                    return True

                            # If no Done button found, try to close the modal with the X button
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                ".artdeco-modal__dismiss, button[data-test-modal-close-btn]")

                            for close in close_buttons:
                                if close.is_displayed():
                                    print("Clicking close (X) button on success dialog")
                                    close.click()
                                    time.sleep(2)
                                    return True
                except Exception as e:
                    print(f"Error checking for success dialog: {str(e)}")

                # Check for form errors - if there are errors, we'll handle questions
                has_errors = False
                try:
                    # Only check for errors if not auto-progressing
                    if not auto_progress:
                        error_messages = self.driver.find_elements(By.CSS_SELECTOR, ".artdeco-inline-feedback__message, .fb-dash-form-element-error, [data-test-form-element-error-messages]")
                        has_errors = any(error for error in error_messages if error.text.strip() and "applied" not in error.text.lower())
                        if has_errors:
                            print("Form errors detected - handling additional questions")
                            self.handle_additional_questions()
                            # Validate errors were fixed
                            error_check_result = self.check_for_form_errors()

                            # If errors remain, we'll stop auto-progressing and handle manually
                            if not error_check_result:
                                print("⚠️ Errors remain after handling questions - switching to manual mode")
                                auto_progress = False

                except Exception as e:
                    print(f"Error checking for form errors: {str(e)}")

                # Detect if we're on a question/review screen
                is_question_screen = False
                try:
                    # Only check for question screen if not auto-progressing
                    if not auto_progress:
                        # Check for common question screen indicators
                        question_indicators = [
                            "div[data-test-form-element]",
                            ".jobs-easy-apply-form-section",
                            ".fb-dash-form-element",
                            "input.artdeco-text-input--input:not([value])",  # Empty inputs needing values
                            "select:not(:disabled)",  # Enabled dropdowns
                            "[data-test-text-entity-list-form-component]"  # Forms
                        ]

                        for indicator in question_indicators:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and len([e for e in elements if e.is_displayed()]) > 0:
                                is_question_screen = True
                                break
                except Exception as e:
                    print(f"Error checking for question screen: {str(e)}")

                # Try multiple selectors for next/continue/review/submit buttons
                button_selectors = [
                    "button[aria-label='Review your application']",
                    "button[aria-label='Review']",
                    "button[aria-label='Submit application']",
                    "button[aria-label='Continue to next step']",
                    "button[aria-label='Next']",
                    "button[data-easy-apply-next-button]",
                    "button[aria-label='Submit']",
                    "button[aria-label='Done']",
                    "footer button.artdeco-button--primary",
                    "button.artdeco-button--primary",
                    ".artdeco-modal__footer button.artdeco-button--primary",
                    ".artdeco-modal__actionbar button.artdeco-button--primary",
                    "[data-control-name='continue_unify']",
                    "[data-control-name='submit_unify']",
                    "#ember554"  # Specific ID from your example
                ]

                # Look for a button to click
                button_found = False
                button_is_review = False
                for selector in button_selectors:
                    wait = WebDriverWait(self.driver, 3)
                    try:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            try:
                                if button.is_displayed() and button.is_enabled():
                                    button_text = button.text.strip()
                                    # Check if this is a "review" type button that might need question handling
                                    button_is_review = any(text.lower() in button_text.lower() for text in ["review"])

                                    # If it's a review button or we're on a question screen, handle questions first
                                    # Only if we're not auto-progressing
                                    if (button_is_review or is_question_screen) and not auto_progress:
                                        print(f"Found '{button_text}' button on a form screen - handling questions first")
                                        self.handle_additional_questions()

                                    # Click the button
                                    if any(text.lower() in button_text.lower() for text in ["review", "continue", "next", "submit", "apply", "done"]):
                                        print(f"Clicking button: '{button_text}'")
                                        button.click()
                                        steps_taken += 1

                                        # If we're auto-progressing, increment counter
                                        if auto_progress:
                                            auto_progress_steps_completed += 1
                                            print(f"Auto-progress step {auto_progress_steps_completed} completed")

                                        button_found = True
                                        time.sleep(2)  # Wait for next screen to load

                                        # For buttons with Next or Continue and we're auto-progressing,
                                        # immediately continue without waiting for next iteration
                                        # This allows continuous clicking through auto-progress steps
                                        next_or_continue = any(text.lower() in button_text.lower() for text in ["continue", "next"])
                                        if next_or_continue and auto_progress and auto_progress_steps_completed < self.auto_progress_steps:
                                            print(f"Continuing to auto-progress next step...")
                                            # Don't break or return, let the loop continue immediately
                                            # Reset retry count to ensure we can continue
                                            retry_count = 0
                                            # Skip the rest of the button selectors and continue with next iteration
                                            break  # Just break the inner button loop
                                        break  # Break the inner button loop
                            except StaleElementReferenceException:
                                print("Element became stale, retrying...")
                                continue
                            except Exception as e:
                                print(f"Error clicking button: {str(e)}")

                        if button_found:
                            # Reset retry count on successful button click
                            retry_count = 0
                            break
                    except:
                        continue

                if not button_found:
                    # If no buttons found, we might be done or stuck
                    print("No more buttons found - application may be complete or stuck")

                    # Look for success message or confirmation
                    success_indicators = [
                        ".artdeco-modal__content:contains('Application submitted')",
                        ".artdeco-modal__content:contains('applied')",
                        "h2:contains('Application submitted')",
                        ".artdeco-modal__confirm-dialog-btn",
                        "[data-test-modal-close-btn]",
                        ".jobs-details__main-content",
                        ".jpac-modal-header"  # From your example
                    ]

                    for indicator in success_indicators:
                        try:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                            if elements and any(e.is_displayed() for e in elements):
                                print("Found application success indicator or returned to job details")
                                return True
                        except:
                            continue

                    # If we can't find success indicators or buttons, increment retry count
                    retry_count += 1

                    if retry_count >= max_retries:
                        print(f"Reached maximum retries ({max_retries}) without finding buttons or success indicators")
                        # Try closing any open dialogs before giving up
                        try:
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button[aria-label='Dismiss'], button[aria-label='Close'], .artdeco-modal__dismiss")
                            for close in close_buttons:
                                if close.is_displayed():
                                    close.click()
                                    print("Closed dialog")
                                    time.sleep(1)
                        except:
                            pass
                        return False

                    print(f"No buttons found, retry {retry_count}/{max_retries}")
                    time.sleep(2)

            except Exception as e:
                print(f"Error during application process: {str(e)}")
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Reached maximum retries ({max_retries}) due to errors")
                    return False
                time.sleep(2)

        return steps_taken > 0  # Return True if we managed to take at least one step

    def process_job_card(self, job_card):
        """Process a single job card - click it and check for Easy Apply"""
        try:
            # First, scroll the job card into view to ensure it's visible
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", job_card)
                time.sleep(1)  # Wait for scroll to complete
                print("Scrolled job card into view")
            except Exception as e:
                print(f"Error scrolling to job card: {str(e)}")

            # Extract job info before clicking
            try:
                job_title = job_card.find_element(By.CSS_SELECTOR, ".job-card-list__title").text
            except:
                job_title = "Unknown Job Title"

            try:
                company = job_card.find_element(By.CSS_SELECTOR, ".job-card-container__company-name").text
            except:
                company = "Unknown Company"

            print(f"\nProcessing: {job_title} at {company}")

            # Store job card identifier to handle stale element reference
            try:
                job_id = job_card.get_attribute("data-job-id") or job_card.get_attribute("id")
                print(f"Job card ID: {job_id}")
            except:
                job_id = None

            # Click on the job card to view details - with retry mechanism
            max_click_attempts = 3
            click_success = False

            for attempt in range(max_click_attempts):
                try:
                    # Make sure the element is in view before clicking
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", job_card)
                    time.sleep(0.5)

                    # Try regular click first
                    job_card.click()
                    click_success = True
                    print(f"Clicked job card (attempt {attempt+1})")
                    time.sleep(2)  # Wait for job details to load
                    break
                except StaleElementReferenceException:
                    print(f"Stale element reference on attempt {attempt+1}, refreshing elements")

                    # Try to re-find the job card using its ID
                    if job_id:
                        try:
                            # Re-find the job card
                            refreshed_cards = self.driver.find_elements(By.CSS_SELECTOR, f"[data-job-id='{job_id}'], #{job_id}")
                            if refreshed_cards:
                                job_card = refreshed_cards[0]
                                continue  # Try clicking again with the refreshed element
                        except Exception as e:
                            print(f"Error re-finding job card: {str(e)}")

                    # If couldn't re-find by ID, try JavaScript click
                    try:
                        print("Failed to click job card, trying JavaScript click")
                        self.driver.execute_script("arguments[0].click();", job_card)
                        click_success = True
                        time.sleep(2)
                        break
                    except Exception as js_e:
                        print(f"JavaScript click also failed: {str(js_e)}")
                        time.sleep(1)
                except Exception as e:
                    print(f"Regular click failed (attempt {attempt+1}): {str(e)}")
                    # Try JavaScript click as fallback
                    try:
                        print("Using JavaScript click as fallback")
                        self.driver.execute_script("arguments[0].click();", job_card)
                        click_success = True
                        time.sleep(2)
                        break
                    except Exception as js_e:
                        print(f"JavaScript click also failed: {str(js_e)}")
                        time.sleep(1)

            if not click_success:
                print("❌ Failed to click job card after multiple attempts, skipping")
                self.jobs_skipped += 1
                return False

            # NEW: Check if we've already applied to this job - this check happens before any other processing
            try:
                # Look for the "Applied" indicator
                applied_indicators = self.driver.find_elements(By.CSS_SELECTOR,
                    ".artdeco-inline-feedback--success, .jobs-s-apply .artdeco-inline-feedback__message")

                for indicator in applied_indicators:
                    if "applied" in indicator.text.lower():
                        print(f"⏭️ Already applied to this job: '{indicator.text}' - skipping")
                        self.jobs_skipped += 1
                        return False

                # Alternative check for "See application" link
                see_application_links = self.driver.find_elements(By.CSS_SELECTOR,
                    ".jobs-s-apply__application-link, #jobs-apply-see-application-link")

                if see_application_links and any(link.is_displayed() for link in see_application_links):
                    print("⏭️ Already applied to this job (See application link found) - skipping")
                    self.jobs_skipped += 1
                    return False

                print("No 'Already applied' indicator found - continuing with application process")
            except Exception as e:
                print(f"Error checking if already applied: {str(e)}")

            # Extract detailed job information for advanced filtering
            job_details = self.extract_job_details()

            # Check if we've already applied to this job
            if job_details.get("id") and self.is_job_already_applied(job_details["id"]):
                print("⏭️ Already applied to this job - skipping")
                self.jobs_skipped += 1
                return False

            # Check if the job meets our relevancy criteria
            if not self.check_job_relevancy(job_details):
                print("⏭️ Job doesn't meet relevancy criteria - skipping")
                self.jobs_skipped += 1
                return False

            # Check if Easy Apply button exists
            try:
                # Wait for job details panel to load
                wait = WebDriverWait(self.driver, 5)

                # Check for Easy Apply button (using multiple potential selectors)
                easy_apply_selectors = [
                    ".jobs-apply-button:not([disabled])",
                    "button.jobs-apply-button",
                    "button[data-control-name='jobs_apply_button']",
                    ".jobs-s-apply button"
                ]

                easy_apply_button = None
                for selector in easy_apply_selectors:
                    try:
                        buttons = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            if "Easy Apply" in button.text and button.is_displayed() and button.is_enabled():
                                easy_apply_button = button
                                break
                        if easy_apply_button:
                            break
                    except:
                        continue

                if easy_apply_button:
                    print("✅ Found Easy Apply button")

                    # Click the Easy Apply button
                    try:
                        # Try regular click first
                        try:
                            easy_apply_button.click()
                            print("Clicked Easy Apply button")
                        except Exception as e:
                            print(f"Regular click on Easy Apply failed: {str(e)}")
                            # Try JavaScript click as fallback
                            self.driver.execute_script("arguments[0].click();", easy_apply_button)
                            print("Used JavaScript click for Easy Apply button")

                        time.sleep(2)

                        # Reset step counter for this new application
                        self.current_step = 0

                        # Process the multi-step application
                        if self.proceed_with_application(max_steps=10):
                            print("✅ Application completed successfully")
                            self.jobs_applied += 1

                            # Track job application for analytics and history
                            if job_details.get("id"):
                                self.job_history.add(job_details["id"])

                            # Store job details for analytics
                            job_details["application_date"] = time.strftime("%Y-%m-%d %H:%M")
                            self.applied_jobs_details.append(job_details)

                            # Save application history periodically
                            if len(self.applied_jobs_details) % 5 == 0:
                                try:
                                    self.save_application_history()
                                except Exception as e:
                                    print(f"Error saving application history: {str(e)}")
                        else:
                            print("⚠️ Application process was incomplete or encountered issues")
                            # Still count it as applied if we started the process
                            self.jobs_applied += 1

                        # Close the application modal if it appears
                        try:
                            close_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button[aria-label='Dismiss'], button[aria-label='Close'], .artdeco-modal__dismiss")
                            for close in close_buttons:
                                if close.is_displayed():
                                    close.click()
                                    print("Closed application modal")
                                    time.sleep(1)
                                    break
                        except:
                            pass

                        return True
                    except Exception as e:
                        print(f"Error during application process: {str(e)}")
                else:
                    print("❌ No Easy Apply button found - skipping job")
                    self.jobs_skipped += 1
                    return False

            except Exception as e:
                print(f"Error checking for Easy Apply button: {str(e)}")
                self.jobs_skipped += 1
                return False

        except Exception as e:
            print(f"Error processing job card: {str(e)}")
            self.jobs_skipped += 1
            return False

    def save_application_history(self):
        """Save application history to a JSON file"""
        try:
            history_data = {
                "jobs_applied": self.jobs_applied,
                "jobs_skipped": self.jobs_skipped,
                "applied_jobs": self.applied_jobs_details,
                "job_ids": list(self.job_history)
            }

            with open("application_history.json", "w") as f:
                json.dump(history_data, f, indent=2)

            print(f"Saved application history ({self.jobs_applied} jobs)")
            return True
        except Exception as e:
            print(f"Error saving application history: {str(e)}")
            return False

    def go_to_next_page(self):
        """Navigate to the next page of job search results"""
        print(f"\nNavigating to page {self.current_page + 1}...")

        try:
            # Find the next page button using multiple selectors
            next_button = None
            selectors = [
                f"button[aria-label='Page {self.current_page + 1}']",
                ".artdeco-pagination__button--next",
                ".artdeco-pagination__button.artdeco-pagination__button--next",
                f"li[data-test-pagination-page-btn='{self.current_page + 1}'] button"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements and len(elements) > 0:
                        next_button = elements[0]
                        break
                except:
                    continue

            if not next_button:
                print("Next page button not found - may be on last page")
                return False

            # Check if the button is disabled
            if next_button.get_attribute("disabled") == "true":  # Fixed getAttribute to get_attribute
                print("Next page button is disabled - reached last page")
                return False

            # Click the next page button
            next_button.click()
            print(f"Clicked next page button")
            self.current_page += 1
            time.sleep(3)  # Wait for next page to load

            # Check if navigation was successful
            return True

        except Exception as e:
            print(f"Error navigating to next page: {str(e)}")
            return False

    def run_easy_apply_process(self, search_term="data analyst", location="", max_applications=10):
        """Run the entire Easy Apply process"""
        print(f"\n{'=' * 60}")
        print(" LINKEDIN EASY APPLY AUTOMATION ")
        print(f"{'=' * 60}")

        if not self.setup():
            return False

        if not self.login():
            self.cleanup()
            return False

        if not self.search_jobs(search_term, location):
            self.cleanup()
            return False

        applications_count = 0
        keep_going = True

        print("\nStarting Easy Apply process...")
        try:
            while keep_going and self.current_page <= self.max_pages and applications_count < max_applications:
                print(f"\n{'=' * 40}")
                print(f" PAGE {self.current_page} ")
                print(f"{'=' * 40}")

                # Find and process job cards
                job_cards = self.find_job_cards_on_page()

                # Ensure we process each page completely before moving to the next
                if job_cards:
                    # Process all jobs on current page
                    jobs_processed_on_page = 0
                    for i, job_card in enumerate(job_cards):
                        print(f"Processing job {i+1}/{len(job_cards)} on page {self.current_page}")

                        # Process the job card
                        try:
                            job_processed = self.process_job_card(job_card)
                            jobs_processed_on_page += 1

                            # If we've applied, increment count
                            if job_processed:
                                applications_count += 1
                                print(f"✅ Application {applications_count}/{max_applications} completed")

                                # Take anti-detection measures every 3-5 applications
                                if applications_count % random.randint(3, 5) == 0:
                                    self.avoid_detection()

                                # Break if we've reached max applications
                                if applications_count >= max_applications:
                                    print(f"Reached maximum number of applications ({max_applications})")
                                    keep_going = False
                                    break

                        except StaleElementReferenceException:
                            print("⚠️ Stale element reference - job card may have changed. Continuing with next job.")
                            # Don't break, just continue to next job.
                            continue
                        except Exception as e:
                            print(f"⚠️ Error processing job card: {str(e)}")
                            # Don't break, just continue to next job.
                            continue

                        # Pause between job applications (3-5 seconds)
                        time.sleep(3 + random.random() * 2)

                    print(f"Completed processing {jobs_processed_on_page} jobs on page {self.current_page}")
                else:
                    print(f"No job cards found on page {self.current_page}")

                # Move to next page if we haven't reached max applications and processed all jobs on current page
                if keep_going and applications_count < max_applications:
                    if not self.go_to_next_page():
                        print("No more pages available - end of search results")
                        break

            # Print summary
            print(f"\n{'=' * 60}")
            print(" EASY APPLY PROCESS COMPLETED ")
            print(f"{'=' * 60}")
            print(f"Jobs applied: {self.jobs_applied}")
            print(f"Jobs skipped (no Easy Apply): {self.jobs_skipped}")
            print(f"Pages processed: {self.current_page}")
            print(f"{'=' * 60}")

            # Save application history
            try:
                self.save_application_history()
            except Exception as e:
                print(f"Error saving application history: {str(e)}")

        except Exception as e:
            print(f"Error during Easy Apply process: {str(e)}")
        finally:
            self.cleanup()

        return self.jobs_applied > 0

    def cleanup(self):
        """Close browser and clean up resources"""
        if self.driver:
            print("\nClosing browser...")
            self.driver.quit()

    def detect_application_step(self):
        """NEW: Detect which step of the application process we're currently on"""
        try:
            # Check for progress indicator
            progress_indicators = self.driver.find_elements(By.CSS_SELECTOR,
                ".artdeco-completeness-meter-linear__progress-element, .artdeco-progress-bar__progress")

            # Try to get the current step from the progress meter
            current_step = 0
            total_steps = 0

            if progress_indicators:
                for indicator in progress_indicators:
                    try:
                        # Try to parse step count from aria-valuenow attribute
                        current_step_str = indicator.get_attribute("aria-valuenow")
                        if current_step_str and current_step_str.isdigit():
                            current_step = int(current_step_str)

                        # Try to parse total from aria-valuemax attribute
                        total_steps_str = indicator.get_attribute("aria-valuemax")
                        if total_steps_str and total_steps_str.isdigit():
                            total_steps = int(total_steps_str)

                        if current_step and total_steps:
                            print(f"Detected application progress: Step {current_step}/{total_steps}")
                            return current_step, total_steps
                    except:
                        continue

            # If we couldn't get step from progress bar, try step headers
            step_headers = self.driver.find_elements(By.CSS_SELECTOR,
                ".artdeco-modal__header span, .jobs-easy-apply-modal h3, .artdeco-modal__title")

            if step_headers:
                for header in step_headers:
                    try:
                        header_text = header.text.lower()
                        if "step" in header_text and "of" in header_text:
                            # Parse Step X of Y format
                            step_match = re.search(r'step\s+(\d+)\s+of\s+(\d+)', header_text)
                            if step_match:
                                current_step = int(step_match.group(1))
                                total_steps = int(step_match.group(2))
                                print(f"Detected application progress from header: Step {current_step}/{total_steps}")
                                return current_step, total_steps
                    except:
                        continue

            # Otherwise estimate based on the form content
            form_content = self.driver.find_elements(By.CSS_SELECTOR, ".jobs-easy-apply-form-section__grouping")
            if form_content:
                # First step usually has contact info which is minimal
                if len(form_content) <= 2:
                    print("Estimated application step: 1 (contact info)")
                    return 1, 0  # 0 total means unknown total
                # Review step usually has many sections
                elif len(form_content) >= 5:
                    print("Estimated application step: Review step")
                    return 0, 0  # 0,0 means likely review step
                else:
                    print("Estimated application step: Middle step with questions")
                    return 2, 0  # Middle step

            print("Could not determine current application step")
            return 0, 0  # Unknown

        except Exception as e:
            print(f"Error detecting application step: {str(e)}")
            return 0, 0  # Unknown on error

    def should_auto_progress(self):
        """NEW: Determine if we should automatically progress to the next step without handling questions"""
        try:
            # Check if intelligent skip is enabled
            if not self.use_intelligent_skip:
                print("Intelligent skip is disabled, processing all steps normally")
                return False

            # Get current step
            current_step, total_steps = self.detect_application_step()

            # Store detected step for later use
            self.current_step = current_step

            # If we're in the first steps (usually just contact info), auto-progress
            if 1 <= current_step <= self.auto_progress_steps:
                print(f"🚀 Auto-progressing through step {current_step} without question handling")
                return True

            # Check if this appears to be a simple contact info screen with no questions
            try:
                # Check for indicators of a complex form that would need handling
                complex_form_indicators = [
                    "input[type='text']:not([value])",  # Empty text inputs
                    "select:not(:disabled)",            # Enabled dropdowns
                    "textarea:not([value])",            # Empty textareas
                    "input[type='radio']",              # Radio buttons
                    "input[type='checkbox']",           # Checkboxes
                    ".fb-dash-form-element-error"       # Error messages
                ]

                form_complexity = 0
                for indicator in complex_form_indicators:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                    # Only count visible elements
                    visible_elements = [e for e in elements if e.is_displayed()]
                    form_complexity += len(visible_elements)

                # If form is very simple (just prefilled fields), auto-progress
                if form_complexity <= 1:
                    print("Form appears to be simple with prefilled fields - auto-progressing")
                    return True
            except Exception as e:
                print(f"Error checking form complexity: {str(e)}")

            print(f"Step {current_step}: Manual handling required, not auto-progressing")
            return False

        except Exception as e:
            print(f"Error determining auto-progress status: {str(e)}")
            return False

    def extract_job_details(self):
        """NEW: Extract key details about the current job for analysis and filtering"""
        job_details = {
            "title": "",
            "company": "",
            "location": "",
            "description": "",
            "id": "",
            "date_posted": "",
            "seniority": "",
            "job_functions": []
        }

        try:
            # Extract job title - updated with more comprehensive selectors
            title_selectors = [
                ".jobs-unified-top-card__job-title, .job-details-jobs-unified-top-card__job-title",
                ".t-24.job-details-jobs-unified-top-card__job-title h1",
                "h1.t-24.t-bold.inline",
                ".job-view-layout h1",
                "h1.jobs-unified-top-card__job-title"
            ]

            for selector in title_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].text.strip():
                    job_details["title"] = elements[0].text.strip()
                    print(f"Found job title using selector: {selector}")
                    break

            # Extract company name - updated with more comprehensive selectors
            company_selectors = [
                ".jobs-unified-top-card__company-name, .job-details-jobs-unified-top-card__company-name",
                ".job-details-jobs-unified-top-card__company-name a",
                ".jobs-unified-top-card__subtitle-primary-grouping a",
                ".jobs-top-card__company-url"
            ]

            for selector in company_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements and elements[0].text.strip():
                    job_details["company"] = elements[0].text.strip()
                    print(f"Found company name using selector: {selector}")
                    break

            # Extract location - updated with more comprehensive selectors
            location_selectors = [
                ".jobs-unified-top-card__bullet, .job-details-jobs-unified-top-card__bullet",
                ".job-details-jobs-unified-top-card__primary-description-container .t-black--light span",
                ".jobs-unified-top-card__subtitle-primary-grouping .jobs-unified-top-card__bullet",
                ".jobs-unified-top-card__subtitle-secondary-grouping .jobs-unified-top-card__bullet"
            ]

            for selector in location_selectors:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Try to find the location text within the elements
                    for element in elements:
                        text = element.text.strip()
                        # Skip empty text or text that's likely not location
                        if text and not any(skip in text.lower() for skip in ["ago", "applicant", "remote"]):
                            job_details["location"] = text
                            print(f"Found location: {text}")
                            break
                    if job_details["location"]:
                        break

            # If no specific location found, look for the entire location container
            if not job_details["location"]:
                location_containers = self.driver.find_elements(By.CSS_SELECTOR,
                    ".job-details-jobs-unified-top-card__primary-description-container")

                if location_containers:
                    full_text = location_containers[0].text
                    # Extract location from the full text using regex patterns
                    location_matches = re.search(r'(.*?)·\s+\d+\s+(?:hour|day|week|month)', full_text)
                    if location_matches:
                        job_details["location"] = location_matches.group(1).strip()
                        print(f"Extracted location from container: {job_details['location']}")

            # Extract job ID (for deduplication)
            try:
                current_url = self.driver.current_url
                # Try multiple URL patterns
                job_id_patterns = [
                    r'currentJobId=(\d+)',
                    r'jobs/view/(\d+)',
                    r'jobId=(\d+)'
                ]

                for pattern in job_id_patterns:
                    job_id_match = re.search(pattern, current_url)
                    if job_id_match:
                        job_details["id"] = job_id_match.group(1)
                        print(f"Found job ID: {job_details['id']}")
                        break
            except:
                pass

            # Extract job description
            description_elements = self.driver.find_elements(By.CSS_SELECTOR,
                ".jobs-description, .jobs-unified-description__content, .jobs-description-content__text")
            if description_elements:
                job_details["description"] = description_elements[0].text.strip()

            # Extract job criteria (seniority, job functions, etc.)
            criteria_elements = self.driver.find_elements(By.CSS_SELECTOR, ".description__job-criteria-item")
            for element in criteria_elements:
                try:
                    label_element = element.find_element(By.CSS_SELECTOR, ".description__job-criteria-subheader")
                    value_element = element.find_element(By.CSS_SELECTOR, ".description__job-criteria-text")

                    label = label_element.text.strip().lower()
                    value = value_element.text.strip()

                    if "seniority" in label:
                        job_details["seniority"] = value
                    elif "function" in label:
                        job_details["job_functions"] = [item.strip() for item in value.split(',')]
                    elif "posted" in label or "date" in label:
                        job_details["date_posted"] = value
                except:
                    continue

            # Try to identify job type (full-time, internship, etc.) from UI labels
            try:
                job_type_elements = self.driver.find_elements(By.CSS_SELECTOR,
                    ".ui-label.ui-label--accent-3 span, .job-details-preferences-and-skills__pill .ui-label")

                for element in job_type_elements:
                    text = element.text.strip().lower()
                    if text and any(job_type in text for job_type in ["full-time", "part-time", "contract", "internship"]):
                        job_details["job_type"] = text
                        print(f"Found job type: {text}")
                        break
            except Exception as e:
                print(f"Error extracting job type: {e}")

            # Print found details for debugging
            print(f"Extracted job details: '{job_details['title']}' at '{job_details['company']}'")
            return job_details

        except Exception as e:
            print(f"Error extracting job details: {str(e)}")
            return job_details

    def is_job_already_applied(self, job_id):
        """NEW: Check if we've already applied to this job"""
        if not job_id:
            return False

        # Check our history
        if job_id in self.job_history:
            print(f"Already applied to job ID {job_id}")
            return True

        # Check LinkedIn's UI for "Applied" indicator
        try:
            applied_indicators = [
                ".jobs-s-apply__applied-tag",
                ".jobs-applied-badge",
                "[data-test-applied-date]",
                "[data-control-name='view_application']"
            ]

            for indicator in applied_indicators:
                elements = self.driver.find_elements(By.CSS_SELECTOR, indicator)
                if elements and any(e.is_displayed() for e in elements):
                    print("Found 'Applied' indicator on job")
                    self.job_history.add(job_id)  # Add to history
                    return True
        except:
            pass

        return False

    def check_job_relevancy(self, job_details):
        """NEW: Analyze job details to determine if this job is relevant"""
        if not job_details or not job_details["title"] or not job_details["description"]:
            return True  # Default to accepting if we don't have enough details

        score = 100  # Start with perfect score and deduct points
        red_flags = []

        # Check seniority level - avoid senior positions if configured
        if "seniority" in job_details and job_details["seniority"]:
            seniority = job_details["seniority"].lower()
            if "senior" in seniority or "principal" in seniority or "director" in seniority:
                score -= 15
                red_flags.append(f"Senior position: {job_details['seniority']}")

        # Check for keywords in description that indicate excessive requirements
        description = job_details["description"].lower()
        requirement_indicators = [
            ("years", r'(\d+)\+?\s*(?:years|yrs)', 7),  # Avoid jobs requiring 7+ years experience
            ("phd", r'ph\.?d', 10),  # Avoid jobs requiring PhD
            ("master", r'master[\'s]?|msc|m.sc', 5),  # Small penalty for Master's requirement
        ]

        for keyword, pattern, penalty in requirement_indicators:
            matches = re.findall(pattern, description)
            if matches:
                if keyword == "years" and matches:
                    # Check if any year requirement exceeds our threshold
                    for match in matches:
                        try:
                            years = int(match)
                            if years > 5:  # Threshold for excessive experience
                                score -= penalty
                                red_flags.append(f"Requires {years}+ years experience")
                                break
                        except:
                            pass
                else:
                    score -= penalty
                    red_flags.append(f"Contains {keyword} requirement")

        # Return result with explanation
        if score < self.relevancy_threshold:
            print(f"⚠️ Job relevancy score {score}/100 - below threshold ({self.relevancy_threshold})")
            print(f"Red flags: {', '.join(red_flags)}")
            return False
        else:
            print(f"✅ Job relevancy score {score}/100 - meets threshold ({self.relevancy_threshold})")
            return True

    def human_like_typing(self, element, text, clear_first=True):
        """Type text into an element with random delays between keystrokes to appear more human-like"""
        try:
            if not element or not element.is_displayed() or not element.is_enabled():
                print("Element is not valid, visible or enabled")
                return False

            # First clear the field if requested
            if clear_first:
                try:
                    # Use our centralized clear_input_field utility
                    self.clear_input_field(element)
                except Exception as e:
                    print(f"Error clearing field: {str(e)}")

            # Click on the element to ensure focus
            element.click()
            time.sleep(0.3)

            # NEW APPROACH: First type some random characters and delete them
            # This helps overcome some validation issues by simulating genuine user interaction
            if random.random() > 0.7:  # 30% chance to use this technique
                # Type 2-3 random characters
                random_chars = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(2))
                for char in random_chars:
                    element.send_keys(char)
                    time.sleep(0.1)

                # Delete those characters with backspace
                for _ in range(len(random_chars)):
                    element.send_keys(Keys.BACKSPACE)
                    time.sleep(0.1)

                time.sleep(0.3)  # Brief pause after clearing

            # Type each character with a random delay
            for char in text:
                element.send_keys(char)
                # Random delay between keystrokes (50ms to 200ms)
                time.sleep(0.05 + (random.random() * 0.15))

            # Small pause after typing
            time.sleep(0.5)

            # NEW: Occasionally press a cursor key and then go back to simulate user behavior
            if random.random() > 0.8:  # 20% chance
                element.send_keys(Keys.ARROW_RIGHT)
                time.sleep(0.2)
                element.send_keys(Keys.ARROW_LEFT)
                time.sleep(0.2)

            # Verify if the field actually took our value
            actual_value = element.get_attribute("value")
            if actual_value != text:
                print(f"Field validation may have failed. Expected '{text}', got '{actual_value}'")

                # Try a different approach - use JavaScript to set the value directly
                print("Using JavaScript to set value directly...")
                try:
                    self.driver.execute_script(f"arguments[0].value = '{text}';", element)

                    # Trigger input event to activate validation
                    self.driver.execute_script("""
                        var event = new Event('input', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                    """, element)

                    # Trigger change event to activate validation
                    self.driver.execute_script("""
                        var event = new Event('change', {
                            bubbles: true,
                            cancelable: true,
                        });
                        arguments[0].dispatchEvent(event);
                    """, element)

                    time.sleep(0.5)
                    actual_value = element.get_attribute("value")
                    print(f"After JavaScript injection: '{actual_value}'")
                except Exception as js_error:
                    print(f"JavaScript approach failed: {str(js_error)}")

                if actual_value != text:
                    # Last resort - try character by character with tab focus
                    element.click()
                    time.sleep(0.3)
                    element.clear()
                    time.sleep(0.2)

                    for char in text:
                        element.send_keys(char)
                        time.sleep(0.15)

                    # Tab out and back to trigger validation
                    element.send_keys(Keys.TAB)
                    time.sleep(0.3)
                    element.send_keys(Keys.SHIFT + Keys.TAB)
                    time.sleep(0.3)

            # Tab out to trigger validation
            element.send_keys(Keys.TAB)
            time.sleep(0.5)
            return True

        except Exception as e:
            print(f"Error during human-like typing: {str(e)}")
            # Fallback to direct send_keys if human typing fails
            try:
                element.clear()
                element.send_keys(text)
                element.send_keys(Keys.TAB)
                print("Used fallback typing method")
                return True
            except Exception as fallback_error:
                print(f"Fallback typing also failed: {str(fallback_error)}")
                return False

    def avoid_detection(self):
        """Perform human-like browsing behavior to avoid being detected as automation"""
        print("🔄 Taking anti-detection measures...")
        try:
            # Click the LinkedIn home icon
            home_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a[href='/feed/'], .global-nav__logo, a[data-test-global-nav-item='home']")

            if home_buttons:
                print("Navigating to LinkedIn feed...")
                home_buttons[0].click()
                time.sleep(3)

                # Scroll the feed randomly for a bit
                scroll_count = random.randint(3, 8)
                print(f"Scrolling through feed {scroll_count} times...")

                for i in range(scroll_count):
                    # Random scroll distance
                    scroll_amount = random.randint(300, 800)
                    self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")

                    # Random pause between scrolls
                    pause_time = 1 + (random.random() * 3)
                    time.sleep(pause_time)

                    # Occasionally like a post (10% chance)
                    if random.random() < 0.1:
                        try:
                            # Find like buttons
                            like_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                                "button.react-button__trigger, button[aria-label*='Like']")

                            if like_buttons:
                                # Choose a random like button
                                random_index = random.randint(0, min(len(like_buttons)-1, 5))
                                if random_index < len(like_buttons):
                                    print("Clicking a random like button...")
                                    like_buttons[random_index].click()
                                    time.sleep(1)
                        except:
                            pass

                # Random hovering
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, ".feed-shared-update-v2")
                    if elements:
                        random_element = random.choice(elements)
                        actions = webdriver.ActionChains(self.driver)
                        actions.move_to_element(random_element).perform()
                        time.sleep(1.5)
                except:
                    pass

                print("Returning to job search...")
                self.driver.back()  # Go back to job search
                time.sleep(2)
                return True
            else:
                print("Could not find home button")
                return False

        except Exception as e:
            print(f"Error during anti-detection measures: {str(e)}")
            return False





    def handle_radio_buttons(self, radio_buttons, question_text):
        """Special handler for radio button groups with proper selection"""
        print(f"Handling radio button group: {question_text}")

        try:
            if not radio_buttons or len(radio_buttons) == 0:
                print("No radio buttons provided")
                return False

            # Get the available options text
            radio_options = []
            for radio in radio_buttons:
                # Try to get the text associated with this radio button
                try:
                    # First check label attached to the radio
                    radio_id = radio.get_attribute("id")
                    option_text = ""

                    if radio_id:
                        # Try to find label by "for" attribute
                        label = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                        if label:
                            option_text = label[0].text.strip()

                    # If no label found, get text from parent or grandparent
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", radio)
                        if parent:
                            option_text = parent.text.strip()

                            # If parent has no visible text, try grandparent
                            if not option_text:
                                grandparent = self.driver.execute_script("return arguments[0].parentElement;", parent)
                                if grandparent:
                                    option_text = grandparent.text.strip()

                    radio_options.append((radio, option_text))
                except Exception as e:
                    print(f"Error getting radio option text: {e}")
                    radio_options.append((radio, ""))

            print(f"Radio options: {[opt[1] for opt in radio_options if opt[1]]}")

            # Determine which option to select based on the question
            lower_question = question_text.lower()
            selected_radio = None

            # First check for "Yes" options in yes/no questions
            for radio, option_text in radio_options:
                if option_text.lower() == "yes":
                    selected_radio = radio
                    print(f"Selected 'Yes' option: {option_text}")
                    break

            # If no "Yes" found but question is about relocation, sponsorship, etc.
            # select based on our answer policy
            if not selected_radio:
                keywords = {
                    "relocation": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "sponsorship": {"prefer": ["no", "not", "don't"], "avoid": ["yes", "require"]},
                    "authorized": {"prefer": ["yes", "authorized"], "avoid": ["no", "not"]},
                    "eligible": {"prefer": ["yes", "eligible"], "avoid": ["no", "not"]},
                    "remote": {"prefer": ["yes", "hybrid", "remote"], "avoid": ["no", "on-site"]},
                    "background check": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "start date": {"prefer": ["immediate", "1 week", "asap"], "avoid": ["2 months", "3 months"]},
                    "driving license": {"prefer": ["yes", "have"], "avoid": ["no", "don't"]},
                    "travel": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "drug test": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "covid": {"prefer": ["yes", "vaccinated"], "avoid": ["no", "not"]},
                }

                # Check if question matches any of our keyword categories
                matching_category = None
                for category, patterns in keywords.items():
                    if any(word in lower_question for word in category.split()):
                        matching_category = category
                        print(f"Question matches category: {category}")
                        break

                if matching_category:
                    # First try to find options matching our preferred patterns
                    for radio, option_text in radio_options:
                        lower_option = option_text.lower()
                        if any(prefer in lower_option for prefer in keywords[matching_category]["prefer"]):
                            selected_radio = radio
                            print(f"Selected preferred option for {matching_category}: {option_text}")
                            break

            # If still no selection, use the first radio button
            if not selected_radio and radio_buttons:
                selected_radio = radio_buttons[0]
                print(f"Selected first radio button by default")

            # Click the selected radio button using multiple strategies
            if selected_radio:
                # Scroll to element first to ensure visibility
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", selected_radio)
                time.sleep(0.5)

                # First try regular click
                try:
                    # First attempt: standard click
                    selected_radio.click()
                    print("Radio selected with standard click")
                except Exception as e:
                    print(f"Standard radio click failed: {e}")

                    try:
                        # Second attempt: JavaScript click
                        self.driver.execute_script("arguments[0].click();", selected_radio)
                        print("Radio selected with JavaScript click")
                    except Exception as js_e:
                        print(f"JavaScript radio click failed: {js_e}")

                        try:
                            # Third attempt: use label if available
                            radio_id = selected_radio.get_attribute("id")
                            if radio_id:
                                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                                if labels:
                                    labels[0].click()
                                    print("Radio selected by clicking associated label")
                        except Exception as label_e:
                            print(f"Label click failed: {label_e}")

                            try:
                                # Fourth attempt: try parent click
                                parent = self.driver.execute_script("return arguments[0].parentElement;", selected_radio)
                                if parent:
                                    parent.click()
                                    print("Radio selected by clicking parent element")
                            except Exception as parent_e:
                                print(f"Parent click failed: {parent_e}")

                # Wait briefly after clicking
                time.sleep(0.5)

                # Verify selection by checking if radio is selected
                try:
                    is_selected = selected_radio.is_selected()
                    print(f"Radio button selection verified: {is_selected}")
                    return is_selected
                except:
                    # If we can't verify, assume success since we tried multiple click strategies
                    return True
            else:
                print("No radio button selected")
                return False

        except Exception as e:
            print(f"Error handling radio buttons: {str(e)}")
            return False

    def handle_checkboxes(self, checkboxes, question_text):
        """Special handler for checkbox groups with proper selection"""
        print(f"Handling checkbox group: {question_text}")

        try:
            if not checkboxes or len(checkboxes) == 0:
                print("No checkboxes provided")
                return False

            # Get information about each checkbox
            checkbox_info = []
            for checkbox in checkboxes:
                try:
                    # Try to get label text from associated label
                    checkbox_id = checkbox.get_attribute("id")
                    option_text = ""

                    if checkbox_id:
                        label_elements = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                        if label_elements:
                            option_text = label_elements[0].text.strip()

                    # If no label found, try parent text
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", checkbox)
                        if parent:
                            option_text = parent.text.strip()

                    # If still nothing, try grandparent
                    if not option_text:
                        grandparent = self.driver.execute_script("return arguments[0].parentElement.parentElement;", checkbox)
                        if grandparent:
                            option_text = grandparent.text.strip()

                    # Store checkbox with its text
                    checkbox_info.append((checkbox, option_text))
                except Exception as e:
                    print(f"Error getting checkbox info: {e}")
                    checkbox_info.append((checkbox, ""))

            print(f"Checkbox options: {[info[1] for info in checkbox_info if info[1]]}")

            # Determine which checkboxes to select based on question
            lower_question = question_text.lower()
            selected_any = False

            # Keywords that indicate we should select all checkboxes
            select_all_keywords = ["select all that apply", "check all that apply"]
            should_select_all = any(keyword in lower_question for keyword in select_all_keywords)

            # Keywords for common categories
            skill_keywords = ["skills", "proficient", "familiar with", "experienced in"]
            is_skills_question = any(keyword in lower_question for keyword in skill_keywords)

            # For skills questions, select all checkboxes (common approach)
            if is_skills_question or should_select_all:
                print("Detected skills question or 'select all' - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For agreement/terms checkboxes, select all
            agreement_keywords = ["agree", "terms", "confirm", "acknowledge", "accept"]
            is_agreement = any(keyword in lower_question for keyword in agreement_keywords)

            if is_agreement:
                print("Detected agreement question - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For other checkbox questions, select options based on keywords
            positive_keywords = ["yes", "willing", "able", "agree", "interested"]
            negative_keywords = ["no", "not", "disagree", "unwilling"]

            # Look for positive options
            selected_any = False
            for checkbox, option_text in checkbox_info:
                lower_option = option_text.lower()

                # Skip options that contain negative keywords
                if any(neg in lower_option for neg in negative_keywords):
                    continue

                # Select options with positive keywords
                if any(pos in lower_option for pos in positive_keywords):
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            print(f"Selected positive checkbox: {option_text}")
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

            # If we didn't select anything yet, select the first checkbox by default
            if not selected_any and checkbox_info:
                try:
                    checkbox = checkbox_info[0][0]
                    # Ensure element is visible
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                    time.sleep(0.3)

                    # Check if already selected
                    already_selected = checkbox.is_selected()
                    if not already_selected:
                        # Try regular click
                        try:
                            checkbox.click()
                        except Exception as e:
                            print(f"Regular click failed: {e}")
                            # Try JavaScript click
                            self.driver.execute_script("arguments[0].click();", checkbox)

                        selected_any = True
                        print("Selected first checkbox by default")
                        time.sleep(0.3)
                except Exception as e:
                    print(f"Error selecting default checkbox: {e}")

            return selected_any

        except Exception as e:
            print(f"Error handling checkboxes: {str(e)}")
            return False

    def navigate_to_home(self):
        """Navigate to the LinkedIn home page using multiple fallback methods"""
        print("Attempting to navigate to LinkedIn home page...")

        try:
            # Method 1: Click the LinkedIn logo/home button
            home_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a[href='/feed/'], a.global-nav__logo, a[data-test-global-nav-item='home']")

            if home_buttons and len(home_buttons) > 0:
                try:
                    print("Found LinkedIn home button, clicking...")
                    home_buttons[0].click()
                    time.sleep(2)

                    # Verify we're on the home page
                    if "/feed" in self.driver.current_url:
                        print("Successfully navigated to home page via button")
                        return True
                except Exception as e:
                    print(f"Error clicking home button: {str(e)}")

            # Method 2: Direct URL navigation
            print("Trying direct URL navigation to LinkedIn feed...")
            self.driver.get("https://www.linkedin.com/feed/")
            time.sleep(2)

            # Verify if navigation was successful
            if "/feed" in self.driver.current_url:
                print("Successfully navigated to home page via direct URL")
                return True

            # Method 3: Try LinkedIn main page and then click into feed
            self.driver.get("https://www.linkedin.com")
            time.sleep(2)

            # Look for "Sign in" button which indicates we're logged out
            sign_in_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a.nav__button-secondary, a[data-tracking-control-name='guest_homepage-basic_sign-in-button']")

            if sign_in_buttons and len(sign_in_buttons) > 0:
                print("Detected logged out state, attempting to log in again...")
                return self.login()  # Re-login if we've been logged out

            print("Navigation to home completed")
            return True

        except Exception as e:
            print(f"Error navigating to home: {str(e)}")
            return False

    def handle_input_field(self, input_element, value, field_type="text"):
        """Advanced method to reliably fill any input field with proper validation handling"""
        print(f"Handling {field_type} input field with value: {value}")

        if not input_element:
            print("No input element provided")
            return False

        success = False
        retry_count = 0
        max_retries = 3

        while not success and retry_count < max_retries:
            retry_count += 1
            print(f"Attempt {retry_count} to fill input field")

            try:
                # Ensure element is in view
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                time.sleep(0.3)

                # First focus on element with a click
                try:
                    input_element.click()
                    time.sleep(0.3)
                except Exception as click_error:
                    print(f"Click failed: {str(click_error)}")
                    try:
                        # Use JavaScript click as fallback
                        self.driver.execute_script("arguments[0].click();", input_element)
                        time.sleep(0.3)
                    except:
                        pass

                # APPROACH 1: Clear field using multiple methods for thoroughness
                # 1.1: Standard clear() method
                try:
                    input_element.clear()
                    time.sleep(0.3)
                except:
                    pass

                # 1.2: Use Ctrl+A to select all text and delete
                try:
                    input_element.send_keys(Keys.CONTROL + "a")
                    time.sleep(0.2)
                    input_element.send_keys(Keys.DELETE)
                    time.sleep(0.3)
                except:
                    pass

                # 1.3: Use JavaScript to clear
                try:
                    self.driver.execute_script("arguments[0].value = '';", input_element)
                    time.sleep(0.3)
                except:
                    pass

                # Check if field is actually empty
                current_value = input_element.get_attribute("value")
                if current_value:
                    print(f"Field still contains text after clearing: '{current_value}'")
                    # Additional approach - send backspace keys
                    for _ in range(len(current_value)):
                        try:
                            input_element.send_keys(Keys.BACKSPACE)
                            time.sleep(0.05)
                        except:
                            break

                # APPROACH 2: "Human-like" typing
                # First type 1-2 random characters and delete them (helps with some validation issues)
                rand_chars = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(2))
                for char in rand_chars:
                    try:
                        input_element.send_keys(char)
                        time.sleep(0.1)
                    except:
                        break

                # Delete the random characters
                for _ in range(len(rand_chars)):
                    try:
                        input_element.send_keys(Keys.BACKSPACE)
                        time.sleep(0.1)
                    except:
                        break

                # Now type the actual value
                for char in str(value):
                    try:
                        input_element.send_keys(char)
                        # Random delay between keystrokes (50-150ms)
                        time.sleep(0.05 + (random.random() * 0.1))
                    except Exception as char_error:
                        print(f"Error typing character: {str(char_error)}")
                        break

                # APPROACH 3: If normal typing failed, use direct JavaScript value assignment
                # Check if our value was properly set
                time.sleep(0.5)  # Wait a moment for any validation to occur
                actual_value = input_element.get_attribute("value")
                if actual_value != str(value):
                    print(f"Field validation issue detected. Expected '{value}', got '{actual_value}'")
                    print("Using JavaScript direct assignment instead...")

                    # Use JavaScript to set the value directly
                    try:
                        self.driver.execute_script("arguments[0].value = arguments[1];", input_element, str(value))
                        time.sleep(0.2)

                        # Trigger input events to activate validation
                        self.driver.execute_script("""
                            var event = new Event('input', { bubbles: true, cancelable: true });
                            arguments[0].dispatchEvent(event);

                            var changeEvent = new Event('change', { bubbles: true, cancelable: true });
                            arguments[0].dispatchEvent(changeEvent);

                            var blurEvent = new Event('blur', { bubbles: true, cancelable: true });
                            arguments[0].dispatchEvent(blurEvent);
                        """, input_element)
                        time.sleep(0.5)

                        # Check value after JavaScript
                        actual_value = input_element.get_attribute("value")
                        print(f"After JavaScript injection: value='{actual_value}'")
                    except Exception as js_error:
                        print(f"JavaScript injection error: {str(js_error)}")

                # APPROACH 4: If all else fails, try with React event handling
                if actual_value != str(value):
                    print("Using React-aware event handling...")
                    try:
                        # This special script helps with React-controlled inputs
                        self.driver.execute_script("""
                            var el = arguments[0];
                            var value = arguments[1];

                            // Get property descriptor for value
                            var valueDesc = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value');
                            var reactDesc = Object.getOwnPropertyDescriptor(el, 'value');

                            // If this is a React element with special descriptor
                            if (reactDesc && reactDesc.set) {
                                // Direct property access using React setter
                                reactDesc.set.call(el, value);
                            } else if (valueDesc && valueDesc.set) {
                                // Standard DOM element
                                valueDesc.set.call(el, value);
                            } else {
                                // Fallback
                                el.value = value;
                            }

                            // Trigger events for React to notice
                            el.dispatchEvent(new Event('input', { bubbles: true }));
                            el.dispatchEvent(new Event('change', { bubbles: true }));
                            el.dispatchEvent(new Event('blur', { bubbles: true }));
                        """, input_element, str(value))
                        time.sleep(0.5)
                        actual_value = input_element.get_attribute("value")
                        print(f"After React handling: value='{actual_value}'")
                    except Exception as react_error:
                        print(f"React event handling error: {str(react_error)}")

                # Tab out of the field to trigger validation/blur events
                try:
                    input_element.send_keys(Keys.TAB)
                    time.sleep(0.3)
                except:
                    pass

                # Final value check
                final_value = input_element.get_attribute("value")
                if final_value == str(value):
                    print(f"✅ Input field successfully filled with value: '{final_value}'")
                    success = True
                else:
                    print(f"⚠️ Value mismatch after attempt {retry_count}. Expected: '{value}', Got: '{final_value}'")

                # If it's a different value but not empty, consider it partial success
                if final_value and final_value.strip() and retry_count == max_retries:
                    print(f"⚠️ Using partial success with value: '{final_value}'")
                    success = True

            except Exception as e:
                print(f"Error handling input field (attempt {retry_count}): {str(e)}")

                # Short pause before retry
                time.sleep(1)

                # On last attempt, try the simplest possible approach
                if retry_count == max_retries - 1:
                    try:
                        print("Final attempt with simple approach...")
                        input_element.clear()
                        input_element.send_keys(str(value))
                        time.sleep(0.5)
                        input_element.send_keys(Keys.TAB)
                        time.sleep(0.3)

                        final_value = input_element.get_attribute("value")
                        if final_value:
                            success = True
                    except:
                        pass

        return success

    # --- UTILITY FUNCTIONS FOR FORM HANDLING ---

    def clear_input_field(self, element):
        """Comprehensive method to clear an input field using multiple techniques"""
        if not element:
            print("No element provided to clear")
            return False

        try:
            # Method 1: focus and click
            try:
                element.click()
                time.sleep(0.3)
            except Exception as e:
                print(f"Click before clearing failed: {str(e)}")

            # Method 2: standard clear()
            try:
                element.clear()
                time.sleep(0.3)
            except Exception as e:
                print(f"Standard clear() failed: {str(e)}")

            # Method 3: Ctrl+A and Delete
            try:
                element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.2)
                element.send_keys(Keys.DELETE)
                time.sleep(0.3)
            except Exception as e:
                print(f"Ctrl+A and Delete failed: {str(e)}")

            # Method 4: JavaScript clear
            try:
                self.driver.execute_script("arguments[0].value = '';", element)
                time.sleep(0.3)
            except Exception as e:
                print(f"JavaScript clear failed: {str(e)}")

            return True
        except Exception as e:
            print(f"Error in clear_input_field: {str(e)}")
            return False

    def safe_click(self, element, scroll_first=True):
        """Click an element with fallbacks to ensure it works"""
        if not element:
            print("No element provided to click")
            return False

        try:
            if scroll_first:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.3)

            # Try regular click
            try:
                element.click()
                return True
            except Exception as e:
                print(f"Regular click failed: {str(e)}")

            # Try JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception as js_e:
                print(f"JavaScript click failed: {str(js_e)}")

            return False
        except Exception as e:
            print(f"Error in safe_click: {str(e)}")
            return False

    def get_element_label(self, element):
        """Get the label text for a form element using multiple strategies"""
        if not element:
            return "Unknown"

        try:
            # Try aria-label
            aria_label = element.get_attribute("aria-label")
            if aria_label:
                return aria_label

            # Try associated label by ID
            element_id = element.get_attribute("id")
            if element_id:
                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{element_id}']")
                if labels:
                    return labels[0].text.strip()

            # Try parent element text
            parent = self.driver.execute_script("return arguments[0].parentElement;", element)
            if parent:
                parent_text = parent.text.strip()
                if parent_text:
                    return parent_text.split('\n')[0].strip()

            # Try grandparent
            if parent:
                grandparent = self.driver.execute_script("return arguments[0].parentElement;", parent)
                if grandparent:
                    grandparent_text = grandparent.text.strip()
                    if grandparent_text:
                        return grandparent_text.split('\n')[0].strip()

            return "Unknown"
        except Exception as e:
            print(f"Error finding element label: {str(e)}")
            return "Unknown"

    def classify_question(self, question_text):
        """Centralized method to classify question types and return appropriate values"""
        lower_question = question_text.lower()

        # CTC/Salary questions
        if re.search(r'(?:current|present).*(?:ctc|salary|package|compensation|annual)', lower_question) or re.search(r'(?:ctc|salary|package|compensation|annual).*(?:current|present)', lower_question):
            return {"type": "current_salary", "value": "300000"}

        elif re.search(r'(?:expected|desired).*(?:ctc|salary|package|compensation|annual)', lower_question) or re.search(r'(?:ctc|salary|package|compensation|annual).*(?:expected|desired)', lower_question):
            return {"type": "expected_salary", "value": "900000"}

        elif re.search(r'notice\s*period|serving\s*period|join\s*within|joining\s*time', lower_question):
            return {"type": "notice_period", "value": "15"}

        elif re.search(r'(?:years|year|yrs).*(?:experience|work|professional)|(?:experience|work|professional).*(?:years|year|yrs)', lower_question) or re.search(r'how\s*many\s*years', lower_question):
            return {"type": "experience", "value": "1"}

        elif re.search(r'location.*city|city.*location', lower_question) or lower_question == "location (city)":
            return {"type": "location", "value": "Faridabad, Haryana"}

        elif any(keyword in lower_question for keyword in ["github", "portfolio", "website"]):
            return {"type": "website", "value": "https://github.com/yourusername"}

        elif any(keyword in lower_question for keyword in ["bachelor", "education", "degree"]):
            return {"type": "education", "value": "Yes"}

        elif any(keyword in lower_question for keyword in ["relocate", "relocation"]):
            return {"type": "relocation", "value": "Yes"}

        elif any(keyword in lower_question for keyword in ["authorized", "citizenship", "legally"]):
            return {"type": "citizenship", "value": "Yes, I am authorized to work in this country for any employer"}

        elif any(keyword in lower_question for keyword in ["sponsor", "sponsorship"]):
            return {"type": "sponsorship", "value": "No"}

        elif any(keyword in lower_question for keyword in ["commute", "travel", "remote"]):
            return {"type": "remote", "value": "Yes"}

        # For questions with numeric answers or true/false answers
        if any(word in lower_question for word in ["how many", "how much", "years", "month", "number"]):
            return {"type": "numeric", "value": "1"}

        # Default for yes/no questions
        if "?" in lower_question:
            return {"type": "yes_no", "value": "Yes"}

        return {"type": "other", "value": "Yes"}  # Default to 'Yes' for unknown questions



    def handle_location_dropdown(self, input_element, location_text):
        """Special handler for location fields with dropdown selection"""
        print(f"Handling location dropdown with text: {location_text}")

        try:
            # First clear the field thoroughly
            self.clear_input_field(input_element)

            # Now type the location text deliberately character by character
            input_element.click()
            time.sleep(0.5)

            # Type slowly to ensure the dropdown appears
            for char in location_text:
                input_element.send_keys(char)
                time.sleep(0.15)  # Longer delay for location typing to ensure dropdown appears

            # Wait for dropdown to appear
            time.sleep(2)

            # Look for dropdown suggestions
            suggestion_selectors = [
                ".artdeco-typeahead__result",
                ".jobs-location-typeahead__suggestion",
                ".ember-power-select-option",
                "ul[role='listbox'] li",
                ".autocomplete-results li"
            ]

            # Try to find and click a suggestion
            for selector in suggestion_selectors:
                try:
                    suggestions = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    if suggestions and len(suggestions) > 0:
                        # Click the first suggestion
                        self.safe_click(suggestions[0])
                        print("Selected first location suggestion")
                        time.sleep(0.5)
                        return True
                except:
                    continue

            # If no suggestions found or couldn't click, use keyboard navigation
            print("No suggestions found, using keyboard navigation")
            input_element.send_keys(Keys.DOWN)
            time.sleep(1)
            input_element.send_keys(Keys.DOWN)  # Press down again to ensure selection
            time.sleep(0.5)
            input_element.send_keys(Keys.ENTER)
            time.sleep(1)

            # Verify if a selection was made
            new_value = input_element.get_attribute("value")
            if new_value and new_value.strip():
                print(f"Location set to: {new_value}")
                return True
            else:
                # Last resort - just leave the text as-is
                print("Using typed location value without dropdown selection")
                return True

        except Exception as e:
            print(f"Error handling location dropdown: {str(e)}")
            # Even if there's an error, consider it successful if there's a value
            try:
                value = input_element.get_attribute("value")
                if value and value.strip():
                    print(f"Location field has a value: {value}")
                    return True
            except:
                pass
            return False

    def handle_select_dropdown(self, select_element, question_text):
        """Unified method to handle dropdown selection based on question context"""
        if not select_element:
            print("No select element provided")
            return False

        try:
            # First click to ensure focus
            self.safe_click(select_element, scroll_first=True)
            time.sleep(0.5)

            # Determine if it's a standard select or custom dropdown
            is_standard_select = select_element.tag_name.lower() == 'select'

            if is_standard_select:
                # For standard HTML select elements
                select = Select(select_element)
                options = select_element.find_elements(By.TAG_NAME, "option")
                valid_options = [opt for opt in options if opt.get_attribute("value") and
                               not opt.text.lower() in ["select an option", "please select", "choose"]]

                if not valid_options:
                    print("No valid options found in dropdown")
                    return False

                # Get question classification
                classification = self.classify_question(question_text)
                lower_question = question_text.lower()

                # Select based on question category
                if "proficiency" in lower_question or "english" in lower_question:
                    preferred_options = ["Native", "Bilingual", "Fluent", "Professional", "Full professional"]
                    for preferred in preferred_options:
                        for option in valid_options:
                            if preferred.lower() in option.text.lower():
                                select.select_by_visible_text(option.text)
                                print(f"Selected proficiency: {option.text}")
                                return True
                    # Default to highest (last) option if no match
                    select.select_by_visible_text(valid_options[-1].text)
                    return True

                elif "education" in lower_question or "degree" in lower_question:
                    education_levels = ["Master", "Bachelor", "Graduate", "Post Graduate", "Diploma"]
                    for edu in education_levels:
                        for option in valid_options:
                            if edu.lower() in option.text.lower():
                                select.select_by_visible_text(option.text)
                                print(f"Selected education: {option.text}")
                                return True
                    # Default to first option
                    select.select_by_visible_text(valid_options[0].text)
                    return True

                elif "relocate" in lower_question or "willing" in lower_question or "location" in lower_question:
                    # Find "Yes" option for relocation questions
                    for option in valid_options:
                        if "yes" in option.text.lower():
                            select.select_by_visible_text(option.text)
                            print(f"Selected: {option.text}")
                            return True
                    # Default to first option
                    select.select_by_visible_text(valid_options[0].text)
                    return True

                else:
                    # Default behavior - select first valid option
                    select.select_by_visible_text(valid_options[0].text)
                    print(f"Selected first option: {valid_options[0].text}")
                    return True
            else:
                # For custom/fancy dropdowns that use divs, spans, etc.
                # First click to open the dropdown
                select_element.click()
                time.sleep(1)

                # Look for dropdown items that appear after clicking
                dropdown_items = self.driver.find_elements(By.CSS_SELECTOR,
                    "ul.dropdown-menu li, div[role='listbox'] div[role='option'], .artdeco-dropdown__content li")

                if not dropdown_items:
                    # Try using arrow keys to navigate
                    select_element.send_keys(Keys.DOWN)
                    time.sleep(0.8)
                    select_element.send_keys(Keys.DOWN)  # Press down twice to ensure selection
                    time.sleep(0.8)
                    select_element.send_keys(Keys.ENTER) # Confirm selection
                    time.sleep(0.8)
                    return True

                # Find a suitable option to click
                print(f"Found {len(dropdown_items)} dropdown items")

                # Try to find a meaningful option based on question
                lower_question = question_text.lower()

                if "location" in lower_question:
                    for item in dropdown_items:
                        if any(city in item.text.lower() for city in ["delhi", "faridabad", "haryana", "bengaluru", "bangalore", "kolkata"]):
                            print(f"Clicking location option: {item.text}")
                            item.click()
                            time.sleep(1)
                            return True

                # If no specific match, click the first item
                if dropdown_items:
                    dropdown_items[0].click()
                    print(f"Clicked first dropdown option: {dropdown_items[0].text}")
                    time.sleep(1)
                    return True

                return False

        except Exception as e:
            print(f"Error handling dropdown: {str(e)}")

            # Fallback method - try using keyboard
            try:
                select_element.click()
                time.sleep(0.5)
                select_element.send_keys(Keys.DOWN)
                time.sleep(0.5)
                select_element.send_keys(Keys.ENTER)
                time.sleep(0.5)
                print("Used keyboard fallback for dropdown")
                return True
            except:
                print("Dropdown handling failed completely")
                return False

    def handle_radio_buttons(self, radio_buttons, question_text):
        """Special handler for radio button groups with proper selection"""
        print(f"Handling radio button group: {question_text}")

        try:
            if not radio_buttons or len(radio_buttons) == 0:
                print("No radio buttons provided")
                return False

            # Get the available options text
            radio_options = []
            for radio in radio_buttons:
                # Try to get the text associated with this radio button
                try:
                    # First check label attached to the radio
                    radio_id = radio.get_attribute("id")
                    option_text = ""

                    if radio_id:
                        # Try to find label by "for" attribute
                        label = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                        if label:
                            option_text = label[0].text.strip()

                    # If no label found, get text from parent or grandparent
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", radio)
                        if parent:
                            option_text = parent.text.strip()

                            # If parent has no visible text, try grandparent
                            if not option_text:
                                grandparent = self.driver.execute_script("return arguments[0].parentElement;", parent)
                                if grandparent:
                                    option_text = grandparent.text.strip()

                    radio_options.append((radio, option_text))
                except Exception as e:
                    print(f"Error getting radio option text: {e}")
                    radio_options.append((radio, ""))

            print(f"Radio options: {[opt[1] for opt in radio_options if opt[1]]}")

            # Determine which option to select based on the question
            lower_question = question_text.lower()
            selected_radio = None

            # First check for "Yes" options in yes/no questions
            for radio, option_text in radio_options:
                if option_text.lower() == "yes":
                    selected_radio = radio
                    print(f"Selected 'Yes' option: {option_text}")
                    break

            # If no "Yes" found but question is about relocation, sponsorship, etc.
            # select based on our answer policy
            if not selected_radio:
                keywords = {
                    "relocation": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "sponsorship": {"prefer": ["no", "not", "don't"], "avoid": ["yes", "require"]},
                    "authorized": {"prefer": ["yes", "authorized"], "avoid": ["no", "not"]},
                    "eligible": {"prefer": ["yes", "eligible"], "avoid": ["no", "not"]},
                    "remote": {"prefer": ["yes", "hybrid", "remote"], "avoid": ["no", "on-site"]},
                    "background check": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "start date": {"prefer": ["immediate", "1 week", "asap"], "avoid": ["2 months", "3 months"]},
                    "driving license": {"prefer": ["yes", "have"], "avoid": ["no", "don't"]},
                    "travel": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "drug test": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "covid": {"prefer": ["yes", "vaccinated"], "avoid": ["no", "not"]},
                }

                # Check if question matches any of our keyword categories
                matching_category = None
                for category, patterns in keywords.items():
                    if any(word in lower_question for word in category.split()):
                        matching_category = category
                        print(f"Question matches category: {category}")
                        break

                if matching_category:
                    # First try to find options matching our preferred patterns
                    for radio, option_text in radio_options:
                        lower_option = option_text.lower()
                        if any(prefer in lower_option for prefer in keywords[matching_category]["prefer"]):
                            selected_radio = radio
                            print(f"Selected preferred option for {matching_category}: {option_text}")
                            break

            # If still no selection, use the first radio button
            if not selected_radio and radio_buttons:
                selected_radio = radio_buttons[0]
                print(f"Selected first radio button by default")

            # Click the selected radio button using multiple strategies
            if selected_radio:
                # Scroll to element first to ensure visibility
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", selected_radio)
                time.sleep(0.5)

                # First try regular click
                try:
                    # First attempt: standard click
                    selected_radio.click()
                    print("Radio selected with standard click")
                except Exception as e:
                    print(f"Standard radio click failed: {e}")

                    try:
                        # Second attempt: JavaScript click
                        self.driver.execute_script("arguments[0].click();", selected_radio)
                        print("Radio selected with JavaScript click")
                    except Exception as js_e:
                        print(f"JavaScript radio click failed: {js_e}")

                        try:
                            # Third attempt: use label if available
                            radio_id = selected_radio.get_attribute("id")
                            if radio_id:
                                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                                if labels:
                                    labels[0].click()
                                    print("Radio selected by clicking associated label")
                        except Exception as label_e:
                            print(f"Label click failed: {label_e}")

                            try:
                                # Fourth attempt: try parent click
                                parent = self.driver.execute_script("return arguments[0].parentElement;", selected_radio)
                                if parent:
                                    parent.click()
                                    print("Radio selected by clicking parent element")
                            except Exception as parent_e:
                                print(f"Parent click failed: {parent_e}")

                # Wait briefly after clicking
                time.sleep(0.5)

                # Verify selection by checking if radio is selected
                try:
                    is_selected = selected_radio.is_selected()
                    print(f"Radio button selection verified: {is_selected}")
                    return is_selected
                except:
                    # If we can't verify, assume success since we tried multiple click strategies
                    return True
            else:
                print("No radio button selected")
                return False

        except Exception as e:
            print(f"Error handling radio buttons: {str(e)}")
            return False

    def handle_checkboxes(self, checkboxes, question_text):
        """Special handler for checkbox groups with proper selection"""
        print(f"Handling checkbox group: {question_text}")

        try:
            if not checkboxes or len(checkboxes) == 0:
                print("No checkboxes provided")
                return False

            # Get information about each checkbox
            checkbox_info = []
            for checkbox in checkboxes:
                try:
                    # Try to get label text from associated label
                    checkbox_id = checkbox.get_attribute("id")
                    option_text = ""

                    if checkbox_id:
                        label_elements = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                        if label_elements:
                            option_text = label_elements[0].text.strip()

                    # If no label found, try parent text
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", checkbox)
                        if parent:
                            option_text = parent.text.strip()

                    # If still nothing, try grandparent
                    if not option_text:
                        grandparent = self.driver.execute_script("return arguments[0].parentElement.parentElement;", checkbox)
                        if grandparent:
                            option_text = grandparent.text.strip()

                    # Store checkbox with its text
                    checkbox_info.append((checkbox, option_text))
                except Exception as e:
                    print(f"Error getting checkbox info: {e}")
                    checkbox_info.append((checkbox, ""))

            print(f"Checkbox options: {[info[1] for info in checkbox_info if info[1]]}")

            # Determine which checkboxes to select based on question
            lower_question = question_text.lower()
            selected_any = False

            # Keywords that indicate we should select all checkboxes
            select_all_keywords = ["select all that apply", "check all that apply"]
            should_select_all = any(keyword in lower_question for keyword in select_all_keywords)

            # Keywords for common categories
            skill_keywords = ["skills", "proficient", "familiar with", "experienced in"]
            is_skills_question = any(keyword in lower_question for keyword in skill_keywords)

            # For skills questions, select all checkboxes (common approach)
            if is_skills_question or should_select_all:
                print("Detected skills question or 'select all' - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For agreement/terms checkboxes, select all
            agreement_keywords = ["agree", "terms", "confirm", "acknowledge", "accept"]
            is_agreement = any(keyword in lower_question for keyword in agreement_keywords)

            if is_agreement:
                print("Detected agreement question - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For other checkbox questions, select options based on keywords
            positive_keywords = ["yes", "willing", "able", "agree", "interested"]
            negative_keywords = ["no", "not", "disagree", "unwilling"]

            # Look for positive options
            selected_any = False
            for checkbox, option_text in checkbox_info:
                lower_option = option_text.lower()

                # Skip options that contain negative keywords
                if any(neg in lower_option for neg in negative_keywords):
                    continue

                # Select options with positive keywords
                if any(pos in lower_option for pos in positive_keywords):
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            print(f"Selected positive checkbox: {option_text}")
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

            # If we didn't select anything yet, select the first checkbox by default
            if not selected_any and checkbox_info:
                try:
                    checkbox = checkbox_info[0][0]
                    # Ensure element is visible
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                    time.sleep(0.3)

                    # Check if already selected
                    already_selected = checkbox.is_selected()
                    if not already_selected:
                        # Try regular click
                        try:
                            checkbox.click()
                        except Exception as e:
                            print(f"Regular click failed: {e}")
                            # Try JavaScript click
                            self.driver.execute_script("arguments[0].click();", checkbox)

                        selected_any = True
                        print("Selected first checkbox by default")
                        time.sleep(0.3)
                except Exception as e:
                    print(f"Error selecting default checkbox: {e}")

            return selected_any

        except Exception as e:
            print(f"Error handling checkboxes: {str(e)}")
            return False

    def navigate_to_home(self):
        """Navigate to the LinkedIn home page using multiple fallback methods"""
        print("Attempting to navigate to LinkedIn home page...")

        try:
            # Method 1: Click the LinkedIn logo/home button
            home_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a[href='/feed/'], a.global-nav__logo, a[data-test-global-nav-item='home']")

            if home_buttons and len(home_buttons) > 0:
                try:
                    print("Found LinkedIn home button, clicking...")
                    home_buttons[0].click()
                    time.sleep(2)

                    # Verify we're on the home page
                    if "/feed" in self.driver.current_url:
                        print("Successfully navigated to home page via button")
                        return True
                except Exception as e:
                    print(f"Error clicking home button: {str(e)}")

            # Method 2: Direct URL navigation
            print("Trying direct URL navigation to LinkedIn feed...")
            self.driver.get("https://www.linkedin.com/feed/")
            time.sleep(2)

            # Verify if navigation was successful
            if "/feed" in self.driver.current_url:
                print("Successfully navigated to home page via direct URL")
                return True

            # Method 3: Try LinkedIn main page and then click into feed
            self.driver.get("https://www.linkedin.com")
            time.sleep(2)

            # Look for "Sign in" button which indicates we're logged out
            sign_in_buttons = self.driver.find_elements(By.CSS_SELECTOR,
                "a.nav__button-secondary, a[data-tracking-control-name='guest_homepage-basic_sign-in-button']")

            if sign_in_buttons and len(sign_in_buttons) > 0:
                print("Detected logged out state, attempting to log in again...")
                return self.login()  # Re-login if we've been logged out

            print("Navigation to home completed")
            return True

        except Exception as e:
            print(f"Error navigating to home: {str(e)}")
            return False



    # --- UTILITY FUNCTIONS FOR FORM HANDLING ---

    def clear_input_field(self, element):
        """Comprehensive method to clear an input field using multiple techniques"""
        if not element:
            print("No element provided to clear")
            return False

        try:
            # Method 1: focus and click
            try:
                element.click()
                time.sleep(0.3)
            except Exception as e:
                print(f"Click before clearing failed: {str(e)}")

            # Method 2: standard clear()
            try:
                element.clear()
                time.sleep(0.3)
            except Exception as e:
                print(f"Standard clear() failed: {str(e)}")

            # Method 3: Ctrl+A and Delete
            try:
                element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.2)
                element.send_keys(Keys.DELETE)
                time.sleep(0.3)
            except Exception as e:
                print(f"Ctrl+A and Delete failed: {str(e)}")

            # Method 4: JavaScript clear
            try:
                self.driver.execute_script("arguments[0].value = '';", element)
                time.sleep(0.3)
            except Exception as e:
                print(f"JavaScript clear failed: {str(e)}")

            return True
        except Exception as e:
            print(f"Error in clear_input_field: {str(e)}")
            return False

    def safe_click(self, element, scroll_first=True):
        """Click an element with fallbacks to ensure it works"""
        if not element:
            print("No element provided to click")
            return False

        try:
            if scroll_first:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.3)

            # Try regular click
            try:
                element.click()
                return True
            except Exception as e:
                print(f"Regular click failed: {str(e)}")

            # Try JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception as js_e:
                print(f"JavaScript click failed: {str(js_e)}")

            return False
        except Exception as e:
            print(f"Error in safe_click: {str(e)}")
            return False

    def get_element_label(self, element):
        """Get the label text for a form element using multiple strategies"""
        if not element:
            return "Unknown"

        try:
            # Try aria-label
            aria_label = element.get_attribute("aria-label")
            if aria_label:
                return aria_label

            # Try associated label by ID
            element_id = element.get_attribute("id")
            if element_id:
                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{element_id}']")
                if labels:
                    return labels[0].text.strip()

            # Try parent element text
            parent = self.driver.execute_script("return arguments[0].parentElement;", element)
            if parent:
                parent_text = parent.text.strip()
                if parent_text:
                    return parent_text.split('\n')[0].strip()

            # Try grandparent
            if parent:
                grandparent = self.driver.execute_script("return arguments[0].parentElement;", parent)
                if grandparent:
                    grandparent_text = grandparent.text.strip()
                    if grandparent_text:
                        return grandparent_text.split('\n')[0].strip()

            return "Unknown"
        except Exception as e:
            print(f"Error finding element label: {str(e)}")
            return "Unknown"

    def classify_question(self, question_text):
        """Centralized method to classify question types and return appropriate values"""
        lower_question = question_text.lower()

        # CTC/Salary questions
        if re.search(r'(?:current|present).*(?:ctc|salary|package|compensation|annual)', lower_question) or re.search(r'(?:ctc|salary|package|compensation|annual).*(?:current|present)', lower_question):
            return {"type": "current_salary", "value": "300000"}

        elif re.search(r'(?:expected|desired).*(?:ctc|salary|package|compensation|annual)', lower_question) or re.search(r'(?:ctc|salary|package|compensation|annual).*(?:expected|desired)', lower_question):
            return {"type": "expected_salary", "value": "900000"}

        elif re.search(r'notice\s*period|serving\s*period|join\s*within|joining\s*time', lower_question):
            return {"type": "notice_period", "value": "15"}

        elif re.search(r'(?:years|year|yrs).*(?:experience|work|professional)|(?:experience|work|professional).*(?:years|year|yrs)', lower_question) or re.search(r'how\s*many\s*years', lower_question):
            return {"type": "experience", "value": "1"}

        elif re.search(r'location.*city|city.*location', lower_question) or lower_question == "location (city)":
            return {"type": "location", "value": "Faridabad, Haryana"}

        elif any(keyword in lower_question for keyword in ["github", "portfolio", "website"]):
            return {"type": "website", "value": "https://github.com/yourusername"}

        elif any(keyword in lower_question for keyword in ["bachelor", "education", "degree"]):
            return {"type": "education", "value": "Yes"}

        elif any(keyword in lower_question for keyword in ["relocate", "relocation"]):
            return {"type": "relocation", "value": "Yes"}

        elif any(keyword in lower_question for keyword in ["authorized", "citizenship", "legally"]):
            return {"type": "citizenship", "value": "Yes, I am authorized to work in this country for any employer"}

        elif any(keyword in lower_question for keyword in ["sponsor", "sponsorship"]):
            return {"type": "sponsorship", "value": "No"}

        elif any(keyword in lower_question for keyword in ["commute", "travel", "remote"]):
            return {"type": "remote", "value": "Yes"}

        # For questions with numeric answers or true/false answers
        if any(word in lower_question for word in ["how many", "how much", "years", "month", "number"]):
            return {"type": "numeric", "value": "1"}

        # Default for yes/no questions
        if "?" in lower_question:
            return {"type": "yes_no", "value": "Yes"}

        return {"type": "other", "value": "Yes"}  # Default to 'Yes' for unknown questions

    def fill_input_field(self, input_element, value):
        """Fill an input field with the provided value using robust methods"""
        if not input_element:
            print("No input element provided")
            return False

        success = False
        retry_count = 0
        max_retries = 3

        while not success and retry_count < max_retries:
            retry_count += 1
            print(f"Attempt {retry_count} to fill input field with value: {value}")

            try:
                # First clear the field thoroughly
                self.clear_input_field(input_element)

                # APPROACH 1: Type random characters and delete them first
                if random.random() > 0.5:  # 50% chance to use this technique
                    random_chars = ''.join(random.choice('abcdefghijklmnopqrstuvwxyz') for _ in range(2))
                    for char in random_chars:
                        input_element.send_keys(char)
                        time.sleep(0.1)

                    time.sleep(0.2)

                    # Delete the random characters
                    for _ in range(len(random_chars)):
                        input_element.send_keys(Keys.BACKSPACE)
                        time.sleep(0.1)

                    time.sleep(0.3)

                # APPROACH 2: JavaScript value assignment (most reliable)
                self.driver.execute_script("arguments[0].value = arguments[1];", input_element, str(value))
                time.sleep(0.3)

                # APPROACH 3: Human-like typing with random delays
                input_element.click()  # Re-focus the element
                time.sleep(0.2)

                for char in str(value):
                    try:
                        input_element.send_keys(char)
                        time.sleep(0.05 + (random.random() * 0.1))  # Random delay between keystrokes
                    except:
                        # If sending keys fails, we already set the value with JavaScript
                        pass

                time.sleep(0.3)

                # APPROACH 4: Trigger events to ensure validation occurs
                self.driver.execute_script("""
                    var el = arguments[0];
                    var event = new Event('input', { bubbles: true });
                    el.dispatchEvent(event);

                    var changeEvent = new Event('change', { bubbles: true });
                    el.dispatchEvent(changeEvent);
                """, input_element)

                input_element.send_keys(Keys.TAB)  # Tab out to trigger validation
                time.sleep(0.3)

                # Verify the value was set correctly
                actual_value = input_element.get_attribute("value")
                if actual_value == str(value):
                    print(f"Successfully filled input field with value: {value}")
                    success = True
                    break
                else:
                    print(f"Value mismatch: Expected '{value}', got '{actual_value}'")

                    # APPROACH 5: React-aware JavaScript as final attempt for controlled inputs
                    self.driver.execute_script("""
                        var el = arguments[0];
                        var value = arguments[1];

                        // Try to access React props
                        var key = Object.keys(el).find(k => k.startsWith("__reactProps$"));
                        if (key) {
                            // This is a React element
                            var reactProps = el[key];
                            if (reactProps.onChange) {
                                // Create a synthetic event
                                var event = {target: {value: value}};
                                reactProps.onChange(event);
                            }
                        }

                        // Set value directly and trigger events
                        el.value = value;
                        el.dispatchEvent(new Event('input', { bubbles: true }));
                        el.dispatchEvent(new Event('change', { bubbles: true }));
                        el.dispatchEvent(new Event('blur', { bubbles: true }));
                    """, input_element, str(value))
                    time.sleep(0.3)

                    actual_value = input_element.get_attribute("value")
                    if actual_value == str(value) or actual_value.strip() != "":
                        print(f"Input field filled using React-aware JavaScript: {actual_value}")
                        success = True
                        break
            except Exception as e:
                print(f"Error filling input field (attempt {retry_count}): {str(e)}")
                time.sleep(1)

        # If we failed after all retries but the field has some value, consider it a partial success
        if not success:
            actual_value = input_element.get_attribute("value")
            if actual_value and actual_value.strip():
                print(f"Partial success - field has a non-empty value: {actual_value}")
                return True

        return success

    def handle_location_dropdown(self, input_element, location_text):
        """Special handler for location fields with dropdown selection"""
        print(f"Handling location dropdown with text: {location_text}")

        try:
            # First clear the field thoroughly
            self.clear_input_field(input_element)

            # Now type the location text deliberately character by character
            input_element.click()
            time.sleep(0.5)

            # Type slowly to ensure the dropdown appears
            for char in location_text:
                input_element.send_keys(char)
                time.sleep(0.15)  # Longer delay for location typing to ensure dropdown appears

            # Wait for dropdown to appear
            time.sleep(2)

            # Look for dropdown suggestions
            suggestion_selectors = [
                ".artdeco-typeahead__result",
                ".jobs-location-typeahead__suggestion",
                ".ember-power-select-option",
                "ul[role='listbox'] li",
                ".autocomplete-results li"
            ]

            # Try to find and click a suggestion
            for selector in suggestion_selectors:
                try:
                    suggestions = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, selector))
                    )
                    if suggestions and len(suggestions) > 0:
                        # Click the first suggestion
                        self.safe_click(suggestions[0])
                        print("Selected first location suggestion")
                        time.sleep(0.5)
                        return True
                except:
                    continue

            # If no suggestions found or couldn't click, use keyboard navigation
            print("No suggestions found, using keyboard navigation")
            input_element.send_keys(Keys.DOWN)
            time.sleep(1)
            input_element.send_keys(Keys.DOWN)  # Press down again to ensure selection
            time.sleep(0.5)
            input_element.send_keys(Keys.ENTER)
            time.sleep(1)

            # Verify if a selection was made
            new_value = input_element.get_attribute("value")
            if new_value and new_value.strip():
                print(f"Location set to: {new_value}")
                return True
            else:
                # Last resort - just leave the text as-is
                print("Using typed location value without dropdown selection")
                return True

        except Exception as e:
            print(f"Error handling location dropdown: {str(e)}")
            # Even if there's an error, consider it successful if there's a value
            try:
                value = input_element.get_attribute("value")
                if value and value.strip():
                    print(f"Location field has a value: {value}")
                    return True
            except:
                pass
            return False

    def handle_select_dropdown(self, select_element, question_text):
        """Unified method to handle dropdown selection based on question context"""
        if not select_element:
            print("No select element provided")
            return False

        try:
            # First click to ensure focus
            self.safe_click(select_element, scroll_first=True)
            time.sleep(0.5)

            # Determine if it's a standard select or custom dropdown
            is_standard_select = select_element.tag_name.lower() == 'select'

            if is_standard_select:
                # For standard HTML select elements
                select = Select(select_element)
                options = select_element.find_elements(By.TAG_NAME, "option")
                valid_options = [opt for opt in options if opt.get_attribute("value") and
                               not opt.text.lower() in ["select an option", "please select", "choose"]]

                if not valid_options:
                    print("No valid options found in dropdown")
                    return False

                # Get question classification
                classification = self.classify_question(question_text)
                lower_question = question_text.lower()

                # Select based on question category
                if "proficiency" in lower_question or "english" in lower_question:
                    preferred_options = ["Native", "Bilingual", "Fluent", "Professional", "Full professional"]
                    for preferred in preferred_options:
                        for option in valid_options:
                            if preferred.lower() in option.text.lower():
                                select.select_by_visible_text(option.text)
                                print(f"Selected proficiency: {option.text}")
                                return True
                    # Default to highest (last) option if no match
                    select.select_by_visible_text(valid_options[-1].text)
                    return True

                elif "education" in lower_question or "degree" in lower_question:
                    education_levels = ["Master", "Bachelor", "Graduate", "Post Graduate", "Diploma"]
                    for edu in education_levels:
                        for option in valid_options:
                            if edu.lower() in option.text.lower():
                                select.select_by_visible_text(option.text)
                                print(f"Selected education: {option.text}")
                                return True
                    # Default to first option
                    select.select_by_visible_text(valid_options[0].text)
                    return True

                elif "relocate" in lower_question or "willing" in lower_question or "location" in lower_question:
                    # Find "Yes" option for relocation questions
                    for option in valid_options:
                        if "yes" in option.text.lower():
                            select.select_by_visible_text(option.text)
                            print(f"Selected: {option.text}")
                            return True
                    # Default to first option
                    select.select_by_visible_text(valid_options[0].text)
                    return True

                else:
                    # Default behavior - select first valid option
                    select.select_by_visible_text(valid_options[0].text)
                    print(f"Selected first option: {valid_options[0].text}")
                    return True
            else:
                # For custom/fancy dropdowns that use divs, spans, etc.
                # First click to open the dropdown
                select_element.click()
                time.sleep(1)

                # Look for dropdown items that appear after clicking
                dropdown_items = self.driver.find_elements(By.CSS_SELECTOR,
                    "ul.dropdown-menu li, div[role='listbox'] div[role='option'], .artdeco-dropdown__content li")

                if not dropdown_items:
                    # Try using arrow keys to navigate
                    select_element.send_keys(Keys.DOWN)
                    time.sleep(0.8)
                    select_element.send_keys(Keys.DOWN)  # Press down twice to ensure selection
                    time.sleep(0.8)
                    select_element.send_keys(Keys.ENTER) # Confirm selection
                    time.sleep(0.8)
                    return True

                # Find a suitable option to click
                print(f"Found {len(dropdown_items)} dropdown items")

                # Try to find a meaningful option based on question
                lower_question = question_text.lower()

                if "location" in lower_question:
                    for item in dropdown_items:
                        if any(city in item.text.lower() for city in ["delhi", "faridabad", "haryana", "bengaluru", "bangalore", "kolkata"]):
                            print(f"Clicking location option: {item.text}")
                            item.click()
                            time.sleep(1)
                            return True

                # If no specific match, click the first item
                if dropdown_items:
                    dropdown_items[0].click()
                    print(f"Clicked first dropdown option: {dropdown_items[0].text}")
                    time.sleep(1)
                    return True

                return False

        except Exception as e:
            print(f"Error handling dropdown: {str(e)}")

            # Fallback method - try using keyboard
            try:
                select_element.click()
                time.sleep(0.5)
                select_element.send_keys(Keys.DOWN)
                time.sleep(0.5)
                select_element.send_keys(Keys.ENTER)
                time.sleep(0.5)
                print("Used keyboard fallback for dropdown")
                return True
            except:
                print("Dropdown handling failed completely")
                return False

    def handle_radio_buttons(self, radio_buttons, question_text):
        """Special handler for radio button groups with proper selection"""
        print(f"Handling radio button group: {question_text}")

        try:
            if not radio_buttons or len(radio_buttons) == 0:
                print("No radio buttons provided")
                return False

            # Get the available options text
            radio_options = []
            for radio in radio_buttons:
                # Try to get the text associated with this radio button
                try:
                    # First check label attached to the radio
                    radio_id = radio.get_attribute("id")
                    option_text = ""

                    if radio_id:
                        # Try to find label by "for" attribute
                        label = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                        if label:
                            option_text = label[0].text.strip()

                    # If no label found, get text from parent or grandparent
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", radio)
                        if parent:
                            option_text = parent.text.strip()

                            # If parent has no visible text, try grandparent
                            if not option_text:
                                grandparent = self.driver.execute_script("return arguments[0].parentElement;", parent)
                                if grandparent:
                                    option_text = grandparent.text.strip()

                    radio_options.append((radio, option_text))
                except Exception as e:
                    print(f"Error getting radio option text: {e}")
                    radio_options.append((radio, ""))

            print(f"Radio options: {[opt[1] for opt in radio_options if opt[1]]}")

            # Determine which option to select based on the question
            lower_question = question_text.lower()
            selected_radio = None

            # First check for "Yes" options in yes/no questions
            for radio, option_text in radio_options:
                if option_text.lower() == "yes":
                    selected_radio = radio
                    print(f"Selected 'Yes' option: {option_text}")
                    break

            # If no "Yes" found but question is about relocation, sponsorship, etc.
            # select based on our answer policy
            if not selected_radio:
                keywords = {
                    "relocation": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "sponsorship": {"prefer": ["no", "not", "don't"], "avoid": ["yes", "require"]},
                    "authorized": {"prefer": ["yes", "authorized"], "avoid": ["no", "not"]},
                    "eligible": {"prefer": ["yes", "eligible"], "avoid": ["no", "not"]},
                    "remote": {"prefer": ["yes", "hybrid", "remote"], "avoid": ["no", "on-site"]},
                    "background check": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "start date": {"prefer": ["immediate", "1 week", "asap"], "avoid": ["2 months", "3 months"]},
                    "driving license": {"prefer": ["yes", "have"], "avoid": ["no", "don't"]},
                    "travel": {"prefer": ["yes", "willing", "able"], "avoid": ["no", "not"]},
                    "drug test": {"prefer": ["yes", "agree"], "avoid": ["no", "disagree"]},
                    "covid": {"prefer": ["yes", "vaccinated"], "avoid": ["no", "not"]},
                }

                # Check if question matches any of our keyword categories
                matching_category = None
                for category, patterns in keywords.items():
                    if any(word in lower_question for word in category.split()):
                        matching_category = category
                        print(f"Question matches category: {category}")
                        break

                if matching_category:
                    # First try to find options matching our preferred patterns
                    for radio, option_text in radio_options:
                        lower_option = option_text.lower()
                        if any(prefer in lower_option for prefer in keywords[matching_category]["prefer"]):
                            selected_radio = radio
                            print(f"Selected preferred option for {matching_category}: {option_text}")
                            break

            # If still no selection, use the first radio button
            if not selected_radio and radio_buttons:
                selected_radio = radio_buttons[0]
                print(f"Selected first radio button by default")

            # Click the selected radio button using multiple strategies
            if selected_radio:
                # Scroll to element first to ensure visibility
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", selected_radio)
                time.sleep(0.5)

                # First try regular click
                try:
                    # First attempt: standard click
                    selected_radio.click()
                    print("Radio selected with standard click")
                except Exception as e:
                    print(f"Standard radio click failed: {e}")

                    try:
                        # Second attempt: JavaScript click
                        self.driver.execute_script("arguments[0].click();", selected_radio)
                        print("Radio selected with JavaScript click")
                    except Exception as js_e:
                        print(f"JavaScript radio click failed: {js_e}")

                        try:
                            # Third attempt: use label if available
                            radio_id = selected_radio.get_attribute("id")
                            if radio_id:
                                labels = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{radio_id}']")
                                if labels:
                                    labels[0].click()
                                    print("Radio selected by clicking associated label")
                        except Exception as label_e:
                            print(f"Label click failed: {label_e}")

                            try:
                                # Fourth attempt: try parent click
                                parent = self.driver.execute_script("return arguments[0].parentElement;", selected_radio)
                                if parent:
                                    parent.click()
                                    print("Radio selected by clicking parent element")
                            except Exception as parent_e:
                                print(f"Parent click failed: {parent_e}")

                # Wait briefly after clicking
                time.sleep(0.5)

                # Verify selection by checking if radio is selected
                try:
                    is_selected = selected_radio.is_selected()
                    print(f"Radio button selection verified: {is_selected}")
                    return is_selected
                except:
                    # If we can't verify, assume success since we tried multiple click strategies
                    return True
            else:
                print("No radio button selected")
                return False

        except Exception as e:
            print(f"Error handling radio buttons: {str(e)}")
            return False

    def handle_checkboxes(self, checkboxes, question_text):
        """Special handler for checkbox groups with proper selection"""
        print(f"Handling checkbox group: {question_text}")

        try:
            if not checkboxes or len(checkboxes) == 0:
                print("No checkboxes provided")
                return False

            # Get information about each checkbox
            checkbox_info = []
            for checkbox in checkboxes:
                try:
                    # Try to get label text from associated label
                    checkbox_id = checkbox.get_attribute("id")
                    option_text = ""

                    if checkbox_id:
                        label_elements = self.driver.find_elements(By.CSS_SELECTOR, f"label[for='{checkbox_id}']")
                        if label_elements:
                            option_text = label_elements[0].text.strip()

                    # If no label found, try parent text
                    if not option_text:
                        parent = self.driver.execute_script("return arguments[0].parentElement;", checkbox)
                        if parent:
                            option_text = parent.text.strip()

                    # If still nothing, try grandparent
                    if not option_text:
                        grandparent = self.driver.execute_script("return arguments[0].parentElement.parentElement;", checkbox)
                        if grandparent:
                            option_text = grandparent.text.strip()

                    # Store checkbox with its text
                    checkbox_info.append((checkbox, option_text))
                except Exception as e:
                    print(f"Error getting checkbox info: {e}")
                    checkbox_info.append((checkbox, ""))

            print(f"Checkbox options: {[info[1] for info in checkbox_info if info[1]]}")

            # Determine which checkboxes to select based on question
            lower_question = question_text.lower()
            selected_any = False

            # Keywords that indicate we should select all checkboxes
            select_all_keywords = ["select all that apply", "check all that apply"]
            should_select_all = any(keyword in lower_question for keyword in select_all_keywords)

            # Keywords for common categories
            skill_keywords = ["skills", "proficient", "familiar with", "experienced in"]
            is_skills_question = any(keyword in lower_question for keyword in skill_keywords)

            # For skills questions, select all checkboxes (common approach)
            if is_skills_question or should_select_all:
                print("Detected skills question or 'select all' - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For agreement/terms checkboxes, select all
            agreement_keywords = ["agree", "terms", "confirm", "acknowledge", "accept"]
            is_agreement = any(keyword in lower_question for keyword in agreement_keywords)

            if is_agreement:
                print("Detected agreement question - selecting all checkboxes")
                for checkbox, _ in checkbox_info:
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

                return selected_any

            # For other checkbox questions, select options based on keywords
            positive_keywords = ["yes", "willing", "able", "agree", "interested"]
            negative_keywords = ["no", "not", "disagree", "unwilling"]

            # Look for positive options
            selected_any = False
            for checkbox, option_text in checkbox_info:
                lower_option = option_text.lower()

                # Skip options that contain negative keywords
                if any(neg in lower_option for neg in negative_keywords):
                    continue

                # Select options with positive keywords
                if any(pos in lower_option for pos in positive_keywords):
                    try:
                        # Ensure element is visible
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                        time.sleep(0.3)

                        # Check if already selected
                        already_selected = checkbox.is_selected()
                        if not already_selected:
                            # Try regular click
                            try:
                                checkbox.click()
                            except Exception as e:
                                print(f"Regular click failed: {e}")
                                # Try JavaScript click
                                self.driver.execute_script("arguments[0].click();", checkbox)

                            selected_any = True
                            print(f"Selected positive checkbox: {option_text}")
                            time.sleep(0.3)
                    except Exception as e:
                        print(f"Error selecting checkbox: {e}")

            # If we didn't select anything yet, select the first checkbox by default
            if not selected_any and checkbox_info:
                try:
                    checkbox = checkbox_info[0][0]
                    # Ensure element is visible
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                    time.sleep(0.3)

                    # Check if already selected
                    already_selected = checkbox.is_selected()
                    if not already_selected:
                        # Try regular click
                        try:
                            checkbox.click()
                        except Exception as e:
                            print(f"Regular click failed: {e}")
                            # Try JavaScript click
                            self.driver.execute_script("arguments[0].click();", checkbox)

                        selected_any = True
                        print("Selected first checkbox by default")
                        time.sleep(0.3)
                except Exception as e:
                    print(f"Error selecting default checkbox: {e}")

            return selected_any

        except Exception as e:
            print(f"Error handling checkboxes: {str(e)}")
            return False

def main():
    """Main function to run the LinkedIn Easy Apply automation"""
    try:
        # Get user inputs
        print("\nLinkedIn Easy Apply Automation")
        print("-" * 60)

        # Get search parameters
        search_term = input("\nEnter job search term (default: data analyst): ").strip()
        if not search_term:
            search_term = "data analyst"

        location = input("Enter location (leave blank for all locations): ").strip()

        max_applications = input("Enter maximum number of applications (default: 100): ").strip()
        if not max_applications or not max_applications.isdigit():
            max_applications = 100
        else:
            max_applications = int(max_applications)

        # Get credentials from environment variables or prompt user
        email = "<EMAIL>"
        password = "badshaah"

        if not email or not password:
            email = input("Enter LinkedIn email: ").strip()
            password = input("Enter LinkedIn password: ").strip()

            # Save as environment variables for this session
            os.environ['LINKEDIN_EMAIL'] = email
            os.environ['LINKEDIN_PASSWORD'] = password

        credentials = {'email': email, 'password': password}

        # Run the Easy Apply process
        easy_apply = LinkedInEasyApply(headless=False, credentials=credentials)
        easy_apply.run_easy_apply_process(
            search_term=search_term,
            location=location,
            max_applications=max_applications
        )

    except KeyboardInterrupt:
        print("\n\nProcess interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")


if __name__ == "__main__":
    main()
