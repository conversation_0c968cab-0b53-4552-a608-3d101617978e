import os
import shutil
import tarfile
import zipfile
import argparse
import logging
import sys

def get_logger(log_path):
    """
    Initializes and returns a logger that writes to both a file and the console.
    This is a self-contained logger to avoid dependencies on the main project.
    """
    logger = logging.getLogger('project_recovery_tool')
    logger.setLevel(logging.INFO)

    # Prevent adding multiple handlers if the function is called more than once
    if logger.hasHandlers():
        logger.handlers.clear()

    # File handler
    file_handler = logging.FileHandler(log_path)
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Console handler
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_formatter = logging.Formatter('%(asctime)s - %(message)s')
    stream_handler.setFormatter(stream_formatter)
    logger.addHandler(stream_handler)

    return logger

def restore_from_zip(backup_path, restore_path, logger):
    """Restores the project from a .zip backup."""
    try:
        if not zipfile.is_zipfile(backup_path):
            logger.error(f"Error: '{backup_path}' is not a valid .zip file.")
            return False
        logger.info(f"Starting restore from ZIP backup: '{backup_path}'")
        with zipfile.ZipFile(backup_path, 'r') as zip_ref:
            zip_ref.extractall(restore_path)
        logger.info(f"Successfully restored project to '{restore_path}'")
        return True
    except Exception as e:
        logger.error(f"Failed to restore from ZIP backup: {e}", exc_info=True)
        return False

def restore_from_tar(backup_path, restore_path, logger):
    """Restores the project from a .tar.gz backup."""
    try:
        if not tarfile.is_tarfile(backup_path):
            logger.error(f"Error: '{backup_path}' is not a valid .tar.gz file.")
            return False
        logger.info(f"Starting restore from TAR.GZ backup: '{backup_path}'")
        with tarfile.open(backup_path, 'r:gz') as tar_ref:
            tar_ref.extractall(path=restore_path)
        logger.info(f"Successfully restored project to '{restore_path}'")
        return True
    except Exception as e:
        logger.error(f"Failed to restore from TAR.GZ backup: {e}", exc_info=True)
        return False

def restore_from_mirror(backup_path, restore_path, logger):
    """Restores the project from a mirror directory backup."""
    try:
        if not os.path.isdir(backup_path):
            logger.error(f"Error: Backup path '{backup_path}' is not a valid directory.")
            return False
        logger.info(f"Starting restore from mirror backup: '{backup_path}'")
        if os.path.exists(restore_path):
            logger.warning(f"Restore path '{restore_path}' already exists. For safety, it will be cleared before restoration.")
            shutil.rmtree(restore_path)
        
        # Ensure the restore path exists before copying
        os.makedirs(restore_path)
        
        shutil.copytree(backup_path, restore_path, dirs_exist_ok=True)
        logger.info(f"Successfully restored project to '{restore_path}'")
        return True
    except Exception as e:
        logger.error(f"Failed to restore from mirror backup: {e}", exc_info=True)
        return False

def main():
    parser = argparse.ArgumentParser(
        description="""Project Recovery Tool (v3.0)
This tool helps restore the entire project from a previously created backup.
You can restore from a .zip, .tar.gz, or a mirror directory backup.
The tool is self-contained and should be run from a command line.
""",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("backup_path", help="The full path to the backup file (.zip, .tar.gz) or directory (mirror).")
    parser.add_argument("restore_path", help="The path to the directory where the project will be restored. This directory will be created if it doesn't exist.")
    
    args = parser.parse_args()

    # It's safer to create the restore path if it doesn't exist
    if not os.path.exists(args.restore_path):
        os.makedirs(args.restore_path)

    # Setup a dedicated log file for the recovery process
    log_dir = os.path.join(args.restore_path, 'security', 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    logger = get_logger(os.path.join(log_dir, 'recovery.log'))

    backup_path = os.path.abspath(args.backup_path)
    restore_path = os.path.abspath(args.restore_path)

    logger.info("="*60)
    logger.info("Project Recovery Process Started")
    logger.info(f"Timestamp: {__import__('datetime').datetime.now().isoformat()}")
    logger.info(f"Backup Source: {backup_path}")
    logger.info(f"Restore Destination: {restore_path}")
    logger.info("="*60)

    if not os.path.exists(backup_path):
        logger.error(f"FATAL: Backup source '{backup_path}' not found.")
        print(f"FATAL ERROR: Backup source not found at '{backup_path}'. Please check the path and try again.")
        return

    success = False
    if backup_path.lower().endswith('.zip'):
        success = restore_from_zip(backup_path, restore_path, logger)
    elif backup_path.lower().endswith('.tar.gz'):
        success = restore_from_tar(backup_path, restore_path, logger)
    elif os.path.isdir(backup_path):
        success = restore_from_mirror(backup_path, restore_path, logger)
    else:
        logger.error(f"Unsupported backup format for '{backup_path}'.")
        logger.error("Please provide a path to a .zip, .tar.gz file, or a mirror directory.")

    if success:
        logger.info("Recovery process completed successfully.")
        print("\n" + "="*60)
        print("✅ Recovery process completed successfully.")
        print(f"Project restored to: {restore_path}")
        print(f"A detailed log has been saved to: {os.path.join(log_dir, 'recovery.log')}")
        print("\n" + "-"*60)
        print("⚠️ CRITICAL NEXT STEP: VERIFY INTEGRITY ⚠️")
        print("To ensure the restored project is secure and valid, you must run an integrity check.")
        print("Open a new terminal and run the following commands:")
        print(f"cd {restore_path}")
        print(f"python security\run.py --integrity-check")
        print("="*60)
    else:
        logger.error("Recovery process failed. See logs for details.")
        print("\n" + "="*60)
        print("❌ Recovery process FAILED.")
        print(f"Please check the log file for detailed error information:")
        print(f"{os.path.join(log_dir, 'recovery.log')}")
        print("="*60)


if __name__ == "__main__":
    main()
