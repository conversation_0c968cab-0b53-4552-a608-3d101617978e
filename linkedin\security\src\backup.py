# backup.py: The core logic for the multi-layered project backup system.

import os
import shutil
import zipfile
import tarfile
from datetime import datetime
import ctypes

# --- Helper function for Windows ---
def hide_file_windows(file_path):
    """Sets the 'hidden' attribute for a file on Windows."""
    try:
        # FILE_ATTRIBUTE_HIDDEN = 0x02
        ret = ctypes.windll.kernel32.SetFileAttributesW(file_path, 0x02)
        return ret != 0
    except Exception:
        # This will fail on non-Windows systems, which is expected.
        return False
# --- End Helper ---

def create_zip_backup(source_dir, dest_dir, logger, excluded_dirs, excluded_files):
    """Creates a .zip archive of the project.

    Args:
        source_dir (str): The root directory of the project to back up.
        dest_dir (str): The directory where the backup file will be stored.
        logger: The logger object for recording progress.
        excluded_dirs (set): A set of directory names to exclude.
        excluded_files (set): A set of file names to exclude.
    """
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    zip_filename = os.path.join(dest_dir, f"backup_{timestamp}.zip")
    
    logger.info(f"\n[Method 1/3] Creating universal .zip backup...")
    logger.info(f"--> {zip_filename}")

    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                # Exclude specified directories
                dirs[:] = [d for d in dirs if d not in excluded_dirs]
                
                for file in files:
                    if file in excluded_files:
                        continue
                    
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, source_dir)
                    zipf.write(file_path, arcname)
        
        # Hide the file on Windows after creation
        if os.name == 'nt':
            if hide_file_windows(zip_filename):
                logger.info("--> .zip backup created and hidden successfully.")
            else:
                logger.warning("--> .zip backup created, but failed to hide it.")
        else:
            logger.info("--> .zip backup created successfully.")
        return True
    except Exception as e:
        logger.error(f"[ERROR] Failed to create .zip backup: {e}", exc_info=True)
        return False

def create_targz_backup(source_dir, dest_dir, logger, excluded_dirs, excluded_files):
    """Creates a .tar.gz archive of the project.

    Args:
        source_dir (str): The root directory of the project.
        dest_dir (str): The directory where the backup file will be stored.
        logger: The logger object for recording progress.
        excluded_dirs (set): A set of directory names to exclude.
        excluded_files (set): A set of file names to exclude.
    """
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    tar_filename = os.path.join(dest_dir, f"backup_{timestamp}.tar.gz")
    
    logger.info(f"\n[Method 2/3] Creating developer .tar.gz backup...")
    logger.info(f"--> {tar_filename}")

    try:
        with tarfile.open(tar_filename, "w:gz") as tarf:
            # Custom filter function to exclude items
            def exclude_filter(tarinfo):
                # Check if the item is in an excluded directory
                if any(f"/{d}/" in tarinfo.name or tarinfo.name.startswith(f"{d}/") for d in excluded_dirs):
                    return None
                # Check if the file itself is excluded
                if os.path.basename(tarinfo.name) in excluded_files:
                    return None
                return tarinfo

            tarf.add(source_dir, arcname='.', filter=exclude_filter)

        # Hide the file on Windows after creation
        if os.name == 'nt':
            if hide_file_windows(tar_filename):
                logger.info("--> .tar.gz backup created and hidden successfully.")
            else:
                logger.warning("--> .tar.gz backup created, but failed to hide it.")
        else:
            logger.info("--> .tar.gz backup created successfully.")
        return True
    except Exception as e:
        logger.error(f"[ERROR] Failed to create .tar.gz backup: {e}", exc_info=True)
        return False

def create_mirror_backup(source_dir, dest_dir, logger, excluded_dirs, excluded_files):
    """Creates an uncompressed, mirrored copy of the project.

    Args:
        source_dir (str): The root directory of the project.
        dest_dir (str): The directory where the mirror will be stored.
        logger: The logger object for recording progress.
        excluded_dirs (set): A set of directory names to exclude.
        excluded_files (set): A set of file names to exclude.
    """
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    mirror_path = os.path.join(dest_dir, f"mirror_{timestamp}")
    
    logger.info(f"\n[Method 3/3] Creating instant mirror backup (visible)...")
    logger.info(f"--> {mirror_path}")

    try:
        shutil.copytree(
            source_dir, 
            mirror_path, 
            ignore=shutil.ignore_patterns(*excluded_dirs, *excluded_files)
        )
        logger.info("--> Mirror backup created successfully.")
        return True
    except Exception as e:
        logger.error(f"[ERROR] Failed to create mirror backup: {e}", exc_info=True)
        return False

def run_backup_process(project_root, logger, config):
    """Coordinates the creation of all three backup types based on config.

    Args:
        project_root (str): The absolute path to the project's root directory.
        logger: The logger object for recording progress.
        config: The configuration object.
    """
    backup_dest_dir = os.path.join(project_root, 'backups')
    
    # Load exclusions from config
    excluded_dirs = set(e.strip() for e in config.get('Exclusions', 'excluded_dirs', fallback='').split(','))
    excluded_files = set(e.strip() for e in config.get('Exclusions', 'excluded_files', fallback='').split(','))

    # Ensure the main backups directory exists
    try:
        os.makedirs(backup_dest_dir, exist_ok=True)
        logger.info(f"Backup destination folder is ready: {backup_dest_dir}")
    except OSError as e:
        logger.error(f"[FATAL] Could not create backup directory: {e}", exc_info=True)
        return

    logger.info("="*50)
    logger.info("Starting Full Project Backup Process")
    logger.info(f"Project Root: {project_root}")
    logger.info(f"Backup Destination: {backup_dest_dir}")
    logger.info("="*50)

    # --- Execute Backups ---
    zip_ok = create_zip_backup(project_root, backup_dest_dir, logger, excluded_dirs, excluded_files)
    targz_ok = create_targz_backup(project_root, backup_dest_dir, logger, excluded_dirs, excluded_files)
    mirror_ok = create_mirror_backup(project_root, backup_dest_dir, logger, excluded_dirs, excluded_files)
    # --- End Execute Backups ---

    logger.info("\n" + "="*50)
    logger.info("Backup Process Summary")
    logger.info(f"  .zip Archive (Hidden):    {'SUCCESS' if zip_ok else 'FAILED'}")
    logger.info(f"  .tar.gz Archive (Hidden): {'SUCCESS' if targz_ok else 'FAILED'}")
    logger.info(f"  Mirror Copy (Visible):    {'SUCCESS' if mirror_ok else 'FAILED'}")
    logger.info("="*50)

    # Automatically prune backups if enabled in config
    if config.getboolean('Backup', 'enable_pruning', fallback=False):
        logger.info("\nAutomatic backup pruning is enabled.")
        prune_backups(backup_dest_dir, logger, config)

def prune_backups(backup_dir, logger, config):
    """Deletes old backups based on retention rules in the config."""
    logger.info("--- Starting Backup Pruning Process ---")
    try:
        retention_days = config.getint('Backup', 'retention_days', fallback=30)
        retention_count = config.getint('Backup', 'retention_count', fallback=3)
        now = datetime.now()

        logger.info(f"Retention Policy: Keep backups for {retention_days} days, always keeping the last {retention_count} of each type.")

        # Group backups by type (zip, tar, mirror)
        backups = {'zip': [], 'tar': [], 'mirror': []}
        for item in os.listdir(backup_dir):
            item_path = os.path.join(backup_dir, item)
            try:
                # Extract timestamp from filename
                timestamp_str = item.split('_', 1)[1].rsplit('.', 1)[0]
                if item.endswith('.zip'):
                    backups['zip'].append((datetime.strptime(timestamp_str, "%Y-%m-%d_%H-%M-%S"), item_path))
                elif item.endswith('.tar.gz'):
                    backups['tar'].append((datetime.strptime(timestamp_str, "%Y-%m-%d_%H-%M-%S"), item_path))
                elif item.startswith('mirror_'):
                    backups['mirror'].append((datetime.strptime(timestamp_str, "%Y-%m-%d_%H-%M-%S"), item_path))
            except (IndexError, ValueError):
                logger.warning(f"Could not parse timestamp from '{item}'. Skipping.")
                continue
        
        for backup_type, file_list in backups.items():
            # Sort by date, newest first
            file_list.sort(key=lambda x: x[0], reverse=True)
            
            # Identify which files to delete
            to_delete = []
            # Keep the first 'retention_count' files, regardless of age
            files_to_check = file_list[retention_count:]
            
            for file_date, file_path in files_to_check:
                if (now - file_date).days > retention_days:
                    to_delete.append(file_path)
            
            if not to_delete:
                logger.info(f"No old '{backup_type}' backups to prune.")
            
            for file_path in to_delete:
                try:
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        logger.info(f"Pruned old mirror backup: {os.path.basename(file_path)}")
                    else:
                        os.remove(file_path)
                        logger.info(f"Pruned old archive backup: {os.path.basename(file_path)}")
                except Exception as e:
                    logger.error(f"Failed to delete old backup '{file_path}': {e}")

        logger.info("--- Backup Pruning Complete ---")

    except Exception as e:
        logger.error(f"An error occurred during backup pruning: {e}", exc_info=True)
